<template>
  <Cad3d :ncid="`${route.query.ncid as string}`" />
  <!-- <AttributeDetail /> -->
</template>
<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { usePagesStore } from '@/store/common/usePagesStore'
import { onMounted } from 'vue'
import Cad3d from '@/components/cdp/cad-3d.vue'
import AttributeDetail from '@/components/cdp/cad-detail/attribute-detail.vue'

const route = useRoute()
const router = useRouter()
const pagesStore = usePagesStore()

const ncid: string = route.query.ncid as string
const setPageInfo = () => {
  const title = (route.query.name || '--') as string
  pagesStore.setHeaderToobar({
    showGoBack: true,
    showSetting: true,
    title: [ title ], 
  })
}

const handleNodeClick = (node: any) => {
  console.log('node== 展开属性详情', node)

}

onMounted(() => {
  setPageInfo()
})
</script>