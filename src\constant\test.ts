export const ROLELIST = [
  {
    label: '用户管理',
    value: 1
  },
  {
    label: '应用管理',
    value: 4
  },
  {
    label: '用户组管理',
    value: 2
  },
  {
    label: '镜像管理',
    value: 5
  },
  {
    label: '资源管理',
    value: 6
  },
  {
    label: '角色管理',
    value: 3
  }
]

export const modal1 = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
    }
    //页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onBeforeMount: () => {
      console.log('NeConfigProvider 组件即将挂载')
    },
    onMounted: () => {
      console.log('NeConfigProvider 组件即将挂载')
    }
  },
  apis: {},
  elements: 
}
export const pageData = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
    }
    //页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onBeforeMount: () => {
      console.log('NeConfigProvider 组件即将挂载')
    },
    onMounted: () => {
      console.log('NeConfigProvider 组件即将挂载')
    }
  },
  apis: {},
  elements: [
    {
      id: 'SearchForm_60spo02i5g',//组件唯一身份
      type: 'layout-content',
      name: '搜索表单',
      props: {
        style: { padding: '0 50px' }
      },
      events: {},
      elements: [
        {
          id: 'SearchForm_1',
          type: 'form',
          name: '搜索表单',
          props: {
            model: {
              username: ''
            }
          },
          events: {
            onFinish: (val) => console.log(val, 'onFinish'),
            onFinishFailed: () => console.log('onfinishFailed'),
            onValidate: () => console.log('onValidate'),
            onSubmit: (val) => console.log(val, 'onSubmit'),
            onMounted: (ins) => {
              ins.proxy.customMethod()
            }
          },
          elements: [
            {
              id: 'Input_fb19o54fjh',
              parentId: 'SearchForm_60spo02i5g',
              type: 'form-item',
              name: '文本框',
              props: {
                label: '123',
                name: 'username',
                rules: [{ required: true, message: 'Please input your username!' }],
                style: {}
              },
              elements: [
                {
                  id: 'Input_fb19o54fjh',
                  parentId: 'SearchForm_60spo02i5g',
                  type: 'input',
                  name: '文本框',
                  props: {
                    // defaultValue: '123',
                    value: '12333',
                    placeholder: 'input placeholder',
                    style: {}
                  },
                  slots: { prefix: '前置', suffix: 'suffix', append: 'append' },
                  events: {
                    onMounted: (ins) => {
                      ins.proxy.customMethod()
                    }
                  }
                },
                {
                  id: 'Input_fb19o54fjq',
                  parentId: 'SearchForm_60spo02i5g',
                  type: 'button',
                  name: 'demo',
                  events: {
                    actions: [
                      {
                        nickName: '点击事件',
                        type: 'onClick',
                        action: {
                          id: 'event1',
                          type: 'openModal',
                          name: '弹框',
                          conf: {
                            modelValue: true,
                            title: '弹框标题',
                            width: '50%'
                          },
                          elementModel: modal1
                        }
                      }
                    ]
                  }
                }
              ],
              events: {
                onMounted: (ins) => {
                  ins.proxy.customMethod()
                }
              }
            },
            {
              id: 'Input_fb19o54fjh',
              parentId: 'SearchForm_60spo02i5g',
              type: 'form-item',
              name: '文本框',
              elements: [
                {
                  id: 'Input_fb19o54fjh',
                  parentId: 'SearchForm_60spo02i5g',
                  type: 'row',
                  props: {},
                  name: '文本框',
                  elements: [
                    {
                      id: 'Input_fb19o54fjh',
                      parentId: 'SearchForm_60spo02i5g',
                      type: 'col',
                      name: '文本框',
                      props: { span: 8 },
                      elements: [
                        {
                          id: 'Input_fb19o54fjh',
                          parentId: 'SearchForm_60spo02i5g',
                          type: 'button',
                          name: '文本框',
                          props: {
                            type: 'primary',
                            htmlType: 'submit'
                          },
                          slots: { default: '提交23',loading:'123' },
                          events: {
                            // onClick: () => console.log('按钮点击'),
                          }
                        }
                      ]
                    },
                    {
                      id: 'Input_fb19o54fjh',
                      parentId: 'SearchForm_60spo02i5g',
                      type: 'col',
                      name: '文本框',
                      elements: [
                        {
                          id: 'Input_fb19o54fjh',
                          parentId: 'SearchForm_60spo02i5g',
                          type: 'button',
                          name: '文本框',
                          props: {
                            label: 'submit'
                          },

                          slots: { default: '重置' },

                          events: {
                            // onClick: () => console.log('按钮点击'),
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: 'MarsTable_3gx13gh2ht',
          type: 'pro-table',
          name: '普通表格',
          props: {
            columns: [
              {
                label: 'Name',
                prop: 'name',
                sortable: true
              },
              {
                label: 'Gender',
                prop: 'age',
                filters: [
                  { text: 'Male', value: 'male' },
                  { text: 'Female', value: 'female' }
                ],
                width: '300px',
                sortable: true
              },
              {
                label: 'Email',
                prop: 'email'
              }
            ],
            data: []
          },
          events: {
            onRowClick: (row: any, column: any, event: Event) => {
              console.log(row, column, event)
            },
            onMounted: (ins) => {
              ins.proxy.customMethod()
            }
          }
        },
        {
          id: 'MarsTable_3gx13gh2ht',
          type: 'tree-table',
          name: '普通表格',
          props: {
            columns: [
              {
                type: 'selection'
              },
              {
                label: 'Name',
                prop: 'name',
                sortable: true,
                width: '300px',
                style: 'color:#f00'
              },
              {
                label: 'address',
                prop: 'address',
                width: '300px'
              },
              {
                label: 'date',
                prop: 'date'
              }
            ],
            data: []
          },
          events: {
            onRowClick: (row: any, column: any, event: Event) => {
              console.log(row, column, event)
             


            },
            onMounted: (ins) => {
              console.log('tree-table')
              ins.proxy.customMethod()
            }
          }
        }
      ]
    },
    {
      id: 'Modal_er4mn6jbtk',
      type: 'drawer',
      name: '弹框',
      props: {
        modelValue: true
      },
      elements: [],
      slots: {
        header: '123',
        footer: 'footer',
        default: [
          {
            id: 'MarsTable_3gx13gh2ht',
            type: 'tree-table',
            name: '普通表格',
            props: {
              columns: [
                {
                  type: 'selection'
                },
                {
                  label: 'Name',
                  prop: 'name',
                  sortable: true,
                  width: '300px',
                  style: 'color:#f00'
                },
                {
                  label: 'address',
                  prop: 'address',
                  width: '300px'
                },
                {
                  label: 'date',
                  prop: 'date'
                }
              ],
              data: []
            },
            events: {
              onRowClick: (row: any, column: any, event: Event) => {
                console.log(row, column, event)
              },
              onMounted: (ins) => {
                console.log('tree-table')
                ins.proxy.customMethod()
              }
            }
          }
        ]
      }
    }
  ]

}
