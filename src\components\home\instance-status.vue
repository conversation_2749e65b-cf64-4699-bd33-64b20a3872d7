<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-10-24 14:48:43
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-10-30 15:41:18
 * @FilePath: /neue-cloud-vdi/src/components/home/<USER>
-->
<template>
  <div class="instance-status">
    <div class="row-top">
      <div class="column  column-detail">
        <img src="@/assets/images/home/<USER>" alt="">
      </div>
      <div class="column column-net">
        <div class="item-title">存储空间</div>
        <div class="item-detail">
          <div class="item-detail-item">
            <div class="item-detail-item-title">已使用</div>
            <div class="item-detail-item-value">20.3TB</div>
          </div>
          <div class="item-detail-hr"></div>
          <div class="item-detail-item">
            <div class="item-detail-item-title">未使用</div>
            <div class="item-detail-item-value">79.7TB</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row-bottom">
      <ul>
        <li>
          <div class="item-icon">
            <i class="iconfont icon-shouye-CPU1"></i>
          </div>
          <div class="item-title">CPU使用率</div>
          <div class="item-data">24%</div>
        </li>
        <li>
          <div class="item-icon">
            <i class="iconfont icon-shouye-cunchu"></i>
          </div>
          <div class="item-title">内存使用率</div>
          <div class="item-data">36%</div>
        </li>
        <li>
          <div class="item-icon">
            <i class="iconfont icon-shouye-neicun1"></i>
          </div>
          <div class="item-title">存储使用率</div>
          <div class="item-data">60%</div>
        </li>
      </ul>
    </div>
  </div>
</template>
<style lang="less" scoped>

.instance-status {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-left: 24px;
  padding-right: 24px;

  .row-top {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: row;
    .column {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
      }
    }
    .column-net {
      flex: 1;
    }
    .column-detail {
      width: 50%;
      img {
        width: 75%;
      }
    }

    .item-title {
      height: 22px;
      font-size: @text-1;
      line-height: 22px;
      color: @text-2;
      margin-bottom: 12px;
    }

    .item-detail {
      // flex: 1;
      display: flex;
      flex-direction: row;
      .item-detail-item-title {
        color: @text-color-5;
      }
    }
    .item-detail-hr {
      margin: 0 16px;
      width: 1px;
      height: 34px;
      background: url('@/assets/images/home/<USER>');
    }
  }

  .row-bottom{
    ul {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: row;
    }
    li {
      flex: 1;
      padding: 12px !important;
      background: @primary-1;
      display: flex;
      flex-direction: column;
      align-items: start;
      border-radius: @border-radius-5;
      &:nth-child(n+2) {
        margin-left: 12px;
      }
      .iconfont {
        font-size: @text-3;
        color: @text-color-5;
      }
      .item-title {
        font-size: @text-0;
        text-align: left;
      }
    }
  }

}
</style>
