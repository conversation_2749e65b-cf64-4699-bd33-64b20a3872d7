const api = {
  actionsList: '/cdp/202512/designCollaborationSpaces/actions/list',
  designCollaborationSpace: (designCollaborationSpaceId: string | number) =>
    `/cdp/202512/designCollaborationSpaces/${designCollaborationSpaceId}`,
  designCollaborationSpaces: `/cdp/202512/designCollaborationSpaces`,

  userDCSRelations: `/cdp/202512/userDCSRelations/actions/list`,
  batchCreate: `/cdp/202512/userDCSRelations/actions/batchCreate`,
  userDCSRelationsDel: (userDCSRelationId: string | number) =>
    `/cdp/202512/userDCSRelations/${userDCSRelationId}`,
  plt0UserActionsList: `/businessobject/202512/plt0User/actions/list`,
  projectSpaceList: '/businessobject/202512/plt0ProjectSpace/actions/list',
  designPartList: (classId: string | number) => `/cdp/202512/${classId}/actions/list`,
  designPartDetail: (classId: string | number, designPartId: string | number) =>
    `/cdp/202512/${classId}/${designPartId}`,
  designPartRecursiveGet: (plt0CADBOM: string | number) =>
    `/cdp/202512/${plt0CADBOM}/actions/list`,
  batchGetSignatureUrl: (model: string | number) =>
    `/cdp/202512/${model}/actions/batchGetSignatureUrl`,
  ActivityObjectHistory: (
    classId: string | number,
    ncid: string | number,
    childClass: string | number
  ) => `/cdp/202512/${classId}/${ncid}/${childClass}/actions/list`,
  cADPartInfo: (classId: string | number, CADPartId: string | number) =>
    `/cdp/202512/${classId}/${CADPartId}`,
  recursiveGet: (model: string | number, CADPartId: string | number) =>
    `/cdp/202512/${model}/${CADPartId}/actions/recursiveGet`
}
export default api
