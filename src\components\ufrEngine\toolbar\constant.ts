export interface OperationItem {
  text: string;
  key: string;
  img: string;
  selectedImg?: string;
}

export interface Operation {
  label: string;
  img: string;
  selectedImg?: string;
  des: OperationItem[];
}

export const OperationList: Operation[] = [
	{
		label: 'restore',
		icon: '3D-resert',
		des: [
			{
				text: '还 原',
				icon: '',
				key: 'restore'
			}
		]
	}, {
		label: 'camera',
		icon: '3D-camera',
		des: [
			{
				text: '透视',
				icon: '3D-perspective',
				key: 'perspective'
			}, {
				text: '正交',
				icon: '3D-orthogonality',
				key: 'orthogonality'
			}
		]
	}, {
		label: 'position',
		icon: '3D-view-front',
		des: [
			{
				text: '上',
				icon: '3D-view-up',
				key: 'up'
			}, {
				text: '下',
				icon: '3D-view-down',
				key: 'down'
			},
			{
				text: '左',
				icon: '3D-view-left',
				key: 'left'
			}, {
				text: '右',
				icon: '3D-view-right',
				key: 'right'
			},{
				text: '前',
				icon: '3D-view-back',
				key: 'front'
			}, {
				text: '后',
				icon: '3D-view-front',
				key: 'end'
			}
		]
	}, {
		label: 'zoom',
		icon: '3D-zoom',
		des: [
			{
				text: '窗口缩放',
				icon: '3D-zoom',
				key: 'scale'
			}, {
				text: '窗口适应',
				icon: '3D-fit',
				key: 'adapt'
			}
		]
	}, {
		label: 'display',
		icon: '3D-show-Threading',
		des: [
			{
				text: '线+面',
				icon: '3D-show-Threading',
				key: 'edgeAndFace'
			}, {
				text: '线',
				icon: '3D-show-line',
				key: 'edge'
			},
			{
				text: '面',
				icon: '3D-show-surface',
				key: 'face'
			}
		]
	}, {
		label: '',
		icon: '',
		des: [
			{
				text: '',
				icon: '',
				key: ''
			}]
	}, {
		label: 'measure',
		icon: '3D-measure-obj',
		des: [
			{
				text: '测量对象',
				icon: '3D-measure-obj-select',
				key: 'measureObject'
			}, {
				text: '测量距离',
				icon: '3D-measure-dis-select',
				key: 'measureDistance'
			}
		]
	},
	{
		label: 'sectioning',
		icon: '3D-cut',
		selectIcon: '3D-cut-select',
		des: [
			{
				text: '剖切',
				icon: '',
				key: 'sectioning'
			}
		]
	}, {
		label: 'exploded',
		icon: '3D-boom',
		des: [
			{
				text: '爆炸图',
				icon: '',
				key: 'exploded'
			}
		]
	}, {
		label: 'hotkey',
		icon: '3D-hotkey',
		des: [
			{
				text: '快捷键',
				icon: '',
				key: 'hotkey'
			}
		]
	}
]