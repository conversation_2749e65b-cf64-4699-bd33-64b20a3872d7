const template = `<div  style='display:flex'>
    <el-tag style='margin-right:10px'>{{version}}</el-tag>
    <p>最新版</p>
  </div>`
export const ROLELIST = [
  {
    label: '用户管理',
    value: 1
  },
  {
    label: '应用管理',
    value: 4
  },
  {
    label: '用户组管理',
    value: 2
  },
  {
    label: '镜像管理',
    value: 5
  },
  {
    label: '资源管理',
    value: 6
  },
  {
    label: '角色管理',
    value: 3
  }
]

export enum ROLE {
  OWNER = 'OWNER',
  MEMBER = 'MEMBER'
}
export const roleEnum = {
  [ROLE.OWNER]: { text: '责任人' },
  [ROLE.MEMBER]: { text: '成员' }
}
export const tableSort = {
  ascending: 'asc',
  descending: 'desc'
}

// 获取状态对应的类型
export const StatusEmu: any = {
  INWORK: { text: '工作中', type: 'primary' }
  // PUBLISHED: { text: '已发布', type: 'success' },
  // FROZEN: { text: '冻结', type: 'info' },
  // ABANDONED: { text: '废弃', type: 'danger' }
}
export const PartTypeEmu: any = {
  NEUE_ASM: { text: 'NEUE_ASM', type: 'primary' },
  NEUE_PRT: { text: 'NEUE_PRT', type: 'primary' }
}
export const CheckStateEmu: any = {
  CHECKED_OUT: { text: '已检出', type: 'primary' },
  CHECKED_IN: { text: '已检入', type: 'primary' }
}
export const ChangeTypeEmu: any = {
  UPDATE: { text: '更新', type: 'primary' },
  CREATE: { text: '创建', type: 'primary' },
  DELETE: { text: '删除', type: 'primary' },
  QUERY: { text: '列表查询', type: 'primary' }
}
export const SubcategoryEmu: any = {
  1: { text: '待处理', type: 'warning' },
  2: { text: '已发布', type: 'success' },
  3: { text: '冻结', type: 'info' },
  4: { text: '废弃', type: 'danger' }
}
export const ConvertStatusEmu = {
  INIT: 'INIT',
  RUNNING: 'RUNNING',
  FAIL: 'FAIL',
  SUCCESS: 'SUCCESS'
}

export const ColorList = [
  '#2772FC',
  '#92A6D4',
  '#19BFBF',
  '#3D5495',
  '#7468FF',
  '#22D3EE'
]
