.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multiline-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.main-container .main-content {
  overflow: hidden;
  overflow-y: auto;
  padding: 24px;
  margin-right: 20px;
  border-radius: @border-radius-3;
  background: @bg-color-white;
}
.main-content,
.third-container {
  flex: 1;
}
.container-title {
  height: 32px;
  text-align: left;
  color: @text-color-1;
  font-size: @text-3;
  font-weight: @font-weight-3;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}

.third-container {
  display: flex;
  flex-direction: column;
}
.third-container .third-menu {
  height: 30px;
  line-height: 30px;
}
.icon-tips-success::before {
  color: @success-6;
}

.icon-tips-fail::before {
  color: @danger-6;
}

.icon-tips-ing::before {
  color: @warning-6;
}


