<!--
 * @Author: fangshijie <EMAIL>
 * @Date: 2024-12-11 14:53:08
 * @LastEditors: fangshijie <EMAIL>
 * @LastEditTime: 2025-02-13 17:06:28
 * @FilePath: /neue-cloud-vdi/src/component-design/ny-input/index.vue
-->
<template>
  <el-input class="ny-textarea" 
    v-bind="props" 
    v-model="inputValue" 
    type="textarea"
    :autosize="{ minRows: 5, maxRows: 12 }"
    :show-word-limit="true"
    :maxlength="255"
  >
    <template v-for="(_, name) in $slots" v-slot:[name]>
       <slot :name="name"></slot>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
// import type { InputProps } from 'element-plus';
import  NyTextareaProps  from '.';

const emit = defineEmits(['update:modelValue'])

const props = defineProps<NyTextareaProps>()

const inputValue = computed({
  get: () => props.modelValue,
  set: (val: string | number) => {
    emit('update:modelValue', val)
  }
})
</script>

<style lang="less">
.ny-textarea {
 .el-textarea__inner {
  padding-bottom: 18px;
  }
}
</style>
