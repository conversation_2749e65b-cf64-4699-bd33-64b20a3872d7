# Vite 2.x + Vue 3.x + TypeScript Base

## 技术栈

- 编程语言：[TypeScript 4.x](https://www.typescriptlang.org/zh/) + [JavaScript](https://www.javascript.com/)
- 构建工具：[Vite 2.x](https://cn.vitejs.dev/)
- 前端框架：[Vue 3.x](https://v3.cn.vuejs.org/)
- 路由工具：[Vue Router 4.x](https://next.router.vuejs.org/zh/index.html)
- 状态管理：[Vuex 4.x](https://next.vuex.vuejs.org/)
- UI 框架：[Element Plus](https://element-plus.org/zh-CN/component/overview.html)
- CSS 预编译：[Stylus](https://stylus-lang.com/) / [Sass](https://sass.bootcss.com/documentation) / [Less](http://lesscss.cn/)
- HTTP 工具：[Axios](https://axios-http.com/)
- Git Hook 工具：[husky](https://typicode.github.io/husky/#/) + [lint-staged](https://github.com/okonet/lint-staged)
- 代码规范：[EditorConfig](http://editorconfig.org) + [Prettier](https://prettier.io/) + [ESLint](https://eslint.org/) + [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript#translation)
- 提交规范：[Commitizen](http://commitizen.github.io/cz-cli/) + [Commitlint](https://commitlint.js.org/#/)
## 建议安装VSCode插件
koroFileHeader
Auto Close Tag
TODO Tree

## 快速开始
```sh
yarn
```

### 安装依赖

```sh
yarn add
```

### 启动项目

```sh
yarn dev
```

### 项目打包

```sh
yarn build
```

## Q&A

1. Q: `git cz` 不生效  

   A: 请全局安装 commitizen，命令：`yarn global add commitizen`

2. Q: husky 报错  

   A: 请检查你的项目下是否有 Git 仓库，没有则先 `git init` 初始化一个


## 各部分服务配置&地址
https://e.gitee.com/nuoyuansoft/projects/641453/docs/2488619/file/5765677?sub_id=11660788&scope=undefined&issue_program_id=641453

 | 环境 |服务地址  |  编译指令| 备注|
|---|---|---|---|
| dev |  //dev.cloud.neuetech.cn:9080 | npm run predev 或 yarn predev |原mvp机器|
| mvp |  //api.dev.cloud.neuetech.cn| npm run build 或 yarn build ||
| galaxy |  //api.dev.cloud.neuetech.cn| npm run galaxy 或 yarn galaxy |新对外地址|
## 环境部署与配置
1. 流水线CI脚本在 `/infra/k8s/` 中
2. 配置不同域名（环境）访问的服务地址：`/src/service/base-config.ts`


