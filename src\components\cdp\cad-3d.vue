<template>
  <div class="cad-3d-container">
    <!-- 节点树 -->
    <div class="cad-3d-tree">
        <artifactTree :bomData="data" @nodeClick="handleNodeClick"></artifactTree>
    </div>

    <!-- 模型 -->
    <div class="cad-3d-view"></div>
    <AttributeDetail v-model:visible="attributeDetailVisible" :activeDetail="activeNodeDetail" ></AttributeDetail>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import artifactTree from '@/views/cdp/ArtifactTree/index.vue'
import cdp from '@/service/cdp/index'
// import { UfrMessageChannel } from "neue-ufr-engine"
import AttributeDetail from './cad-detail/attribute-detail.vue'
const props = defineProps({
  ncid: {
    type: String,
    default: ''
  }
})
const data = ref<any>({})

const activeNodeDetail = ref<Object>({})
const attributeDetailVisible = ref<boolean>(false)

const pageParams = ref({
    "fields": null,
    "expands": null,
    "pageParams": {
        "limit": 9999,
        "page": "1",
        "sorts": null
    },
    "condition": {
        "logic": "AND",
        "ignoreCase": true,
        "statements": [
            {
                "logic": "AND",
                "ignoreCase": true,
                "statements": [],
                "field": "source.ncid",
                "value": [],
                "operator": "="
            }
        ]
    }
})

// const ufrMessageChannel = new UfrMessageChannel()


const getCadBomTree = () =>{
  cdp.getDesignArtifact(props.ncid).then((res: any) => {
    if (res.data && res.data.ncid) {
      pageParams.value.condition.statements[0].value = [res.data.ncid]
      getId()
    }
  })
}

const getId = () => {
  cdp.postActionsList('plt0DesignArtifactCADPartRelation',pageParams.value).then((res: any) => {
    if (res.data && res.data.length > 0 && res.data[0].target && res.data[0].target.ncid) {
      console.log('res.data==', res.data);
      getData(res.data[0].target.ncid)
    } 
  }).catch((error: any) => {
    console.error('Error fetching data:', error)
  })
}
const getData = (id:string)=> {
 
  cdp.postGetBomTree({
    model: 'plt0NeueCADPart',
    ncid: id
  }).then((res: any) => {
    if (res.data) {
      data.value = res.data
      console.log('data==', data.value);
      // ufrMessageChannel.getBomTree(data.value)
    }
  }).catch((error: any) => {
    console.error('Error fetching data:', error)
  })
}

const handleNodeClick = (node: any|Object) => {
  console.log('Node clicked:', node);
  // 这里可以添加点击节点后的逻辑
  activeNodeDetail.value = node
  attributeDetailVisible.value = true
}


onMounted(() => {
  getCadBomTree()

})



</script>
<style scoped lang="less">
.cad-3d-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  .cad-3d-tree {
    // width: 350px;
    height: 100%;
    // background-color: #fff; 
  }
  .cad-3d-view {
    flex: 1;
    height: 100%;
    background-color: #f0f0f0;
  }
}
</style>