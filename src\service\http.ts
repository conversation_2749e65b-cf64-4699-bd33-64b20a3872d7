/**
 * <AUTHOR>
 * @description: http请求
 * @date: 2021-06-23 15:58
 * @lastEditors: wangyan-judy
 * @lastEditTime: 2021-06-23 15:58
 * @FilePath: /neue-cloud-vdi/src/service/http.ts
 */
import service from './base'
// import { getCookie } from '@/utils/cookie'
import cookie from 'cookiejs'

/**
 * get请求
 * @param url
 * @param params
 * @param config
 * @returns {Promise}
 */
const get = (url: string, params?: any, config = {}) =>
  service({
    ...config,
    method: 'get',
    url,
    params
  })

/**
 * post请求
 * @param url
 * @param data
 * @param config
 * @returns {Promise}
 */

const post = (url: any, data?: any, config: any = {}) => {
  let params: any = {
    ...config,
    method: 'post',
    url,
    data
  }
  return service(params)
}
/**
 * put请求
 * @param url
 * @param data
 * @param config
 * @returns {Promise}
 */
const put = (url: any, data: any, config: any = {}) =>
  new Promise((resolve, reject) => {
    let params: any = {
      ...config,
      method: 'put', // 指定请求方法为 PUT
      url,
      data
    }

    if (config?.headers?.['Content-Type'] === 'x-www-form-urlencoded') {
      params = {
        ...params,
        params: data
      }
    }

    service(params) // 假设 service 是一个封装好的请求函数
      .then((res) => {
        resolve(res)
      })
      .catch((error) => {
        console.log('put==>')
        reject(error)
      })
  })

/**
 * post上传文件请求
 * @param url
 * @param data
 * @param config
 * @returns {Promise}
 */

const postFile = (url: string, data: any, config: any = {}) =>
  new Promise((resolve, reject) => {
    config.headers = {
      ...config.headers,
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    }
    service({
      ...config,
      method: 'post',
      url,
      data
    })
      .then((res) => {
        resolve(res)
      })
      .catch((error) => {
        console.log('postFile==>', config.headers)
        reject(error)
      })
  })

const downFile = (url: string) => {
  new Promise((resolve, reject) => {
    fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${cookie.get('token')}`
      }
    })
      .then((response) => {
        // 确保服务器响应是文件流
        if (!response.ok) {
          // throw new Error('服务响应不是文件流')
          reject('服务响应不是文件流')
        }
        return response.blob()
      })
      .then((data) => {
        resolve(data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

const deleteReq = (url: string, params?: any, config = {}) => {
  return new Promise((resolve, reject) => {
    service({
      ...config,
      method: 'delete',
      url,
      params
    })
      .then((res) => {
        resolve(res)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

export default {
  get,
  post,
  put,
  postFile,
  downFile,
  deleteReq
}
