<template>
  <Page title="成员管理" class-container="pt-0" :onClose="handleClose">
    <div class="flex justify-between mb-4">
      <el-input
        v-model="input"
        style="width: 240px"
        placeholder="请输入用户名/邮箱/进行模糊搜索"
        :suffix-icon="Search"
      />
      <el-button type="primary" @click="visible = true">
        <ne-icon type="icon-general-user-add mr-1 " /> 添加成员
      </el-button>
    </div>
    <NyTable :columns="columns" :data="listData" @sort-change="handleFilterChange">
      <template #source.ncid="scope">
        <div class="flex">
          <span class="ne-avatar__text" :style="getStyle()">{{
            getLastName(scope.row.source.name)
          }}</span>
          <span>{{ scope.row.source.name }}</span>
          <ElTag v-if="scope.row.role == ROLE.OWNER" class="ml-2" type="primary"
            >本人</ElTag
          >
        </div>
      </template>
      <template #email="scope">
        <span>{{ scope.row.source.email }}</span>
      </template>

      <template #role="scope">
        <span>{{ roleEnum[scope.row.role as ROLE].text || '-' }}</span>
      </template>
    </NyTable>
    <ny-dialog v-model="visible" title="添加成员" :destroy-on-close="true">
      <MemberEdit ref="editRef" :ncid="ncid" />
      <template #footer>
        <div>
          <el-button @click="resetForm()">取消</el-button>
          <el-button type="primary" @click="submitForm()"> 确定 </el-button>
        </div>
      </template>
    </ny-dialog>
  </Page>
</template>

<script setup lang="ts">
import Page from '@/components/layout/page.vue'
import { Search } from '@element-plus/icons-vue'
import { onMounted, ref, watch } from 'vue'
import MemberEdit from './components/member-edit.vue'
import actions from '@/service/actions'
import { roleEnum, tableSort } from '@/constant'
import { NyTable } from '@/component-design'
import { getRandomColor, transformLastChar } from '@neue/neue-plus/es/utils/lib'
import { ROLE } from '@/constant'
import { keys, values } from 'lodash-es'

// 定义组件 props
const props = defineProps<{
  ncid: string
}>()
let filterSorts = ref()
let input = ref()
let visible = ref(false)
let editRef = ref()
let columns = ref([
  {
    prop: 'source.ncid',
    label: '成员',
    sortable: true
  },
  {
    prop: 'email',
    label: '邮箱',
    sortable: true
  },
  {
    prop: 'role',
    label: '空间角色'
  }
])

const emit = defineEmits(['onClose'])
let handleClose = () => {
  emit('onClose')
  return false
}
let getLastName = (name: any) => {
  return transformLastChar(name || '')
}
let getStyle = () => {
  return {
    backgroundColor: getRandomColor()
  }
}
const listData = ref([])
let rowId = ref()
// 获取空间成员列表
let postUserDCSRelationsFn = (spaceId: string) => {
  if (!spaceId) {
    return
  }
  actions
    .postUserDCSRelations({
      pageParams: {
        limit: 99999,
        page: 0,
        sorts: keys(filterSorts.value).reduce((pre: any, item: any) => {
          let sortOrder = tableSort[filterSorts.value[item] as keyof typeof tableSort]
          return (
            sortOrder &&
            pre.concat({
              sortBy: item,
              sortOrder: sortOrder
            })
          )
        }, [])
      },
      condition: {
        logic: 'AND',
        ignoreCase: true,
        statements: [
          {
            field: 'target.ncid',
            value: [spaceId],
            operator: '='
          }
        ]
      }
    })
    .then((res) => {
      listData.value = res.data.filter((item: any) => {
        return (
          !input.value ||
          item.source.name.includes(input.value) ||
          item.source.email.includes(input.value)
        )
      })
    })
    .catch((error) => {})
}
let submitForm = async () => {
  let val = editRef.value.getData()
  try {
    await actions.postBatchCreate(
      val.map((item: any) => ({
        source: { ncid: item.ncid },
        target: { ncid: props.ncid }, // 使用当前空间的 ncid
        role: ROLE.MEMBER
      }))
    )
    postUserDCSRelationsFn(props.ncid)
    visible.value = false
  } catch (error) {}
}

let resetForm = () => {
  visible.value = false
}

let handleFilterChange = (val: any) => {
  filterSorts.value = {
    ...filterSorts.value,
    [val.prop]: val.order
  }
}
watch(filterSorts, () => {
  postUserDCSRelationsFn(props.ncid)
})
// 监听搜索输入变化
watch(input, () => {
  if (props.ncid) {
    postUserDCSRelationsFn(props.ncid)
  }
})

// 监听 ncid 变化
watch(
  () => props.ncid,
  (newNcid, oldNcid) => {
    if (newNcid && newNcid !== oldNcid) {
      postUserDCSRelationsFn(newNcid)
    }
  },
  { immediate: true }
)

onMounted(() => {})
</script>

<style lang="scss">
.ne-avatar__text {
  margin-right: 8px;
  color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
