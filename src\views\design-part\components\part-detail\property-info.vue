<template>
  <div class="part-detail">
    <div
      class="text-[16px] font-[600] text-[#0F172A] mb-6 h-[48px] flex items-center bg-[#F0F3FA] pl-[20px]"
    >
      <i class="iconfont icon-caret-down mr-2"></i>
      基础属性
    </div>
    <div class="grid grid-cols-2 gap-x-6 gap-y-4 text-[14px]">
      <div
        v-for="item in itemList.slice(0, -6)"
        :key="item.label"
        class="flex items-center"
      >
        <div class="w-[140px] text-right mr-2 text-[#7F8C9F]">{{ item.label }}</div>
        <div
          v-if="item.type !== 'tag'"
          class="rounded-[8px] bg-[#F7F8FA] h-[32px] leading-[32px] px-3 flex-1 text-[#1E293B]"
        >
          {{ item.value }}
        </div>
        <div v-else>
          <el-tag
            type="primary"
            effect="light"
            style="
              --el-tag-text-color: #1856eb;
              --el-tag-border-color: #8fb1ff;
              --el-tag-bg-color: #edf2fc;
            "
            >{{ item.value }}</el-tag
          >
        </div>
      </div>
    </div>
    <div
      class="my-6 text-[16px] font-[600] text-[#0F172A] mb-6 h-[48px] flex items-center bg-[#F0F3FA] pl-[20px]"
    >
      <i class="iconfont icon-caret-down mr-2"></i>
      零件自身属性
    </div>
    <div class="grid grid-cols-2 gap-x-6 gap-y-4 text-[14px]">
      <div
        v-for="item in itemList.slice(-6)"
        :key="item.label"
        class="flex items-center"
      >
        <div class="w-[140px] text-right mr-2 text-[#7F8C9F]">{{ item.label }}</div>
        <div
          class="rounded-[8px] bg-[#F7F8FA] h-[32px] leading-[32px] px-3 flex-1 text-[#1E293B]"
        >
          {{ item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onMounted, ref } from '@vue/runtime-core'
import { StatusEmu, CheckStateEmu } from '@/constant'
import actions from '@/service/actions'
import { Plt0NeueCADPartEntity } from '../..'
import dayjs from 'dayjs'

// 定义组件属性
const props = defineProps({
  partId: {
    type: [String, Number],
    required: true
  }
})
let { partId } = props
const getStatusText = (status: string | undefined): string => {
  return StatusEmu[status as keyof typeof StatusEmu]?.text || String(status)
}
const getCheckStateText = (status: string | undefined): string => {
  return CheckStateEmu[status as keyof typeof CheckStateEmu]?.text || String(status)
}
let partData = ref<Plt0NeueCADPartEntity>()
let itemList = computed(() => [
  {
    label: '零部件编号',
    value: partData.value?.partNo
  },
  {
    label: '零部件名称',
    value: partData.value?.partName
  },
  {
    label: '版本',
    value: partData.value?.version
  },
  {
    label: '状态',
    value: getStatusText(partData.value?.status),
    type: 'tag'
  },
  {
    label: '创建人',
    value: partData.value?.createdBy.name
  },
  {
    label: '创建时间',
    value: dayjs(partData.value?.createdAt).format('YYYY-MM-DD HH:mm:ss')
  },
  {
    label: '修改人',
    value: partData.value?.modifiedBy.name || '-'
  },
  {
    label: '修改时间',
    value: dayjs(partData.value?.modifiedAt).format('YYYY-MM-DD HH:mm:ss')
  },
  {
    label: '检出人',
    value: partData.value?.checkedOutBy?.name || '-'
  },
  {
    label: '检出状态',
    value: getCheckStateText(partData.value?.checkState),
    type: 'tag'
  },
  {
    label: '责任人',
    value: partData.value?.owner.name
  },
  {
    label: '描述',
    value: partData.value?.partDescription || '-'
  },
  {
    label: '实体曲面面积 (mm²)',
    value: partData.value?.actualSurfaceArea || '-'
  },
  {
    label: '质量(kg)',
    value: partData.value?.mass
  },
  {
    label: '开放曲面面积 (mm²)',
    value: partData.value?.openSurfaceArea
  },
  {
    label: '材料',
    value: partData.value?.material
  },
  {
    label: '体积 (mm³)',
    value: partData.value?.volume
  },
  {
    label: '重心',
    value: partData.value?.gravityCenter
  }
])
let getInitData = async () => {
  let res = await actions.getDesignPartDetail({
    classId: 'plt0NeueCADPart',
    designPartId: partId
    // expands: [
    //   {
    //     referenceField: 'checkedOutBy'
    //   }
    // ]
  })
  partData.value = res.data
}
onMounted(() => {
  getInitData()
})
</script>

<style lang="scss"></style>
