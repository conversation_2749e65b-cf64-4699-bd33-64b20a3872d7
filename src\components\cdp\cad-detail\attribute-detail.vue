<template>
  <el-drawer
    v-model="props.visible"
    title="抽屉"
    :modal="false"
    direction="rtl"
    :append-to-body="true"
    class="custom-drawer"
    :before-close="handleClose"
  >
    <template #header="{ close, titleId, titleClass }">
      <div :id="titleId" :class="`custom-title ${titleClass}`">
        <CaxIcon :type="detail.type" iconSize="16px" textSize="14px" />
        <span class="custom-title-text">{{ detail.partNo }}</span>
      </div>
      <div class="custom-close" @click="handleClose">
        <svg-icon name="icon-close" />
      </div>
    </template>
    <div class="drawer-content">
      <!-- 抽屉内容 -->
      <el-menu
        :default-active="'1'"
        class="el-menu-demo"
        mode="horizontal"
      >
        <el-menu-item index="1">属性信息</el-menu-item>
      </el-menu>
      <div class="content-detail">
        <el-collapse v-model="activeNames" class="custom-collapse">
          <el-collapse-item name="1">
            <template #title="{ isActive }">
              <div :class="['title-wrapper', { 'is-active': isActive }]">
                基础属性
              </div>
            </template>
            <div class="collapse-content">
              <div class="content-row">
                <div class="content-row-left">零件编号</div>
                <div class="content-row-right custom-title">
                  <CaxIcon :type="detail.type" iconSize="16px" textSize="14px" />
                  <span class="custom-title-text">{{ detail.partNo }}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">零件名称</div>
                <div class="content-row-right">{{ detail.partName }}</div>
              </div>
              <div class="content-row">
                <div class="content-row-left">版本</div>
                <div class="content-row-right">{{ detail.version }}</div>
              </div>
              <div class="content-row">
                <div class="content-row-left">状态</div>
                <div class="content-row-right">
                  <el-tag type="primary" effect="plain">工作中</el-tag>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">责任人</div>
                <div class="content-row-right">
                  <ne-avatar v-if="detail.owner && detail.owner.name" :name="detail.owner.name"/>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">创建人</div>
                <div class="content-row-right">
                  <ne-avatar v-if="detail.createdBy && detail.createdBy.name" :name="detail.createdBy.name"/>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">创建时间</div>
                <div class="content-row-right">{{ dayjs(detail.createdAt).format('YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
              <div class="content-row">
                <div class="content-row-left">修改人</div>
                <div class="content-row-right">
                  <ne-avatar v-if="detail.modifiedBy && detail.modifiedBy.name" :name="detail.modifiedBy.name"/>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">修改时间</div>
                <div class="content-row-right">{{ dayjs(detail.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
              <div class="content-row">
                <div class="content-row-left">检出状态</div>
                <div class="content-row-right">
                  <el-tag type="primary" effect="plain">他人检出</el-tag>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">检出人</div>
                <div class="content-row-right">
                  <ne-avatar v-if="detail.checkedOutBy && detail.checkedOutBy.name" :name="detail.checkedOutBy.name"/>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">描述</div>
                <div class="content-row-right">{{ detail.partDescription || '-' }}</div>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template #title="{ isActive }">
              <div :class="['title-wrapper', { 'is-active': isActive }]">
                零件自身属性
              </div>
            </template>
            <div class="collapse-content">
              <div class="content-row">
                <div class="content-row-left">质量</div>
                <div class="content-row-right">
                  <span>{{ detail.mass }}</span>
                  <span class="content-row-unit">(kg)</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">材料</div>
                <div class="content-row-right">{{ detail.material }}</div>
              </div>
              <div class="content-row">
                <div class="content-row-left">重心</div>
                <div class="content-row-right">{{ detail.gravityCenter }}</div>
              </div>
              <div class="content-row">
                <div class="content-row-left">实体曲面面积</div>
                <div class="content-row-right">
                  <span>{{ detail.solidSurfaceArea }}</span>
                  <span class="content-row-unit">(mm²)</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">开放区面面积</div>
                <div class="content-row-right">
                  <span>{{ detail.openSurfaceArea }}</span>
                  <span class="content-row-unit">(mm²)</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-row-left">体积</div>
                <div class="content-row-right">
                  <span>{{ detail.volume }}</span>
                  <span class="content-row-unit">(mm³)</span>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { defineProps, defineEmits, ref, computed } from 'vue'
import CaxIcon from '../../common/cax-icon.vue'
import { watch } from 'fs'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  activeDetail: {
    type: Object,
    default: {}
  }
})
const emit = defineEmits(['update:visible'])

const handleClose = () => {
  emit('update:visible', false)
}
// const drawerVisible = ref(true)
const activeNames = ref(['1', '2'])
const detail = computed(() => {
  return props.activeDetail
})


</script>
<style lang="less">
@import url("//at.alicdn.com/t/c/font_4913835_aexsq5x2ok6.css");
.custom-drawer.rtl {
  /* 设置抽屉高度为 300px */
  height: calc(100vh - 68px)!important;
  bottom: 0!important;
  top: auto!important;
  .icon-tree-assembly {
    color: #6C499D;
  }
  
  .custom-title {
    display: flex;
  }
  .custom-title-text {
    margin-left: 4px;
  }
  .custom-collapse {
    margin-top: 16px;
    .el-collapse-item {
      border: 0 none;

    }
    .el-collapse-item__header {
      display: flex;
      justify-content: flex-start; /* 确保箭头在左侧 */
      align-items: center;
      border: 0 none;
      height: 24px;
      background: @primary-2;
      padding: 0 2px;
      border-radius: @border-radius-2;
    }
    .el-collapse-item__wrap {
      border-bottom: 0 none;
    }
    .title-wrapper {
      order: 1;
    }
    .el-collapse-item__arrow {
      order: -1;
      margin-left: 0px;
      font-family: "iconfont" !important;
      font-size: 16px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: rgba(0, 0, 0, 0.85);
      &:before {
        content: "\e724";
      }
    }
    .collapse-content {
      .content-row{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        line-height: 22px;
        font-size: @text-1;
        margin-top: 16px;
        .ne-avatar-circle {
          background-color: @link-6 !important;
        }
      }
      .content-row-left {
        width: 88px;
        color: @text-color-5;
      }
      .content-row-right {
        display: flex;
        flex: 1;
        justify-content: flex-end;
        color: @text-color-1;
        .content-row-unit {
          color: @text-color-5;
          margin-left: 4px;
        }
      }
      .ne-avatar-wrapper {
        justify-content: end !important;
      }
    }
  }
  .el-menu {
    height: 44px;
    .el-menu-item {
      padding: 0;
      height: 100%;
      font-size: @text-1!important;
    }
    .el-menu-item.is-active,
    .el-menu--horizontal>.el-menu-item.is-active {
      border-radius: 0 !important;
      border-bottom-color:@link-6 !important;
      color: @link-6 !important;
    }

  }
}
</style>