<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-25 17:03:03
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-10-31 14:51:56
 * @FilePath: /neue-cloud-vdi/src/components/home/<USER>
-->
<template>
   <el-aside width="33.3%">
    <div class="aside-info">HI,欢迎来到</div>
    <div class="aside-desc">诺源研发云！</div>
    <div class="aside-title">消息通知</div>
    <div class="aside-content">
      <img src="@/assets/images/home/<USER>" width="86" />
      <span class="aside-content-text">暂无消息</span>
    </div>
  </el-aside>
</template>

<script lang="ts" setup></script>

<style scoped lang="less">
.el-aside {
  padding: 24px;
  margin-bottom: 20px;
  background: #fff;
  height: 100%;
  color: @text-color-1;
  text-align: left;
  display: flex;
  flex-direction: column;
  .aside-info {
    font-size: @text-4;
    font-weight: @font-weight-2;
  }
  .aside-desc {
    font-size: @text-4;
    font-weight: @font-weight-2;
  }
  .aside-title {
    margin-top: 18px;
    margin-bottom: 8px;
    font-size: @text-1;
    font-weight: @font-weight-2;
  }
  .aside-content{
    flex: 1;
    width: 100%;
    flex-direction: column;
    border-radius: @border-radius-2;
    background-color: @bg-color-0;
    display: flex;
    justify-content: center;
    align-items: center;
    .aside-content-text {
      display: inline-block;
      padding-top: 12px;
      font-size: @text-1;
      color: @text-color-6;
      font-weight: @font-weight-3;
    }
  }
}
</style>
