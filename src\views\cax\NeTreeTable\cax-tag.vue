<template>
    <div :class="['cax-tag', `cax-tag-${type}`]">
        {{ content }}
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
    type: {
        type: String,
        default: 'default'
    },
    content: {
        type: String,
        default: ''
    },
})

</script>

<style lang="less" scoped>
.cax-tag {
    display: inline-block;
    padding: 1px 8px;
    border-radius: 4px;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
}
.cax-tag-1 {
        background-color: @link-6;
        color: @primary-1;
    }
.cax-tag-2 {
        background-color: @danger-2;
        color: @danger-6;
    }
.cax-tag-3 {
        background: rgba(145, 39, 252, 0.1);
        color: #9127FC;
    }
.cax-tag-4 {
        background-color: @success-1;
        color: @success-6;
    }
</style>