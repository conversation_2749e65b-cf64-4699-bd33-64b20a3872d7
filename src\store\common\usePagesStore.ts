/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-12-06 14:27:11
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-06 15:57:30
 * @FilePath: /neue-cloud-vdi/src/store/common/usePagesStore.ts
 */
import { defineStore } from 'pinia'

interface Page {
  path: string
  name: string
  component: () => Promise<typeof import('*.vue')>
  children?: Page[]
}
// 0: 无, 1: 仅标题, 2: 标题和返回 3：标题和返回和设置
type HeaderToobar = {
  showGoBack?: boolean   // 是否显示返回按钮
  showSetting?: boolean  // 是否显示设置按钮
  title: string[]       // 标题
  tag?: string           // 标签 
}

export const usePagesStore = defineStore('pagesInfo', {
  state: () => ({
    pages: [] as Page[],
    pathFrom: '' as string,
    headerToobar: {
      showGoBack: false,
      showSetting: false,
      title: [],
      tag: ''
    } as HeaderToobar
  }),
  getters: {
    getPages: (state) => state.pages,
    getPathFrom: (state) => state.pathFrom,
    getHeaderToobar: (state) => state.headerToobar
  },
  actions: {
    // async fetchPageList() {
    //   try {
    //     const response = await loginService.pagePermission()
    //     // const pages: Page[] = await response.json()
    //     // this.pages = pages.map((page) => ({
    //     //   ...page,
    //     //   component: () => import(`../views/${page.name}View.vue`)
    //     // }))
    //   } catch (error) {
    //     console.error('Failed to fetch page list:', error)
    //   }
    // },
    setPages(pages: Page[]) {
      this.pages = pages
    },
    setPathFrom(path: string) {
      // console.log('setPathFrom', path)
      this.pathFrom = path
    },
    setHeaderToobar(headerToobar: HeaderToobar) {
      this.setHeaderToobarInit()
      this.headerToobar = headerToobar
    },
    setHeaderToobarInit() {
      this.headerToobar = {
        showGoBack: false,
        showSetting: false,
        title: [],
        tag: ''
      }
    }
  }
})
