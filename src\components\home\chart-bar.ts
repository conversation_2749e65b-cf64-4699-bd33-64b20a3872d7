const barOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{b}  <span style="color: #fff;">{c} ms</span>', // 这里 {a} 是系列名，{b} 是类目名，{c} 是数值
    textStyle: {
      color: '#94A3B8' // 你可以根据需要设置颜色
    },
    backgroundColor: 'rgba(38, 42, 51, 0.8)',
    borderColor: '#333',
    borderWidth: 0,
    padding: [4, 8],
    extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);' // 额外的 CSS 样式
  },
  xAxis: {
    type: 'category',
    data: ['9.1', '9.2', '9.3', '9.4', '9.5', '9.6', '9.7'],
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: false,
      lineStyle: {
        color: 'transparent'
      }
    },
    splitLine: {
      show: false,

      lineStyle: {
        color: 'transparent'
      }
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: 'transparent'
      }
    },
    axisTick: {
      show: false
    }
  },
  series: [
    {
      // name: 'Evaporation',
      type: 'bar',
      data: [260, 400, 260, 450, 260, 440, 260, 162.2],
      itemStyle: {
        color: '#C8D3EA',
        borderRadius: 8,
        borderColor: 'transparent', // 确保没有边框颜色
        borderWidth: 0 // 确保没有边框宽度
      },
      barWidth: '60%'
    },
    {
      // name: 'Evaporation',
      type: 'line',
      data: [260, 400, 260, 450, 260, 440, 260, 162.2],
      itemStyle: {
        color: '#262A33',
        borderRadius: 8
      },
      smooth: true // 添加这个属性来使折线图平滑
    }
  ],
  grid: {
    top: '0%',
    bottom: '0%',
    left: '0%',
    right: '0%',
    containLabel: true
  }
}

export default {
  barOption
}
