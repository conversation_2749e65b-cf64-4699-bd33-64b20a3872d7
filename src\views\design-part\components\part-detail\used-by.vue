<template>
  <ne-tree-table
    :data="dataList"
    :columns="tableColumns"
    row-key="ncid"
    :lazy="true"
    :load="loadChildrenData"
    :treeProps="{ children: 'children', hasChildren: 'hasChildren' }"
  >
    <template #partNo="{ row }">
      <span class="items-center inline-block"
        ><part-icon :partType="row.partType"></part-icon>
        <span class="ml-[4px]">{{ row.partNo }}</span></span
      >
    </template>
    <template #modifiedAt="{ row }">
      {{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
    </template>
    <template #createdAt="{ row }">
      {{ dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss') }}
    </template>
    <template #version="{ row }">
      {{ row.version ? `v${row.version}` : '-' }}
      <el-tag class="ml-2" v-if="row.latestVersion" type="primary">最新版</el-tag>
    </template>
    <template #status="{ row }">
      <el-tag :type="StatusEmu[row.status]?.type">
        {{ StatusEmu[row.status]?.text || row.status }}
      </el-tag>
    </template>
  </ne-tree-table>
</template>
<script setup lang="ts">
import { ref, onMounted, watch, computed, reactive } from '@vue/runtime-core'
import { StatusEmu } from '@/constant'
import actions from '@/service/actions'
import dayjs from 'dayjs'
import PartIcon from '../part-icon.vue'

const props = defineProps({
  ncid: {
    type: String,
    default: undefined
  }
})
let dataList = ref([])

const tableColumns = [
  { prop: 'partNo', label: '零部件编号' },
  { prop: 'instanceName', label: '实例名称' },
  { prop: 'version', label: '版本号' },
  { prop: 'status', label: '状态' },
  { prop: 'modifiedAt', label: '更新时间' },
  { prop: 'createdAt', label: '创建时间' }
]

const loadChildrenData = async (
  row: any,
  treeNode: any,
  resolve: (data: any[]) => void
) => {
  const childrenData = await getDesignPartList(row.ncid)
  resolve(childrenData)
}
let getDesignPartList = async (parentId?: string) => {
  let res = await actions.postDesignPartRecursiveGet({
    id: 'plt0CADBOM',
    expands: [
      {
        referenceField: 'source'
      }
    ],
    condition: {
      logic: 'AND',
      ignoreCase: true,
      statements: [
        {
          field: 'target.ncid',
          value: [parentId],
          operator: '='
        }
      ]
    }
  })
  let arr = res.data.map((item) => {
    return {
      ...item,
      ...item.source,
      hasChildren: true
    }
  })
  return arr
}

onMounted(async () => {
  const rootData = await getDesignPartList(props.ncid)
  dataList.value = rootData
})
</script>

<style scoped lang="less"></style>
