<template>
    <div class="agent-container">
      <div>
        <div class="agent-icon"><i class="iconfont icon-exclamation-circle-fill"></i></div>
        <div class="agent-text">请启动agent后重试</div>
        <el-button type="primary" @click="setUp" size="large">启动CDP Agent</el-button>
      </div>
    </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
const router = useRouter()

const setUp = () => {
  router.push({path: '/login'})
}

</script>

<style lang="less" scoped>


.agent-container {
  // 水平垂直居中
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  &>div{
    width: 212px;
  }
}
.agent-icon {
  text-align: center;
  i{
    color: @warning-5;
    font-size: 36px;
  }
}
.agent-text {
  font-size: @text-3;
  color: #1E293B;
  font-weight: 500;
  margin-top: 6px;
  margin-bottom: 32px;
  text-align: center;
}
.el-button {
  width: 100%;
  height: 50px;

  border-radius: 12px;
  background: #383B46;
  color: #fff;
  &:hover {
    background-color: @primary-6;
    color: #fff;
  }
 font-size: 20px !important;
  font-weight: 500!important;
}


</style>