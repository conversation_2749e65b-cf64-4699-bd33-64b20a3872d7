<template>
  <div>
    <!-- 表格主体 -->
    <el-table
      v-bind="tableAttrs"
      :data="dataSource"
      ref="tableRef"
      :expand-row-keys="expandRowKeys"
      style="--el-table-header-bg-color: #f7f8fa; --el-border-color-lighter: #e2e8f0"
    >
      <el-table-column v-for="column in columns" :key="column.prop" v-bind="column">
        <template #default="scope" v-if="!column.type">
          <slot v-if="$slots[column.prop]" :name="column.prop" v-bind="scope"> </slot>
          <template v-else>{{ scope.row[column.prop] }}</template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, computed, useAttrs, onMounted } from 'vue'
import { ElTable, ElTableColumn } from 'element-plus'
import { neTreeTableProps } from './type'
const props = defineProps(neTreeTableProps)
console.log(props)
const tableAttrs = computed(() => {
  let { columns, pagination, defaultPagination, data, ...otherProps } = props
  console.log('传递给 el-table 的属性:', props)
  // 添加树形配置
  return otherProps
})

// 基本属性
const columns = computed(() => props.columns || [])
const expandRowKeys = computed(() => props.expandRowKeys)
const dataSource = computed(() => {
  console.log(props.data)
  return props.data
})
const tableRef = ref()
defineExpose({
  tableIns: tableRef,
  toggleRowExpansion: (row: any, f: Boolean) => {
    tableRef.value.toggleRowExpansion(row, f)
  }
})
</script>

<style lang="less"></style>
