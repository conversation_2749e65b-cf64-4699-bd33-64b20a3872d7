<template>
  <div class="toolbar">
    <div
      v-for="(tool, index) in operationList"
      :key="index"
      @click="handleToolbar(tool)"
    >
      <div v-if="tool.img">
        <el-tooltip
          placement="top"
          :effect="effectModel(tool.des)"
          :popper-class="
            effectModel(tool.des) === 'dark'
              ? 'custom-tooltip-dark'
              : 'custom-tooltip-light'
          "
        >
          <!-- Tooltip 内容插槽 -->
          <template #content>
            <div
              v-for="item in tool.des"
              :key="item.key"
              :class="effectModel(tool.des) === 'dark' ? '' : 'tooltip-item'"
              @click="handleToolbarItem(tool, item)"
            >
              <div
                v-if="item.img"
                style="
                  width: 16px;
                  height: 16px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <img
                  v-if="item.img"
                  :src="item.img"
                  style="
                    width: 16px;
                    height: 16px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                />
              </div>
              <span
                :style="{ color: effectModel(tool.des) === 'dark' ? '#fff' : '#333' }"
                >{{ item.text }}</span
              >
            </div>
          </template>

          <!-- 触发元素 -->
          <div style="width: 24px; height: 24px" class="toolIcon">
            <img :src="getActiveImg(tool)" />
            <!-- <img :src="currentMode === 'clicked' && selectedLabel === tool.label && tool.selectedImg ? tool.selectedImg : currentMode === 'itemClicked' && clickImg && selectedLabel === tool.label ? clickImg : currentMode === 'measureClicked' && selectedLabel === tool.label && measuredImg ? measuredImg : tool.img" /> -->
          </div>
        </el-tooltip>
      </div>
      <div v-else class="line"></div>
    </div>
  </div>
  <DefaultDialog v-model="defaultDialogVisible" :title="defaultDialogTitle" @delete="onDelete" @close="handleClose">
    <!-- <MeasurementPanel /> -->
		<component :is="currentComponent" />
  </DefaultDialog>
</template>

<script setup lang="ts">
import { OperationItem, Operation } from './constant'
import cutSelect from '@/assets/images/ufrEngine/elite-3d-cut.svg'
import edgeAndFace from '@/assets/images/ufrEngine/edgeAndFace.svg'
import perspective from '@/assets/images/ufrEngine/camera.svg'
import back from '@/assets/images/ufrEngine/back.svg'
import front from '@/assets/images/ufrEngine/front.svg'
import up from '@/assets/images/ufrEngine/up.svg'
import down from '@/assets/images/ufrEngine/down.svg'
import left from '@/assets/images/ufrEngine/left.svg'
import right from '@/assets/images/ufrEngine/right.svg'
import restore from '@/assets/images/ufrEngine/restore.svg'
import zoom from '@/assets/images/ufrEngine/zoom.svg'
import measure from '@/assets/images/ufrEngine/measure.svg'
import eliteCut from '@/assets/images/ufrEngine/eliteCut.svg'
import exploded from '@/assets/images/ufrEngine/exploded.svg'
import hotkey from '@/assets/images/ufrEngine/hotkey.svg'
import orthogonality from '@/assets/images/ufrEngine/orthogonality.svg'
import fit from '@/assets/images/ufrEngine/fit.svg'
import line from '@/assets/images/ufrEngine/line.svg'
import surface from '@/assets/images/ufrEngine/surface.svg'
import measureDis from '@/assets/images/ufrEngine/measureDis.svg'
import selectedExploded from '@/assets/images/ufrEngine/selectedExploded.svg'
import selectedHotkey from '@/assets/images/ufrEngine/selectedHotkey.svg'
import measureObjSelect from '@/assets/images/ufrEngine/measureObjSelect.svg'
import measureDisSelect from '@/assets/images/ufrEngine/measureDisSelect.svg'
import { ref, computed } from 'vue'
import DefaultDialog from './components/defaultDialog.vue'
import MeasurementPanel from './components/measurementPanel.vue'
import ExplodedPanel from './components/explodedPanel.vue'

interface PanelComponents {
  [key: string]: any;
  '测量对象': typeof MeasurementPanel;
  '爆炸图': typeof ExplodedPanel;
}


const operationList = ref<Operation[]>([
  {
    label: 'restore',
    img: restore,
    des: [
      {
        text: '还 原',
        img: '',
        key: 'restore'
      }
    ]
  },
  {
    label: 'camera',
    img: perspective,
    des: [
      {
        text: '透视',
        img: orthogonality,
        key: 'orthogonality'
      },
      {
        text: '正交',
        img: perspective,
        key: 'perspective'
      }
    ]
  },
  {
    label: 'position',
    img: front,
    des: [
      {
        text: '上',
        img: up,
        key: 'up'
      },
      {
        text: '下',
        img: down,
        key: 'down'
      },
      {
        text: '左',
        img: left,
        key: 'left'
      },
      {
        text: '右',
        img: right,
        key: 'right'
      },
      {
        text: '前',
        img: front,
        key: 'front'
      },
      {
        text: '后',
        img: back,
        key: 'end'
      }
    ]
  },
  {
    label: 'zoom',
    img: zoom,
    des: [
      {
        text: '窗口缩放',
        img: zoom,
        key: 'scale'
      },
      {
        text: '窗口适应',
        img: fit,
        key: 'adapt'
      }
    ]
  },
  {
    label: 'display',
    img: edgeAndFace,
    des: [
      {
        text: '线+面',
        key: 'edgeAndFace',
        img: edgeAndFace
      },
      {
        text: '线',
        img: line,
        key: 'edge'
      },
      {
        text: '面',
        key: 'face',
        img: surface
      }
    ]
  },
  {
    label: '',
    img: '',
    des: [
      {
        text: '',
        img: '',
        key: ''
      }
    ]
  },
  {
    label: 'measure',
    img: measure,
    des: [
      {
        text: '测量对象',
        img: measure,
        key: 'measureObject',
        selectedImg: measureObjSelect
      },
      {
        text: '测量距离',
        img: measureDis,
        key: 'measureDistance',
        selectedImg: measureDisSelect
      }
    ]
  },
  {
    label: 'sectioning',
    img: eliteCut,
    selectedImg: cutSelect,
    des: [
      {
        text: '剖切',
        img: '',
        key: 'sectioning'
      }
    ]
  },
  {
    label: 'exploded',
    img: exploded,
    selectedImg: selectedExploded,
    des: [
      {
        text: '爆炸图',
        img: '',
        key: 'exploded'
      }
    ]
  },
  {
    label: 'hotkey',
    img: hotkey,
    selectedImg: selectedHotkey,
    des: [
      {
        text: '快捷键',
        img: '',
        key: 'hotkey'
      }
    ]
  }
])

const selectedLabel = ref<string>('')
const clickImg = ref<string>('')
const currentMode = ref<string>('')
const measuredImg = ref<string>('')
const defaultDialogVisible = ref<boolean>(false)
const defaultDialogTitle = ref<string>('测量对象')


// 根据 title 显示不同的组件
const panelComponents: PanelComponents = {
  '测量对象': MeasurementPanel,
  '爆炸图': ExplodedPanel
}

const currentComponent = computed(() => {
  return panelComponents[defaultDialogTitle.value] || MeasurementPanel
})

const effectModel = (des: OperationItem[]): string => {
  return des.length === 1 ? 'dark' : 'light'
}

const getActiveImg = (tool: Operation): string => {
  if (
    currentMode.value === 'clicked' &&
    selectedLabel.value === tool.label &&
    tool.selectedImg
  ) {
    return tool.selectedImg
  }

  if (
    currentMode.value === 'itemClicked' &&
    selectedLabel.value === tool.label &&
    clickImg.value
  ) {
    return clickImg.value
  }

  if (
    currentMode.value === 'measureClicked' &&
    selectedLabel.value === tool.label &&
    measuredImg.value
  ) {
    return measuredImg.value
  }

  return tool.img || ''
}

const handleToolbarItem = (tool: Operation, item: OperationItem) => {
  selectedLabel.value = tool.label
  if (tool.label === 'measure') {
    currentMode.value = 'measureClicked'
    if (item.selectedImg) {
      measuredImg.value = item.selectedImg
    }
    clickImg.value = ''
  } else {
    clickImg.value = item.img
    currentMode.value = 'itemClicked'
    measuredImg.value = ''
  }
  // 这里执行对应的功能逻辑

  if (handlerMap[item.key]) handlerMap[item.key]()
}

const handleClose = () => {
	clickImg.value = ''
	currentMode.value = ''
}

const handlerMap: Record<string, () => void> = {
  orthogonality: () => console.log('orthogonality'),
  perspective: () => console.log('perspective'),
  up: () => console.log('up'),
  down: () => console.log('down'),
  left: () => console.log('left'),
  right: () => console.log('right'),
  front: () => console.log('front'),
  end: () => console.log('end'),
  scale: () => console.log('scale'),
  adapt: () => console.log('adapt'),
  edgeAndFace: () => console.log('edgeAndFace'),
  edge: () => console.log('edge'),
  face: () => console.log('face'),
  measureObject: () => handleMeasure('object'),
  measureDistance: () => handleMeasure('distance'),
  restore: () => console.log('restore'),
  hotkey: () => console.log('hotkey'),
  exploded: () => handleExploded(),
  sectioning: () => console.log('sectioning')
}

const handleMeasure = (type: string) => {
	console.log(type, 'measure')
	defaultDialogVisible.value = true
	if (type === "object") {
		defaultDialogTitle.value = "测量对象"
	} else {
		defaultDialogTitle.value = "测量距离"
	}
}

const handleExploded = () => {
	defaultDialogVisible.value = true
	defaultDialogTitle.value = "爆炸图"
}

const handleToolbar = (tool: Operation) => {
  const toolArr = ['hotkey', 'exploded', 'sectioning']
  if (toolArr.includes(tool.label)) {
    selectedLabel.value = tool.label
    currentMode.value = 'clicked'
  }
  if (handlerMap[tool.label]) handlerMap[tool.label]()
}

const onDelete = () => {
  console.log('删除')
}
</script>

<style lang="less" scoped>
.toolbar {
  position: absolute;
  left: 40%;
  bottom: 24px;
  width: 480px;
  height: 36px;
  border-radius: 8px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6px 24px;
  gap: 24px;

  .toolIcon {
    cursor: pointer;

    &:hover {
      background-color: #f0f3fa;
    }
  }

  .line {
    position: static;
    left: 264px;
    top: 6px;
    width: 1px;
    height: 24px;
    opacity: 1;
    border: 1px solid rgba(0, 0, 0, 0.25);
    z-index: 5;
  }
}
</style>

<style lang="less">
// 自定义 tooltip 样式
.custom-tooltip-dark {
  width: 64px;
  height: 36px;
  opacity: 1;
  background: rgba(0, 0, 0, 0.85);
  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.12), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
    0px 9px 28px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-tooltip-light {
  border: 1px solid #ebeef5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 0;
  // min-width: 120px;

  .tooltip-item {
    display: flex;
    align-items: center;
    margin: 0 2px;
    padding: 6px 16px;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    gap: 6px;
    min-width: 60px;
    height: 32px;
    border-radius: 4px;
    align-self: stretch;

    &:hover {
      background-color: #f0f3fa;
      // border-radius: 8px;
    }
  }
}
</style>
