<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-22 17:35:08
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-10-30 14:06:10
 * @FilePath: /neue-cloud-vdi/src/components/app/delete.vue
-->
<template>
  <el-dialog
    id="dialog-app-del"
    v-model="props.visible"
    title="删除"
    :style="{
      width: '520px',
      maxHeight: '198px'
    }"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <template #default>
      <div class="dialog-content">
        <div class="dialog-title">
          <template v-if="props.redirectPath">
            <div class="dialog-title-text">
              <i class="iconfont icon-exclamation-circle-fill"></i>
              <span class="title-danger-desc">无法删除“{{ props.activeDetail.name }}”</span>
            </div>
            <div class="dialog-title-desc">该操作需删除您的镜像及其关联应用，不允许执行此操作。</div>
          </template>
          <template v-else>
            <div class="dialog-title-text title-danger">
              <i class="iconfont icon-exclamation-circle-fill"></i>
              <span class="title-danger-desc">您确定要删除“{{ props.activeDetail.name }}”吗？</span>
            </div>
            <div class="dialog-title-desc">删除操作无法恢复，请谨慎操作。</div>
          </template>
          <template></template>
        </div>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="props.activeDetail.bind_status === 2">
          <el-button id="console-btn-image" type="primary" @click="handleRedirect()">前往镜像管理</el-button>
        </template>
        <template v-else>
          <el-button id="console-btn-cancel" @click="handleClose">取消</el-button>
          <el-button id="console-btn-submit" type="primary" @click="handleSave()">确定</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  redirectPath: {
    type: String,
    default: ''
  },
  activeDetail: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:visible', 'handleDelete'])


const handleClose = () => {
  emit('update:visible', false)
}
const handleSave = () => {
  console.log('handleDelete===', props.activeDetail)
  emit('handleDelete', props.activeDetail)
}
const handleRedirect = () => {
  emit('update:visible', false)
  console.log('redirectPath===>', props.redirectPath)
  router.push(props.redirectPath)
}

</script>

<style lang="less">
.dialog-title-text {
  display: flex;
  align-items: center;
  .title-danger {
    color: @danger-6;
  }
  .title-danger-desc {
    word-break: break-all;
  }
}
.icon-exclamation-circle-fill {
  color: @warning-5;
  font-size: @text-5 !important;
}

</style>
