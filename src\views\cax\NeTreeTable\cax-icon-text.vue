<template>
    <div class="cax-icon-text">
        <i :class="['iconfont', iconConfig[props.type]]" 
            :style="{color: colorConfig[props.type], fontSize: props.iconSize }"></i>
        <span :style="{fontSize: props.textSize}">{{ props.content }}</span>
    </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
const props = defineProps({
    type: {
        type: String,
        default: 'default'
    },
    content: {
        type: String,
        default: ''
    },
    iconSize: {
        type: String,
        default: '16px'
    },
    textSize: {
        type: String,
        default: '14px'
    },
})



const iconConfig: Record<string, string> = {
  'IN': 'icon-fanghui',
  'OUT': 'icon-quchu',
  'ASSEMBLY': 'icon-a-zhuangpei1',
  'DRAWING': 'icon-tree-drawing',
  'PART': 'icon-a-lingjian1',
  'FEM': 'icon-a-fangzhen1',
'BIN':'icon-bin',
};

const colorConfig: Record<string, string> = {
  'IN': '#3471FF',
  'OUT': '#F97316',
  'ASSEMBLY': '#A263EA',
  'DRAWING': '#EDB581',
  'PART': '#6789D8',
  'FEM': '#181818',
  'BIN':'#FDCD4B',
};

</script>

<style lang="less" scoped>
.cax-icon-text {
    display: flex;
    align-items: center;
    .iconfont {
        margin-right: 4px;
    }
}
</style>