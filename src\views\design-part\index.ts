export interface Response {
  data: Plt0NeueCADPartEntity
  response?: Plt0ResponseStatusObject
  [property: string]: any
}

/**
 * Plt0NeueCADPartEntity
 */
export interface Plt0NeueCADPartEntity {
  checkedOutAt: string
  checkedOutBy: string
  checkState: CheckState
  /**
   * 创建时间
   */
  createdAt: string
  /**
   * 创建人
   */
  createdBy: Plt0RefrenceObject
  gravityCenter?: string
  latestRevision: boolean
  latestVersion: boolean
  lockedAt: string
  lockedBy: string
  lockState: LockState
  mass?: number
  master: Plt0RefrenceObject
  material?: string
  /**
   * 修改时间
   */
  modifiedAt: string
  /**
   * 修改人
   */
  modifiedBy: Plt0RefrenceObject
  /**
   * 诺源资源唯一标识
   */
  ncid: string
  openSurfaceArea?: number
  owner: Plt0RefrenceObject
  partDescription?: string
  partName?: string
  partNo?: string
  partType?: PartType
  preVersion: Plt0RefrenceObject
  revision: string
  /**
   * 模型版本
   */
  schemaVersion: string
  solidSurfaceArea?: number
  status: CADFileLifecycleStatus
  version: string
  volume?: number
  [property: string]: any
}

/**
 * CheckState
 */
export enum CheckState {
  CheckedIn = 'CHECKED_IN',
  CheckedOut = 'CHECKED_OUT'
}

/**
 * 创建人
 *
 * Plt0RefrenceObject
 *
 * 修改人
 */
export interface Plt0RefrenceObject {
  /**
   * reference ncid
   */
  ncid: string
  [property: string]: any
}

/**
 * LockState
 */
export enum LockState {
  Locked = 'LOCKED',
  Unlocked = 'UNLOCKED'
}

export enum Plt0StorageItemState {
  PENDING = 'PENDING',
  UPLOADING = 'UPLOADING',
  UPLOADED = 'UPLOADED'
}

/**
 * PartType
 */
export enum PartType {
  NeueASM = 'NEUE_ASM',
  NeuePrt = 'NEUE_PRT'
}

export enum FileUsageType {
  DESIGN_MODEL = '设计模型',
  EXCHANGE_FORMAT = '交换格式',
  THUMBNALI = '缩略图',
  PDF = 'PDF',
  SIMULATION = '仿真'
}

/**
 * CADFileLifecycleStatus
 */
export enum CADFileLifecycleStatus {
  Inwork = 'INWORK'
}

/**
 * Plt0ResponseStatusObject
 */
export interface Plt0ResponseStatusObject {
  /**
   * 状态码
   */
  code?: number
  /**
   * 状态信息
   */
  message?: string
  [property: string]: any
}

/**
 * plt0CADBOMResponseList
 */
export interface Plt0CADBOMResponseList {
  /**
   * 是否在BOM中排除
   */
  BOMExcluded?: boolean
  /**
   * 配置
   */
  configuration?: string
  /**
   * 创建时间
   */
  createdAt?: string
  /**
   * 创建者
   */
  createdBy?: { [key: string]: any }
  /**
   * 实例名称
   */
  instanceName?: string
  /**
   * 修改时间
   */
  modifiedAt?: string
  /**
   * 修改者
   */
  modifiedBy?: { [key: string]: any }
  /**
   * 诺源唯一ID
   */
  ncid?: string
  /**
   * 数量
   */
  quantity?: { [key: string]: any }
  /**
   * 模型版本
   */
  schemaVersion?: string
  source?: Source
  /**
   * 抑制
   */
  suppressed?: boolean
  target?: { [key: string]: any }
  /**
   * 变换矩阵
   */
  transformationMatrix?: string
  [property: string]: any
}
export interface Source {
  /**
   * 诺源唯一ID
   */
  ncid?: string
  [property: string]: any
}
