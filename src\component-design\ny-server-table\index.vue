<template>
  <el-table class="ny-server-table" v-bind="otherProps" :data="dataSource" style="width: 100%">
    <el-table-column v-for="column in columns" v-bind="column" :key="column.prop" :type="column.type || undefined">
      <template #default="scope" v-if="!column.type">
        <slot :name="column.prop" v-bind="scope">{{ scope.row[column.prop] }}</slot>
      </template>
    </el-table-column>
  </el-table>
  <div class="mt-[30px] flex justify-end mr-10">
    <el-pagination @current-change="handleCurrentChange" :current-page="paginationRef.currentPage"
      :page-size="paginationRef.pageSize" :total="totalItems" layout="total, prev, pager, next, jumper" />
  </div>
</template>
<script setup lang="ts">
import { ref, PropType, useAttrs, onMounted, defineExpose, watch, computed } from 'vue'
import { ElTable, ElTableColumn, ElPagination } from 'element-plus'
import NyServerTable from '.'
import { TablePaginationProps } from '../ny-table/type';

const otherProps = useAttrs()
const props = defineProps<NyServerTable>()
const currentPage = ref(1)
const dataSource = ref<any[]>([])
const totalItems = ref(0)
const paginationRef = ref(
  Object.assign({}, props.defaultPagination, {
    currentPage: 1,
    pageSize: 10
  })
)
watch(
  () => props.columns,
  (newVal, oldVal) => {
    dataSource.value = []
  }
)
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  refresh({ currentPage: page, pageSize: paginationRef.value.pageSize })
}
let refresh = (pagination: TablePaginationProps) => {
  props.reqFn(pagination).then((res) => {
    paginationRef.value.currentPage = res.currentPage
    paginationRef.value.pageSize = res.pageSize

    dataSource.value = res.items
    totalItems.value = res.total
  })
}
defineExpose({
  refresh
})
onMounted(() => {
  refresh(paginationRef.value)
})
</script>

<style lang="less">
.ny-server-table {
  &.td-boder0 {
    .el-table__inner-wrapper {
      &:before {
        background: transparent !important;
      }
    }

    td {
      border-bottom: none !important;
    }
  }
}
</style>
