import storage from '@/utils/storage'
import router from '@/router';
import { WebSocketManager } from './WebSocketManager';

// 连接失败跳转cax-agent-not-start页面
const onReconnectFailed = () => {
    router.push({
        name: 'CaxAgentNotStart',
    })
    console.log('重连失败');
}

  // 封装WebSocketManager
export const socket = new WebSocketManager({
    url: 'ws://127.0.0.1:9000?consoleID=ab12',
    onOpen: () => console.log('连接成功'),
    onMessage: (msg) => console.log('收到消息', msg),
    onError: (e) => console.error('连接错误', e),
    onClose: () => console.log('连接关闭'),
    onReconnectFailed:onReconnectFailed,
  });



// json数据格式
export const jsonData = (trackId: any,data: any) => {
    const userId = storage.get('agentUserId')
    const agentId = storage.get('agentId')
    return{
        from: userId,
        to: agentId,
        trackId: trackId,
        body:data,
    }
}

// 登录信息
export const loginData = () => {
    let token = storage.get('token')
    let userName = storage.get('userName')
    // 随机生成一个4位的字母数字
    const random = Math.random().toString(36).substring(2, 6)
    let trackId = random
    let data = [
        {
            "CA00004": [
                {
                    "userName": userName,
                    "token": token,
                }
            ]
        }
    ]
    return jsonData(trackId, data)
}

// 获取model列表
export const getModelList = () => {
    const random = Math.random().toString(36).substring(2, 6)
    let trackId = random
    let data = [
        {
            "CA11000": []
        }
    ]
    return jsonData(trackId, data)
}