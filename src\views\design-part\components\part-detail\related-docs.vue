<template>
  <div class="related-docs">
    <!-- 3D文件部分 -->
    <div class="section">
      <div
        class="text-[16px] font-[600] text-[#0F172A] mb-6 h-[48px] flex items-center bg-[#F0F3FA] pl-[20px]"
      >
        3D文件
      </div>
      <ny-table :data="threeDFiles" :columns="threeDColumns" :pagination="false">
        <template #fileName="{ row }">
          <div class="flex items-center whitespace-nowrap">
            <i :class="getIconType(row)"></i>
            {{ row.target.storageItem?.name }}
            <el-tag
              v-if="row.usage === 'DESIGN_MODEL'"
              type="primary"
              effect="light"
              class="ml-1"
              size="small"
              style="
                --el-tag-text-color: #1856eb;
                --el-tag-border-color: #8fb1ff;
                --el-tag-bg-color: #edf2fc;
              "
              >主模型</el-tag
            >
          </div>
        </template>
        <template #mimeType="{ row }"
          >{{ row.target.storageItem?.mimeType || '-' }}
        </template>
        <template #size="{ row }"
          >{{ formatFileSize(row.target.storageItem?.size || 0) }}
        </template>
        <template #createdAt="{ row }"
          >{{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template #modifiedAt="{ row }"
          >{{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template #usage="{ row }">
          {{ FileUsageType[row.usage as keyof typeof FileUsageType] }}
        </template>
        <template #schemaVersion="{ row }">
          <div class="flex">
            <img
              src="@/assets/images/fileIcon/version.svg"
              width="16"
              class="mr-1"
              alt=""
            />{{ row.schemaVersion }}
          </div></template
        >
        <template #action="{ row }">
          <i
            v-if="row.target?.storageItem?.state === Plt0StorageItemState.UPLOADED"
            @click="downloadFile(row)"
            class="iconfont icon-download mr-1 hover:text-[#1856EB] cursor-pointer"
          ></i>
          <span v-else>-</span>
        </template>
      </ny-table>
    </div>
    <!-- 2D图纸部分 -->
    <div class="section">
      <div
        class="text-[16px] font-[600] text-[#0F172A] mb-6 h-[48px] flex items-center bg-[#F0F3FA] pl-[20px]"
      >
        2D图纸
      </div>
      <ny-table :data="twoDDrawings" :columns="twoDColumns" :pagination="false">
        <template #status="{ row }">
          <el-tag :type="StatusEmu[row.target.status]?.type">{{
            StatusEmu[row.target.status]?.text
          }}</el-tag>
        </template>
        <template #drawingNo="{ row }">{{ row.target.drawingNo }} </template>
        <template #createdAt="{ row }"
          >{{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template #modifiedAt="{ row }"
          >{{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </ny-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { StatusEmu } from '@/constant'
import actions from '@/service/actions'
import { ref, defineProps, onMounted } from '@vue/runtime-core'
import dayjs from 'dayjs'
import { formatFileSize } from '@/utils'
import { Plt0StorageItemState, FileUsageType } from '../..'

// 定义组件属性
const props = defineProps({
  partId: {
    type: [String, Number],
    required: true
  }
})
let { partId } = props
// 3D文件数据
const threeDFiles = ref([])

// 2D图纸数据
const twoDDrawings = ref([])
let threeDColumns = [
  {
    label: '文件名',
    prop: 'fileName'
  },
  {
    label: '格式',
    prop: 'mimeType'
  },
  {
    label: '软件版本',
    prop: 'schemaVersion'
  },
  {
    label: '文件大小',
    prop: 'size'
  },
  {
    label: '用途',
    prop: 'usage'
  },
  {
    label: '创建时间',
    prop: 'createdAt',
    sortable: true
  },
  {
    label: '更新时间',
    prop: 'modifiedAt',
    sortable: true
  },
  {
    label: '操作',
    prop: 'action'
  }
]
let twoDColumns = [
  {
    label: '图纸编号',
    prop: 'drawingNo'
  },
  {
    label: '子类型',
    prop: 'drawingType'
  },
  {
    label: '版本',
    prop: 'schemaVersion'
  },
  {
    label: '状态',
    prop: 'status'
  },
  {
    label: '创建时间',
    prop: 'createdAt',
    sortable: true
  },
  {
    label: '更新时间',
    prop: 'modifiedAt',
    sortable: true
  }
]
let getIconType = (row: any) => {
  let { mimeType } = row.target?.storageItem || {}
  console.log(mimeType)
  if (mimeType == 'part') {
    return 'iconfont icon-a-3Dwenjian-zhumoxing mr-1'
  } else if (mimeType == 'idi') {
    return 'iconfont icon-a-3Dwenjian-idi mr-1'
  } else if (mimeType == 'ass') {
    return 'iconfont icon-a-3Dwenjian-zhuangpei mr-1'
  }
}
let downloadFile = async (row: any) => {
  let res = await actions.batchGetSignatureUrl({
    model: 'plt0CommonFile',
    ncids: [row.target.ncid],
    action: 'DOWNLOAD'
  })
  console.log(res)
  res.data.forEach((item: any) => {
    downloadFileByBlob({ url: item.url, name: fileNcid })
  })
}

let downloadFileByBlob = ({ url, name }: any) => {
  const a = document.createElement('a')
  a.href = url
  a.download = name || 'download'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  // 释放URL对象
  setTimeout(() => {
    URL.revokeObjectURL(url)
  }, 100)
}
let getInitData = async () => {
  let res3D = await actions.postDesignPartList({
    //3d文件
    classId: 'plt0CADFileFileRelation',
    expands: [
      {
        referenceField: 'source'
      },
      // {
      //   referenceField: 'target.storageItem'
      // },
      {
        referenceField: 'target',
        expands: [
          {
            referenceField: 'storageItem'
          }
        ]
      }
    ],
    condition: {
      logic: 'AND',
      ignoreCase: true,
      statements: [
        {
          field: 'source.ncid',
          value: [partId],
          operator: '='
        }
      ]
    }
  })
  threeDFiles.value = res3D.data
  let res2D = await actions.postDesignPartList({
    //2d图纸
    classId: 'plt0CADPart2DDrawingRelation',
    expands: [
      {
        referenceField: 'source'
      },
      {
        referenceField: 'target'
      }
    ],
    condition: {
      logic: 'AND',
      ignoreCase: true,
      statements: [
        {
          field: 'source.ncid',
          value: [partId],
          operator: '='
        }
      ]
    }
  })
  twoDDrawings.value = res2D.data
}
onMounted(() => {
  getInitData()
})
</script>

<style scoped lang="less">
.related-docs {
  .section {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #303133;
  }

  .file-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    background-color: #4468e9;
    color: white;
    border-radius: 4px;
  }

  .version-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #4468e9;
  }
}
</style>
