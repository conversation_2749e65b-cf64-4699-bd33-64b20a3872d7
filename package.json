{"name": "cdp-space", "version": "1.0.0", "private": false, "description": "A Vite2.x + Vue3 + TypeScript template starter", "scripts": {"start": "vite", "build": "vite build", "preview": "vite build --mode preview", "preprod": "vite build --mode preprod", "predev": "vite build --mode predev", "galaxy": "vite build --mode galaxy", "internal": "vite build --mode internal", "qa": "vite build --mode qa", "build-tsc": "vue-tsc --noEmit && vite build", "serve": "vite preview", "format": "prettier --write ./src", "lint": "eslint ./src --ext .vue,.js,.ts", "lint-fix": "eslint --fix ./src --ext .vue,.js,.ts", "prepare": "cd ../../ && husky install", "test": "jest"}, "keywords": ["Vite", "Vue3", "TypeScript", "Starter"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "lint-staged": {"*.{vue,js,ts}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@neue/neue-plus": "0.0.1", "@neue/resolver": "0.0.1", "@vue/runtime-core": "^3.5.13", "axios": "1.1.0", "cookiejs": "^2.1.3", "dayjs": "^1.11.13", "draggable-resizable-vue3": "1.0.94-beta", "echarts": "^5.5.1", "element-plus": "^2.9.7", "install": "^0.13.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "npm": "^10.9.2", "qs": "^6.13.1", "v-drag": "^3.0.9", "vite-plugin-vue-devtools": "^7.7.2", "vue": "^3.5.13", "vue-i18n": "^9.14.2", "vue-router": "^4.5.0", "vuedraggable": "^2.24.3", "vuex": "^4.1.0", "neue-ufr-engine": "1.0.7"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@types/jest": "^26.0.24", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "22.15.17", "@types/qs": "^6.9.17", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vitejs/plugin-vue": "^1.10.2", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.9.2", "eslint": "9.26.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^24.7.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.20.0", "husky": "^6.0.0", "jest": "^26.6.3", "less": "^4.2.1", "lint-staged": "^10.5.4", "pinia": "^2.3.1", "postcss": "^8.4.49", "prettier": "^2.8.8", "sass": "1.89.0", "stylus": "^0.54.8", "tailwindcss": "^3.4.16", "ts-jest": "^26.5.6", "typescript": "5.5.4", "unplugin-vue-components": "28.5.0", "vite": "5.1.8", "vue-jest": "5.0.0-alpha.10", "vue-tsc": "^2.1.10"}}