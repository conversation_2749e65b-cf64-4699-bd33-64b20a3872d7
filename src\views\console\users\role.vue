<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-12-04 16:45:44
 * @LastEditors: fangshijie <EMAIL>
 * @LastEditTime: 2025-02-13 17:39:56
 * @FilePath: /neue-cloud-vdi/src/views/console/users/role.vue
-->
<template>
  <el-main class="main-content">
    <div
      class="role-container page-container"
      style="display: flex; flex-direction: column"
    >
      <div class="container-title">
        <span>角色</span>
        <el-button
          id="createRole"
          type="primary"
          style="margin-left: 10px"
          @click="handleCreate"
        >
          创建角色
        </el-button>
      </div>
      <div class="container-filter">
        <el-input
          id="filterRoleName"
          v-model="filterRoleName"
          class="filter-rolename"
          placeholder="请输入角色名称进行模糊查询"
          :clearable="true"
          :clear="handleClearFilters"
          @clear="handleClearFilters"
          @keyup.enter="handleSearchIconClick"
        >
          <template v-slot:suffix>
            <i
              id="searchIcon"
              class="iconfont icon-interactive-search"
              @click="handleSearchIconClick"
            ></i>
          </template>
        </el-input>
      </div>
      <div class="" style="max-height: calc(100% - 100px); overflow: auto">
        <ny-table
          class="role-list"
          :data="roleListData"
          :columns="columns"
          @selection-change="handleSelectionChange"
          @filter-change="handleFilterChange"
        >
          <template #name="scope">
            <el-tooltip class="custom-tooltip">
              <template #default>
                <div class="item-name item-description">
                  {{ scope.row.name }}
                </div>
              </template>
              <template #content>
                <div style="max-width: 300px">
                  {{ scope.row.name }}
                </div>
              </template>
            </el-tooltip>
            <TextTag
              v-if="[1, 2, 3].includes(scope.row.isPre)"
              text="预置"
              size="small"
              type="roletag"
            />
          </template>
          <template #accountCount="scope">
            <span v-if="scope.row.accountCount === 0" style="padding: 2px">0</span>
            <el-button
              v-else
              type="primary"
              class="console-btn"
              @click="handleViewNumbers(scope.row)"
              link
            >
              {{ scope.row.accountCount }}
            </el-button>
          </template>
          <template #description="scope">
            <template v-if="!scope.row.description">
              <div style="display: flex; align-items: center">
                <span>--</span>
              </div>
            </template>
            <template v-else>
              <el-tooltip class="custom-tooltip">
                <template #default>
                  <div class="item-description">{{ scope.row.description }}</div>
                </template>
                <template #content>
                  <div style="max-width: 300px">
                    {{ scope.row.description }}
                  </div>
                </template>
              </el-tooltip>
            </template>
          </template>
          <template #createdTime="scope">
            <div v-if="[1, 2, 3].includes(scope.row.isPre)">--</div>
            <div v-else>
              <div>{{ dayjs(scope.row.createdTime).format('YYYY-MM-DD') }}</div>
              <div>{{ dayjs(scope.row.createdTime).format('HH:mm:ss') }}</div>
            </div>
          </template>
          <template #operate="scope" fixed>
            <el-popover
              v-if="allocationDisabled(scope.row)"
              placement="top"
              :width="74"
              :content="'暂无权限'"
            >
              <template #reference>
                <el-button
                  disabled
                  type="primary"
                  class="console-btn console-btn-allocation"
                  link
                >
                  分配
                </el-button>
              </template>
            </el-popover>
            <el-button
              v-else
              type="primary"
              class="console-btn console-btn-allocation"
              link
              @click="handleAllocation(scope.row)"
            >
              分配
            </el-button>
            <el-button
              v-if="scope.row.isPre"
              type="primary"
              class="console-btn console-btn-view"
              link
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              v-else
              type="primary"
              class="console-btn console-btn-edit"
              link
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-popover
              v-if="deleteDisabled(scope.row)"
              placement="top"
              :content="deleteTips(scope.row)?.text"
              :width="deleteTips(scope.row)?.width"
            >
              <template #reference>
                <ny-button
                  :disabled="deleteDisabled(scope.row)"
                  type="primary"
                  class="console-btn console-btn-delete"
                  link
                  @click="handleDel(scope.row)"
                >
                  删除
                </ny-button>
              </template>
            </el-popover>
            <ny-button
              v-else
              type="primary"
              class="console-btn console-btn-delete"
              link
              @click="handleDel(scope.row)"
            >
              删除
            </ny-button>
          </template>
        </ny-table>
      </div>
    </div>
    <RoleDistribute
      ref="RoleDistributeRef"
      v-model:visible="RoleDistributeDialogVisible"
      @handleComplete="handleComplete"
    >
    </RoleDistribute>
    <UserList v-model:visible="userListVisible" :role-id="roleEditId"></UserList>
    <ny-drawer v-model="roleEditDialogVisible" :close-on-click-modal="false">
      <template #header>
        <h4>{{ roleEditOpenType == 'info' ? '角色详情' : '角色编辑' }}</h4>
      </template>
      <RoleEdit
        ref="roleEditRef"
        :key="roleEditId"
        :id="roleEditId"
        :type="roleEditOpenType"
      ></RoleEdit>
      <template #footer>
        <div class="" v-if="'edit' == roleEditOpenType">
          <el-button type="primary" @click="submitForm()"> 确定 </el-button>
          <el-button @click="resetForm()">取消</el-button>
        </div>
        <div v-else>
          <el-button type="primary" @click="roleEditDialogVisible = false">
            关闭
          </el-button>
        </div>
      </template>
    </ny-drawer>

    <DialogWarning
      v-model:visible="warnDialogVisible"
      :title="'删除'"
      :warnText1="'确定要删除以下角色吗？'"
      :warnText2="'删除操作无法恢复，请谨慎操作。'"
      @confirm="handleDeleteConfirm"
    ></DialogWarning>
  </el-main>
</template>
<script setup lang="ts">
import router from '@/router'
import { ref, watch } from 'vue'
import TextTag from '@/components/common/text-tag.vue'
import UserList from '@/components/role/user-list.vue'
import RoleDistribute from '@/components/role/role-distribute.vue'
import { RoleDetail } from '@/common/types/console/role'
import dayjs from 'dayjs'
import { useUserInfoStore } from '@/store/user/userInfo'
import { RoleType } from '@/common/enum/console/role'
import RoleEdit from '@/components/role/role-edit.vue'
import DialogWarning from '@/components/common/dialog-warning.vue'
import role from '@/service/console/role'
import { ElMessage } from 'element-plus'
import access from '@/service/access'
let columns = ref([
  { prop: 'name', label: '角色名称', minWidth: 200 },
  { prop: 'accountCount', label: '成员数量', minWidth: 120 },
  { prop: 'description', label: '角色描述', minWidth: 372 },
  { prop: 'createdTime', label: '创建时间', minWidth: 160, sortable: false ,key:'createdTime'},
  { prop: 'operate', label: '操作', width: 132, fixed: 'right' },
])

const filterRoleName = ref('')

const userInfoStore = useUserInfoStore()
const roleListData = ref<Array<RoleDetail>>([])
const userListVisible = ref(false)
const userDrawerVisible = ref<number>()
// const filterName = ref('')
// const filterDetail = ref<any>({})
const RoleDistributeDialogVisible = ref(false)
const RoleDistributeRef = ref<InstanceType<typeof RoleDistribute> | null>(null)
const roleEditDialogVisible = ref(false)
const roleEditRef = ref<InstanceType<typeof RoleEdit> | null>(null)
const roleEditOpenType = ref('info')
const roleEditId = ref(-1)
const warnDialogVisible = ref(false)

const isAdmin = () => {
  const roles = userInfoStore.getAccess.roles || []
  if (roles.length === 0) return true
  return (
    roles.filter(
      (item) => item.roleType !== RoleType.ADMIN && item.roleType !== RoleType.SUPER
    ).length > 0
  )
}
// 不可分配的情况
const allocationDisabled = (item: any) => {
  const type = item.roleType || 0 // 默认类型为 0
  const userRoles = (userInfoStore.getAccess.roles || []).map((item) => item.roleType)

  if ([RoleType.SUPER, RoleType.NORMAL].includes(type)) {
    // 超管、普通用户不可分配
    return true
  } else if (!userRoles.includes(RoleType.SUPER) && RoleType.ADMIN === type) {
    // 管理员仅可被超管分配
    return true
  }
  false
}

const deleteDisabled = (item: RoleDetail) => {
  const count = item.accountCount || 0
  return !!(item.isPre || count > 0)
}

const deleteTips = (item: RoleDetail) => {
  if (item.isPre) {
    return { text: '系统预置角色，不可删除', width: '160' }
  }
  if (item.accountCount && item.accountCount > 0) {
    return { text: '该角色下已有成员，不可删除', width: '190' }
  }
}

const handleCreate = () => {
  router.push('role-create')
}

const handleClearFilters = () => {
  filterRoleName.value = ''
}

const handleSearchIconClick = (val: any) => {
  getRoleList()
}

const handleSelectionChange = (val: any) => {
  console.log('handleSelectionChange', val)
}

const handleFilterChange = (val: any) => {
  console.log('handleFilterChange', val)
}

const handleViewNumbers = (row: any) => {
  console.log('handleViewNumbers', row)
  roleEditId.value = row.id
  roleEditOpenType.value = 'info'
  userListVisible.value = true
}

const handleAllocation = (row: any) => {
  RoleDistributeRef.value?.show(row)
  console.log('handleAllocation', row)
}

const handleView = (row: any) => {
  console.log('handleView', row)
  roleEditId.value = row.id
  roleEditOpenType.value = 'info'
  roleEditDialogVisible.value = true
}

const handleEdit = (row: any) => {
  console.log('handleEdit', row)
  roleEditId.value = row.id
  roleEditOpenType.value = 'edit'
  roleEditDialogVisible.value = true
}

const handleDel = (row: any) => {
  console.log('handleDel', row)
  roleEditId.value = row.id
  warnDialogVisible.value = true
}

const handleDeleteConfirm = ({ type }: { type: string }) => {
  if (type === 'OK') {
    // role.deleteRole({ roleId: roleEditId.value }).then((res: any) => {
    //   warnDialogVisible.value = false
    //   getRoleList()
    // })
    access.delRolesRoleId(roleEditId.value).then((res: any) => {
      warnDialogVisible.value = false
      getRoleList()
    })
  }
}
const submitForm = () => {
  roleEditRef.value?.submitForm().then((res) => {
    if (res.code == 200) {
      ElMessage({
        message: '编辑成功',
        type: 'success'
      })
    }
    getRoleList()
    roleEditDialogVisible.value = false
  })
}
const handleComplete = () => {
  getRoleList()
}
const resetForm = () => {
  roleEditDialogVisible.value = false
}
const getRoleList = () => {
  const params: any = {
    pageParams:{
      page: 1,
      limit: 9999
    },
  }
  if (filterRoleName.value) {
    params['name'] = filterRoleName.value
  }
  // role.getRoleList(params).then((res: any) => {
  //   roleListData.value = res?.items || []
  //   console.log('roleListData', roleListData.value)
  // })
  access.postRolesOperationsList(params).then((res: any) => {
    roleListData.value = res.data || []
    console.log('roleListData', roleListData.value)
  })
}

getRoleList()

watch(
  () => filterRoleName.value,
  () => {
    console.log(filterRoleName.value)
    getRoleList()
  }
)
</script>
<style lang="less" scoped>
.role-container {
  display: flex;
  flex-direction: column;

  .container-filter {
    display: flex;
    justify-content: start;

    .filter-rolename {
      width: 320px;

      .iconfont {
        cursor: pointer;
      }
    }
  }

  .role-list {
    .item-name {
      display: flex;
    }
    .text-roletag {
      margin-left: 4px;
    }

    .item-description {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      overflow: hidden;
      line-height: 1.5;
      max-height: 3em;
    }
  }
}
:deep(.el-scrollbar__bar) {
      display: none;
}
:deep(.el-table.role-list .cell) {
  display: flex;
  align-items: center;
  overflow: hidden;
  padding-top: 12px;
  padding-bottom: 12px !important;
  line-height: 18px !important;
  height: 32px !important;
}
</style>
