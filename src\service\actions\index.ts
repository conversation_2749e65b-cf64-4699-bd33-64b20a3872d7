import http from '../http'
import api from './api'

const { get, post, put, deleteReq } = http
export default {
  postActionsList(data: any) {
    return post(api.actionsList, data)
  },
  postDesignCollaborationSpaces(data: any) {
    return post(api.designCollaborationSpaces, data)
  },
  getDesignCollaborationSpaces(data: any) {
    let { id, ...otherData } = data
    return get(api.designCollaborationSpace(id), otherData)
  },
  putDesignCollaborationSpaces(data: any) {
    let { id, ...otherData } = data
    return put(api.designCollaborationSpace(id), otherData)
  },
  delDesignCollaborationSpaces(data: any) {
    let { id, ...otherData } = data
    return deleteReq(api.designCollaborationSpace(id), otherData)
  },
  postUserDCSRelations(data: any) {
    // let { id, ...otherData } = data
    return post(api.userDCSRelations, data)
  },
  postBatchCreate(data: any) {
    // let { id, ...otherData } = data
    return post(api.batchCreate, data)
  },
  delUserDCSRelations(data: any) {
    let { id, ...otherData } = data
    return deleteReq(api.userDCSRelationsDel(id), otherData)
  },
  postPlt0UserActionsList(data: any) {
    return post(api.plt0UserActionsList, data)
  },
  postProjectSpaceList(data: any) {
    return post(api.projectSpaceList, data)
  },
  postDesignPartList(data: any) {
    let { classId, ...otherData } = data
    return post(api.designPartList(data.classId), otherData)
  },
  getDesignPartDetail(data: any) {
    return get(api.designPartDetail(data.classId, data.designPartId), data)
  },
  postDesignPartRecursiveGet(data: any) {
    let { id = 'plt0CADBOM', ...otherData } = data
    return post(api.designPartRecursiveGet(id), otherData)
  },
  batchGetSignatureUrl(data: any) {
    let { model, ...otherData } = data
    return post(api.batchGetSignatureUrl(data.model), otherData)
  },
  ActivityObjectHistory(data: any) {
    let { classId, ncid, childClass, ...otherData } = data
    return post(api.ActivityObjectHistory(classId, ncid, childClass), otherData)
  },
  getCADPartInfo(data: any) {
    let { classId, ncid, ...otherData } = data
    return get(api.cADPartInfo(classId, ncid), otherData)
  },
  postRecursiveGet(data: any) {
    let { model, CADPartId, ...otherData } = data
    return post(api.recursiveGet(model, CADPartId), otherData)
  }
}
