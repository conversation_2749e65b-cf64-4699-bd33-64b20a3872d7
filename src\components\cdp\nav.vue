<template>
  <aside class="nav">
    <el-menu :default-active="String(defaultActive)" class="el-menu-content" @click="handleOpen"
      @close="handleClose">
      <template v-for="(nav, index) in navList">
        <el-menu-item v-if="!nav.chilren" :index="String(index + 1)" :data-index="String(index + 1)"
          @click="navClick(nav)"
        >
          <i class="iconfont icon-index-home"></i>
          {{ nav.name }}
        </el-menu-item>
        <el-sub-menu v-else :index="String(index + 1)" :data-index="String(index + 1)"
          :class="{ 'is-active': nav.isActive }">
          <template #title>
            <img v-if="nav.icon" class="menu-icon" :src="`${nav.isActive ? nav.iconActive : nav.icon}`" alt="" />
            <span>{{ nav.name }}</span>
          </template>
          <el-menu-item v-for="(item, k) in nav.children" :index="`${(index + 1) * 100 + k}`"
            :data-index="String((index + 1) * 100 + k)"
            :class="{ 'is-active': item.isActive }"
            @click="navClick(item)"
          >
            {{ item.name }}
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
    <!-- 项目协作区 -->
    <div class="project-space" v-if="projects.length > 0">
      <div class="content-title">
        <h2>项目协作区</h2>
        <i class="iconfont icon-general-plus" @click="createProjectTeamCenter"></i>
      </div>
      <el-menu :default-active="String(defaultActiveProject)" class="menu-space menu-project-list">
        <el-menu-item
          v-for="(project, index) in projects"
          :key="`p_${index}`"
          :span="24"
           @click="handleViewProject(project, index)"
           class="menu-item-project"
           :class="{ 'is-active': project.isActive }">
          <div :class="`icon-box icon-style${getIconStyleNum(index+1)}`">
            <i class="iconfont icon-index-project1"></i>
          </div>
          <span class="project-name">{{ project.name }}</span>
          <!-- <el-tooltip
            class="box-item"
            effect="dark"
            :content="project.name"
            placement="top-start"
          > 
            <span class="project-name">{{ project.name }}</span>
          </el-tooltip> -->
        </el-menu-item>
      </el-menu>
    </div>
    <div v-else class="project-empty">
      <div class="project-view" @click="createProjectTeamCenter">
        <i class="iconfont icon-general-empty"></i>
        <div class="project-empty-title">
          <i class="iconfont icon-general-plus"></i>新建项目协作区
        </div>
      </div>
    </div>
    <el-menu class="menu-space">
      <el-menu-item class="menu-item" @click="goDesignPart">
        <div class="nenu-left">
          <i class="iconfont icon-index-part"></i>
          <span>设计零部件</span>
        </div>
        <i class="iconfont icon-direction-arrow-right"></i>
      </el-menu-item>
    </el-menu>
    <createProject
      v-model:visible="createProjectVisible"
      @handleCreated="handleCreateProject"
    ></createProject>
  </aside>
</template>

<script lang="ts" setup>
import { onMounted, watch, ref, watchEffect, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NavItem } from '../common/types'
import { nomalNavList, consoleNavsList } from '@/common/filters/nav'
// import { nomalNavList } from '@/common/filters/nav'
import { useUserInfoStore } from '@/store/user/userInfo'
import { useProjectInfo } from '@/store/projectSpace/projectInfo'
import createProject from '@/components/cdp/home/<USER>'
import { CollaborationZone } from '@/common/types/space'
import { CommonResponse } from '@/common/types/common'
import cdp from '@/service/cdp'

const userInfoStore = useUserInfoStore()
const projectInfoStore = useProjectInfo()
const router = useRouter()
const route = useRoute()

interface CollaborationZoneListData {
  data: Array<CollaborationZone>
  nextPage: any,
  responese: any
}

const navList = ref<Array<NavItem>>([])
const defaultActive = ref<number>(100)
const defaultActiveProject = ref<string>('0')

const createProjectVisible = ref(false)
const projects = ref<Array<CollaborationZone>>([])

const getIconStyleNum = (index: number) => {
  return index % 6 === 0 ? 6 : index % 6
}
const handleViewProject = (item: CollaborationZone, index: number) => {
  // projectInfoStore.setActiveProject(item)
  defaultActiveProject.value = `p_${index}`
  projects.value.forEach((project: CollaborationZone, k: number) => {
    project.isActive = false
    if (item.ncid === project.ncid) {
      projectInfoStore.setActiveProject(item)
      project.isActive = true
      defaultActive.value = -1
    }
  })
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

const changeNavActive = (currentPath: string) => {
  const endPath = currentPath.split('/').pop()
  let subIndex = 0
  console.log('navList',navList)
  navList.value.forEach((item: NavItem, index: number) => {
    const temp = item
    let isSubActive = false
    temp.isActive = false
    subIndex = 0
    if (temp.highlight?.some((path: string) => path === endPath)) {
      temp.isActive = true
      defaultActiveProject.value = '-1'
      console.log('temp==>', endPath)
    }
    temp.children?.forEach((childItem: NavItem, k: number) => {
      const subTemp = childItem
      subTemp.isActive = false
      // subIndex = 0
      // temp.isActive = false
      // subTemp.isActive = currentPath.includes(subTemp.path)
      if (subTemp.highlight?.some((path: string) => path === endPath)) {
        subTemp.isActive = true
        temp.isActive = true
        isSubActive = true
        subIndex = k
        defaultActiveProject.value = '-1'
        console.log('subtemp==>', endPath, subIndex)
      }
      return subTemp
    })
    // TODO： 菜单调试注释，刷新问题
    if (temp.isActive) {
      if (!temp.children) {
        defaultActive.value = index + 1
      } else {
        defaultActive.value = (index + 1) * 100 + subIndex
      }
      
      defaultActiveProject.value = '-1'
      projectInfoStore.setActiveProject({})
    }
    return temp
  })
}


const refreshNav = () => {
  const consoleNavs = userInfoStore.getUserNavList
  console.log('consoleNavs', consoleNavs)
  // navList.value = route.path.indexOf('home') > -1 ? consoleNavs : nomalNavList
  // 本期只有通用菜单，不需要权限限制
  navList.value = nomalNavList
  changeNavActive(router.currentRoute.value.path)
}

// 创建项目协作区
const createProjectTeamCenter = () => {
  console.log('createProjectTeamCenter')
  createProjectVisible.value = true
}
// 查看项目 协作区
const handleOpen = (item: CollaborationZone) => {
  console.log('item', item)
}
const handleCreateProject = (data: any) => {
  console.log('handleCreateProject==更新列表', data)
  getProjectList()
}

const getProjectList = async () => {
  cdp.postPlt0ProjectList({})
    .then((res:  CommonResponse<Array<CollaborationZone>> | any) => {
      const data: Array<CollaborationZone> = res.data
      projects.value = data
      projectInfoStore.setProjectList(data)
      // Handle the response data here
    })
    .catch((error) => {
      console.error('API error:', error)
    })
    .finally(() => {
    })
}

const goDesignPart = () => {
  router.push({ name: 'DesignPart' })
}

watch(
  () => router.currentRoute.value.path,
  () => {
    refreshNav()
  }
)

const navClick = (e: NavItem) => {
  console.log('navClick===>', e.path)
  projectInfoStore.setActiveProject({})
  router.push(e.path)
}

onMounted(() => {
  refreshNav()
  getProjectList()
})

</script>

<style scoped lang="less">
@import '@/assets/css/fonts/iconfont.css';

.nav {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 12px 20px;

  .icon-index-home {
    color: @text-color-5;
    font-size: 22px;
    margin-right: 12px;
  }

  .nav-item {
    display: flex;
    box-sizing: border-box;
    width: 100%;
    height: 44px;
    line-height: 44px;
    padding: 0 12px;
    border-radius: 5px;
    cursor: pointer;
    margin: 2px 0;
    font-size: @text-1;

    &.active,
    &:hover {
      color: @primary-6;
      font-weight: @font-weight-2;
      background-color: @other-color-2;

      .iconfont {
        color: @primary-6;
        font-weight: @font-weight-1;
      }
    }
  }

  .el-sub-menu.is-active {
    color: @text-color-1;
    font-weight: @font-weight-2;

    .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }

  .el-menu-item.is-active {
    color: @text-color-1;
    font-weight: @font-weight-2;
    background-color: @other-color-2;

    .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }
}
</style>
<style lang="less">
@import '@/assets/css/fonts/iconfont.css';

.nav {
  .nav-content {
    flex: 1;
  }
  .el-sub-menu.is-active {
    .el-sub-menu__title {
      color: @primary-6;
      font-weight: @font-weight-2;
    }

    .el-menu-content .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }

  .el-menu-item.is-active {
    color: @primary-6;
    font-weight: @font-weight-2;
    background-color: @other-color-2;

    .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }

  .project-space {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 12px 0;
    overflow-y: auto;
    border-top: solid 1px @border-color-1;
    border-bottom: solid 1px @border-color-1;
    .content-title {
      padding: 12px 12px 0 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h2 {
        font-size: @text-0;
        color: @text-color-4;
        margin-bottom: 0;
      }
      .icon-general-plus {
        font-size: 18px;
        color: @text-color-3;
        cursor: pointer;
      }
    }
    .menu-space {
      margin-bottom: 10px;
      // display: flex;
      // flex-wrap: wrap;
      height: 48px;
      // gap: 10px;
      .el-menu-item {
        padding-left: 8px !important;
        padding-right: 8px !important;
        width: 100%;
        height: 40px;
        display: flex;
        font-size: @text-1;
        color: @text-color-1;
        align-items: center;
        // justify-content: center;
        border-radius: 5px;
        cursor: pointer;
        &:hover {
          background-color: @other-color-1;
        }
        &.is-active {
          background-color: @other-color-2;
        }
      }
      .icon-box {
        display: flex;
        width: 22px;
        height: 22px;
        border-radius: 22px;
        line-height: 22px;
        .iconfont {
          font-size: 16px;
        }
      }
    }
    
    .menu-project-list {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      .icon-box {
        display: flex;
        justify-items: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 24px;
        line-height: 24px;
        margin-right: 8px;
        .icon-index-project1 {
          width: 24px;
          height: 24px;
          text-align: center;
          font-size: 14px;
          color: #fff !important;
          margin: 0 auto;
        }
      }
      .icon-style1{
        background: @seq-color-1;
      }
      .icon-style2{
        background: @seq-color-2;
      }
      .icon-style3{
        background: @seq-color-3;
      }
      .icon-style4{
        background: @seq-color-4;
      }
      .icon-style5{
        background: @seq-color-5;
      }
      .icon-style6{
        background: @seq-color-6;
      }
    }
    .project-name {
      font-size: @text-1;
      color: @text-color-1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 160px;
    }
  }
  .project-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 12px;
    border-top: solid 1px @border-color-1;
    border-bottom: solid 1px @border-color-1;
    width: 100%;
    height: 100%;
    .project-view {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 200px;
      height: 112px;
      border-radius: @border-radius-3;
      border: 1px dashed @text-color-6;
      cursor: pointer;
      color: @link-6;
      &:hover{
        background: @primary-1;
        border-color: @link-6;
      }
    }
    .project-empty-title {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .icon-general-plus {
        margin-right: 4px;
        font-size: @text-1;
      }
    }
    .icon-general-empty {
      font-size: 28px;
      color: @text-color-6;
    }
  }
  .menu-item {
    display: flex;
    justify-content: space-between;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
</style>
