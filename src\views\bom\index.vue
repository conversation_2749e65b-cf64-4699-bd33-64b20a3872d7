<template>
  <div class="w-0 flex-1 p-5 bg-[#ECEEF3] bom-tree">
    <div class="p-[16px] rounded-[8px] bg-white h-full overflow-auto">
      <div class="mb-[16px] flex items-center gap-3">
        <el-button type="primary">
          <i class="iconfont icon-neuecax1 mr-[4px]"></i>
          在NeueCAX中打开
        </el-button>
        <div class="flex items-center gap-2">
          <el-button
            v-if="isExpanding"
            size="small"
            type="primary"
            link
            @click="expandAllNodes"
          >
            <i class="iconfont icon-zhankai mr-1"></i>
            展开
          </el-button>
          <el-button v-else size="small" type="primary" link @click="collapseAllNodes">
            <i class="iconfont icon-shouqi mr-1"></i>
            收起
          </el-button>
        </div>
      </div>
      <ne-tree-table
        ref="treeTableRef"
        :data="dataList"
        :columns="tableColumns"
        row-key="ncid"
        :treeProps="{ children: 'cadboms', hasChildren: 'hasChildren' }"
      >
        <template #partNo="{ row }">
          <div class="flex justify-between w-[100%] tree-item">
            <div class="flex">
              <cax-icon :type="row.partType"></cax-icon>
              <span class="ml-[4px]">{{ row.partNo }}</span>
            </div>
            <el-tag
              :disabled="row.convertStatus !== ConvertStatusEmu.SUCCESS"
              class="tag-3d cursor-pointer"
              v-if="row.idi3DLightModelId"
              @click="openBom"
              size="small"
              effect="plain"
              type="info"
              style="display: none"
              >3D</el-tag
            >
          </div>
        </template>
        <template #modifiedBy="{ row }">
          <ne-avatar :name="row.modifiedBy?.name" v-if="row.modifiedBy?.name" />
          <span v-else>-</span>
        </template>
        <template #instanceName="{ row }">
          {{ row.instanceName || '-' }}
        </template>
        <template #version="{ row }">
          {{ row.version ? `v${row.version}` : '-' }}
          <el-tag class="ml-2" v-if="row.latestVersion" type="primary">最新版</el-tag>
        </template>
        <template #status="{ row }">
          <el-tag :type="StatusEmu[row.status]?.type">
            {{ StatusEmu[row.status]?.text || row.status }}
          </el-tag>
        </template>
        <template #modifiedAt="{ row }">
          {{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </ne-tree-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from '@vue/runtime-core'
import { StatusEmu, ConvertStatusEmu } from '@/constant'
import actions from '@/service/actions'
import dayjs from 'dayjs'
import CaxIcon from '@/components/common/cax-icon.vue'
import { useRoute } from 'vue-router'

// 表单引用
let dataList = ref<any[]>([]) // 改为直接存储根节点数据
let treeTableRef = ref()
const isExpanding = ref(true) // 是否正在执行全部展开操作

let openBom = (row: any) => {
  console.log(row)
}
const tableColumns = [
  { prop: 'partNo', label: '编号' },
  { prop: 'partName', label: '零部件名称' },
  { prop: 'instanceName', label: '实例名称' },
  { prop: 'version', label: '版本号' },
  { prop: 'status', label: '状态' },
  { prop: 'modifiedBy', label: '修改人' },
  { prop: 'modifiedAt', label: '修改时间', sortable: true }
]
const expand = (f: boolean) => {
  const expandRecursive = (nodes) => {
    nodes.forEach((node) => {
      treeTableRef.value.toggleRowExpansion(node, f)
      if (node.cadboms) {
        expandRecursive(node.cadboms)
      }
    })
  }
  expandRecursive(dataList.value)
}
const expandAllNodes = async () => {
  expand(true)
  isExpanding.value = false
}
const collapseAllNodes = () => {
  expand(false)
  isExpanding.value = true
}
function traverseTree(nodes: any[]) {
  for (let i = 0; i < nodes.length; i++) {
    nodes[i] = { ...nodes[i].target, ...nodes[i] }
    if (nodes[i].cadboms && nodes[i].cadboms.length > 0) {
      traverseTree(nodes[i].cadboms)
    }
  }
}
const route = useRoute()
const ncid = route.query.ncid
let getInit = async () => {
  let resRecursive = await actions.postRecursiveGet({
    model: 'plt0NeueCADPart',
    CADPartId: ncid
  })
  traverseTree([resRecursive.data])
  dataList.value = [resRecursive.data]
}
onMounted(async () => {
  getInit()
})
</script>

<style lang="less">
.bom-tree {
  .el-table .cell {
    display: flex;
    align-items: center;
  }
  .tree-item:hover {
    .tag-3d {
      border-color: #1856eb;
      border-radius: 4px;
      color: #1856eb;
      display: flex !important;
    }
  }
}
</style>
