<template>
    <div class="right-menu" v-show="showMenu">
        <div>
            <div 
                v-for="(menu, index) in props.menus" 
                :key="index"
            >
                <div class="menu"  v-if="menu.children" 
                    @mouseover="openSubMenu(index)" 
                    @mouseleave="closeSubMenu(index)"
                    @click="(event: MouseEvent) => event.stopPropagation()"
                >
                    {{ menu.name }}
                    <i>></i>
                    <div :class="`sub-menu display-${ index }`">
                        <div
                            class="sub-menu-item" 
                            v-for="(child, index) in menu.children" 
                            @click="(event: MouseEvent) => child.action(event)" 
                            :key="index"
                        >
                            {{ child.name }}
                        </div>
                    </div>
                </div>
                <div class="menu" v-else @click="(event: MouseEvent) => menu.action(event)" >
                    {{ menu.name }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { PropType, ref, watch, defineExpose  } from 'vue';

const props = defineProps({
    menus: {
        type: Array as PropType<any[]>,
        default: () => []
    }
})

 //获取窗口内部大小
var width = window.innerWidth;
var height = window.innerHeight;

//用于重新计算窗口大小
window.addEventListener('resize', ()=>{
    width = window.innerWidth;
    height = window.innerHeight;
})


const showMenu = ref<boolean>(false)

// 打开右键菜单
const open = (e: MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    showMenu.value = false
    setPosition(e)
    showMenu.value = true
    document.addEventListener('click', close)
}

// 设置弹窗位置
const setPosition = (e: MouseEvent) => {
    let menu = document.querySelector('.right-menu') as HTMLElement
    if (menu) {
        let x = e.clientX
        let y = e.clientY
        if (x + menu.offsetWidth > width) {
            x = width - menu.offsetWidth
        }
        if (y + menu.offsetHeight > height) {
            y = height - menu.offsetHeight
        }
        menu.style.left = x + 'px'
        menu.style.top = y + 'px'
    }
}

const close= ()=> {
    showMenu.value = false
}

// 打开关闭二级菜单
const openSubMenu = (index: number) => {
    let subMenu = document.querySelector(`.display-${index}`) as HTMLElement
    subMenu.style.display = 'block'
}
const closeSubMenu = (index: number) => {
    let subMenu = document.querySelector(`.display-${index}`) as HTMLElement
    subMenu.style.display = 'none'
}


defineExpose({
    open, close
})

</script>

<style scoped lang="less">
.right-menu {
    z-index: 9999;
    width: 200px;
    background-color: #fafafa;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    & > div {
        width: 100%;
        height: 100%;
    }
}
.menu {
    width: 100%;
    padding: 10px;
    cursor: pointer;
    position: relative;
    text-align: left;
    &:hover {
        background-color: #f0f0f0;
    }
}
.sub-menu {
    width: 100px;
    position: absolute;
    top: 0;
    left: 100%;
    background-color: #fafafa;
    display: none;
    //设置透明边框
    border-left: 2px solid transparent;
    //默认情况下，背景的颜色会延伸至边框下层，设置background-clip:padding-box来改变背景的默认行为
    background-clip: padding-box;
    .sub-menu-item {
        cursor: pointer;
        padding: 10px;
        &:hover {
        background-color: #f0f0f0;
    }
    }
}


</style>