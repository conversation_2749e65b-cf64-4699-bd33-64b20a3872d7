version: '1.0'
name: branch-pipeline
displayName: BranchPipeline
triggers:
  trigger: auto
  push:
    branches:
      precise:
        - develop
  pr:
    branches:
      prefix:
        - develop
      include:
        - feature\/[a-zA-Z0-9_]+_[a-zA-Z0-9]
stages:
  - name: stage-78ee20f3
    displayName: Static Analysis
    strategy: naturally
    trigger: auto
    steps:
      - step: build@nodejs
        name: execute_by_docker
        displayName: Static Code Analysis
        nodeVersion: 21.5.0
        commands:
          - npm install && rm -rf ./dist && npm run build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./dist
        strategy: {}
  - name: stage-02cf352b
    displayName: Compute
    strategy: naturally
    trigger: auto
    steps:
      - step: publish@release_artifacts
        name: execute_by_docker
        displayName: Build Galaxy Code
        dependArtifact: output
        version: *******
        autoIncrement: true
        strategy: {}
  - name: stage-bd4ad78c
    displayName: Package
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: build@docker
        name: build_pacakge_docker
        displayName: Publish Artifact
        type: account
        repository: registry.cn-hangzhou.aliyuncs.com
        username: NY_Cloud_03_001@neuetech
        password: Ny@********
        tag: neue_galaxy/galaxy_dev:neue-galaxy-app-0.1-${GITEE_BRANCH}.${GITEE_PIPELINE_BUILD_NUMBER}
        dockerfile: ./Dockerfile
        context: ''
        artifacts: []
        isCache: false
        parameter: {}
        notify: []
        strategy:
          retry: '0'
