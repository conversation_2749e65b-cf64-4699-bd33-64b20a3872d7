version: '1.0'
name: pipeline-app-ci-build
displayName: pipeline-app-ci-build
triggers:
  trigger: auto
  push:
    branches:
      prefix:
        - develop
        - master
  pr:
    branches:
      prefix:
        - develop
        - feature
        - master
stages:
  - name: stage-78ee20f3
    displayName: Static Analysis
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: execute@docker
        name: execute_by_docker
        displayName: Static Code Analysis
        certificate: ''
        image: registry.cn-hangzhou.aliyuncs.com/neue_galaxy/hub_tools_mirrors:ci_mvn_git_tools
        command:
          - '# 请在此输入您想执行的脚本'
          - echo 'Code Static analysis'
        notify: []
        strategy:
          retry: '0'
  - name: stage-02cf352b
    displayName: Compute
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: execute@docker
        name: execute_by_docker
        displayName: Build Galaxy Code
        certificate: ''
        image: registry.cn-hangzhou.aliyuncs.com/neue_galaxy/hub_tools_mirrors:ci_mvn_git_tools
        command:
          - '# 请在此输入您想执行的脚本'
          - echo 'npm build job'
        notify: []
        strategy:
          retry: '0'
  - name: stage-bd4ad78c
    displayName: Package
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: build@docker
        name: build_pacakge_docker
        displayName: Publish Artifact
        type: account
        repository: registry.cn-hangzhou.aliyuncs.com
        username: NY_Cloud_03_001@neuetech
        password: Ny@********
        tag: neue_galaxy/galaxy_dev:neue-cdp-0.1-${GITEE_BRANCH}.${GITEE_PIPELINE_BUILD_NUMBER}
        dockerfile: ./Dockerfile
        context: ''
        artifacts: []
        isCache: false
        parameter: {}
        notify: []
        strategy:
          retry: '0'
