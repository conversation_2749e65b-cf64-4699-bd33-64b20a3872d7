<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-10-18 14:36:37
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-05 09:42:48
 * @FilePath: /neue-cloud-vdi/src/components/user/instance.vue
-->
<template>
  <el-drawer id="userInstanceDrawer" class="user-instance-drawer" v-model="props.visible" :before-close="handleClose"
  :close-on-click-modal="false"
    size="980">
    <template #header="{ close, titleId, titleClass }">
      <div :id="titleId" :class="`deploy-title ${titleClass}`">管理资源</div>
    </template>
    <div class="deploy-main">
      <div class="deploy-banner">{{ userDetail.user }}</div>
      <div class="deploy-content">
        <ny-transfer v-model="rightValue"
        filterable
        :titles="['可分配资源', '已分配资源']"
        :data="dataList"
          @change="handleChange">
        </ny-transfer>
      </div>
      <div class="deploy-footer">
        <el-button id="saveBtn" type="primary" @click="handleSave()">确定</el-button>
        <el-button id="cancelBtn" @click="handleCancel">取消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue'
import { InstanceDetail } from '@/common/types/console/instance'
import { ImageDetail } from '@/common/types/console/image'
import { UserDetail } from '@/common/types/console/user'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['update:visible', 'handleComplete'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  activeDetail: {
    type: Object as PropType<ImageDetail>,
    default: () => ({})
  }
})

// const instanceSelection = ref<Array<InstanceDetail>>([])
// const deployStatusVisible = ref(false)
// const activeInstance = ref<any>({})
const rightValue = ref([])
const userDetail = ref<UserDetail>({
  user: '',
  phone: ''
})

const dataList = ref<Array<Option>>([])
const directionParam = ref('')
const movedKeysParam = ref<Array<any>>([])

interface Option {
  key: number
  label: string
  disabled: boolean
}

const generateData = (): Option[] => {
  const data: Option[] = []
  for (let i = 1; i <= 15; i++) {
    data.push({
      key: i,
      label: `Option ${i}`,
      disabled: i % 4 === 0
    })
  }
  return data
}

const data = ref(generateData())

const handleChange = (
  value: number[] | string[],
  direction: 'left' | 'right',
  movedKeys: string[] | number[]
) => {
  directionParam.value = direction
  movedKeysParam.value = movedKeys
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSave = () => {
  console.log('handleSave', directionParam.value === 'left', movedKeysParam.value)
  if (directionParam.value === 'left') {
    vdi.postOperationsDetachAccount({ userId: userDetail.value.userId, ids: movedKeysParam.value }).then(() => {
      ElMessage.success('回收成功')
      emit('handleComplete')
      handleClose()
    })
  } else if (directionParam.value === 'right') {
    vdi.postOperationsBindAccount({ userId: userDetail.value.userId, vdiIds: movedKeysParam.value }).then(() => {
      ElMessage.success('分配成功')
      emit('handleComplete')
      handleClose()
    })
  } else {
    handleClose()
  }
}

const handleCancel = () => {
  handleClose()
}
const getInstance = () => {
  vdi.postOperationsList({}).then((res: any) => {
    dataList.value = (res?.data || []).map((item: { vdiId: any })=>({...item,id:item.vdiId})).filter((item: any) => {
      return (
        item.deployStatus === 0 &&
        (item.userId === -1 || item.userId === userDetail?.value?.userId)
      )
    }).map((item: { id: any; name: any }) => {
      return {
        key: item.id,
        label: item.name,
        disabled: false
      }
    })
  })
}

const getInstanceByUser = () => {
  console.log(userDetail)
  vdi.postOperationsList({
      filter: JSON.stringify({ userId: userDetail?.value?.userId })
    })
    .then((res: any) => {
      const alloccatedList = res?.data || []
      rightValue.value = alloccatedList.map((item: { vdiId: any })=>({...item,id:item.vdiId})).map((item: { id: any }) => item.id)
      console.log('rightList', JSON.stringify(rightValue.value), dataList.value)
    })
}

const show = (user: any) => {
  userDetail.value = user
  console.log('userDetail', userDetail.value)
  emit('update:visible', true)
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      getInstance()
      getInstanceByUser()
    } else {
      rightValue.value = []
      dataList.value = []
    }
  }
)

defineExpose({
  show
})
</script>
<style lang="less">
.user-instance-drawer {
  text-align: left;

  .deploy-title {
    color: @text-color-1 !important;
    font-size: @text-3;
    font-weight: @font-weight-3;
  }

  .deploy-main {
    height: calc(100%);
    display: flex;
    flex-direction: column;
  }

  .deploy-banner {
    height: 32px;
    line-height: 32px;
    color: @text-color-1;
    font-size: @text-1;
    font-weight: @font-weight-3;
    // padding: 0 20px;
  }

  .deploy-content {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .table-control {
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
  }

  .deploy-footer {
    height: 72px;
    display: flex;
    justify-content: start;
    align-items: center;
  }
}
</style>
