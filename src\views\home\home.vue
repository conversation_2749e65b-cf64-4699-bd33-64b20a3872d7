<template>
  <PageMain :title="pageTitle">
    <template #header-slot>
      <el-button id="console-btn-image" type="primary" @click="handleCreateSpace()" >新建空间</el-button>
      <CreateSpace v-model:visible="createSpaceVisible" @handleCreated="handleCreated" :activeDetail="projectDetail" />
    </template>
    <template #content>
      <div v-if="spaces.length > 0" class="home-space-sort">
        <span>创建时间</span>
        <div class="home-space-sort-icon">
          <img v-if="isReverseSort" src="@/assets/images/home/<USER>" @click="handleReverse" alt="">
          <img v-else src="@/assets/images/home/<USER>" @click="handleReverse" alt="">
        </div>
      </div>
      <div class="home-space-list">
        <div v-for="(item, index) in spaces" :key="index" class="space-card" @click="handleSpaceClick(item)">
          <div class="space-img">
            <img src="@/assets/images/space-img.png" alt="space 1" class="space-image">
          </div>
          <div class="space-title">{{ item.name }}</div>
          <div class="space-date">创建于 {{ dayjs(item.createdAt).format('YYYY-MM-DD') }} {{ dayjs(item.createdAt).format('HH:mm:ss') }}</div>
        </div>
      </div>
      <div v-if="spaces.length === 0" class="home-space-empty">
        <img src="@/assets/images/home/<USER>" alt="space 1" class="space-image" @click="handleCreateSpace()">
        <div class="home-space-empty-title">您还没有协同空间，请新建</div>
        <div class="home-space-empty-desc">
          <el-button type="primary" @click="handleCreateSpace()"><i class="iconfont icon-general-plus"></i>新建空间</el-button>
        </div>
      </div>
    </template> 
  </PageMain>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import PageMain from '../home/<USER>'
import CreateSpace from '@/components/cdp/home/<USER>'
import dayjs from 'dayjs'
import cdp from '@/service/cdp'
import { CollaborationSpace } from '@/common/types/space'
import { Condition, PageParams } from '@/common/types/common'
import { useProjectInfo } from '@/store/projectSpace/projectInfo'

const router = useRouter()
const route = useRoute()
const projectInfoStore = useProjectInfo()

const isReverseSort = ref(false)
const isLoadingList = ref(false)
const createSpaceVisible = ref(false)
const spaces = ref<Array<CollaborationSpace>>([])
interface reqParams {
  pageParams?: PageParams
  condition?: Condition
}

const projectDetail = computed(() => {
  return projectInfoStore.getActiveProject
})

const pageTitle = computed(() => {
  return (projectInfoStore.getActiveProject && projectInfoStore.getActiveProject.name) ? projectInfoStore.getActiveProject.name : '首页'
})
const handleCreateSpace = () => {
  createSpaceVisible.value = true
}
const handleReverse = () => {
  if (isLoadingList.value) return
  isReverseSort.value = !isReverseSort.value
}

const handleSpaceClick = (item: CollaborationSpace) => {
  router.push({
    name: 'CadDetail',
    query: {
      ncid: item.ncid,
      name: item.name,
      // type: 'space',
    }
  })
}

const handleCreated = () => {
  getSpaceList()
}

const getSpaceList = async () => {
  isLoadingList.value = true
  const params: reqParams = {
    pageParams: {
      page: '1',
      limit: 9999,
      sorts: [{
        sortBy: 'createdAt',
        sortOrder: isReverseSort.value ? 'DESC' : 'ASC'
      }]
    },
    
  }
  if (projectInfoStore.getActiveProject && projectInfoStore.getActiveProject.ncid) {
    params.condition =  {
      logic: 'AND',
      ignoreCase: true,
      statements: [{
        field: 'projectSpace.ncid',
        value: [projectInfoStore.getActiveProject.ncid],
        operator: '='
      }]
    }
  } else {
    params.condition && delete params.condition
  }
  cdp.postBusinessobjectList(params)
    .then((res) => {
      spaces.value = res.data
    })
    .catch((error) => {
      console.error('API error:', error);
    })
    .finally(() => {
      isLoadingList.value = false
    })
}
watch(isReverseSort, (newVal) => {
  console.log('Sort order changed:', newVal);
  getSpaceList()
})
watch (
  () => projectInfoStore.getActiveProject,
  (newVal) => {
    getSpaceList()
  },
  { immediate: true }
)

getSpaceList()
</script>
<style scoped lang="less">
.home-space-sort {
  display: flex;
  margin-left: 8px;
  font-size: @text-1;
  align-items: center;
  span {
    color: @text-color-3;
  }
  .home-space-sort-icon {
    margin-left: 6px;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
    }
  }
}
.home-space-list {
  position: relative;
  width: 100%;
  display: grid;
  margin: 0;
  padding: 0;
  column-gap: 16px;
  overflow-y: auto;
  // grid-template-columns: repeat(auto-fit, minmax(290px, 1fr));
  .space-card {
    // min-width: 290px;
    display: inline-block;
    margin-top: 16px;
    padding: 8px; 
    cursor: pointer;
    .space-img {
      width: 100%;
      height: 226px;
      overflow: hidden;
      background: @bg-color-3;
      border-radius:  @border-radius-3;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        max-width: 80%;
        max-height: 80%;
        object-fit: contain; 
      }
    }
    .space-title {
      padding: 0 20px;
      margin-top: 8px;
      line-height: 24px;
      font-size: @text-2;
      font-weight: bold;
      text-align: left;
      color: @text-color-1;
    }
    .space-date {
      padding: 0 20px;
      font-size: @text-1;
      line-height: 22px;
      color: #888;
      text-align: left;
    }
  }
}
.home-space-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  .space-image {
    margin-bottom: 20px;
    cursor: pointer;
  }
  .home-space-empty-title {
    font-size: @text-1;
    color: @text-color-5;
    margin-bottom: 8px;
  }
  .home-space-empty-desc {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: @text-1;
    color: @text-color-3;
    .icon-general-plus {
      margin-right: 4px;
      font-size: @text-1;
    }
  }
}
</style>

<style lang="less">
// @media (min-width: 1602px) {
//   .home-space-list {
//     grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
//   }
// }

// @media (max-width: 1601px) {
//   .home-space-list {
//     grid-template-columns: repeat(3, 1fr);
//   }
// }

@media (min-width: 1602px) {
  .home-space-list {
    grid-template-columns: repeat(auto-fill, minmax(388px, 1fr));
  }
}

/* 中等屏幕：宽度在1277px到1601px之间 */
@media (max-width: 1601px) and (min-width: 1277px) {
  .home-space-list {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 小屏幕：宽度小于1277px */
@media (max-width: 1276px) {
  .home-space-list {
    grid-template-columns: repeat(2, 1fr);
  }
}


</style>