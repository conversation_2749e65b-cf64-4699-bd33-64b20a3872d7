<template>
  <div class="flex items-center gap-5 mb-6 pl-7 border-b border-[#D9D9D9]">
    <div
      v-for="tab in tabs"
      :key="tab.name"
      @click="activeTab = tab.name"
      class="cursor-pointer py-3 border-b-2 border-transparent text-[14px] text-[#1E293B] font-[600]"
      :class="{ 'active-item': activeTab === tab.name }"
    >
      {{ tab.label }}
    </div>
  </div>
  <div class="px-[20px]">
    <PropertyInfo v-if="activeTab === 'property'" :partId="partId" />
    <RelatedDocs v-if="activeTab === 'docs'" :partId="partId" />
    <HistoryRecords v-if="activeTab === 'history'" :partId="partId" />
    <UsedBy v-if="activeTab === 'usedBy'" :ncid="partId" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, onMounted } from 'vue'
import PropertyInfo from './part-detail/property-info.vue'
import RelatedDocs from './part-detail/related-docs.vue'
import HistoryRecords from './part-detail/history-records.vue'
import UsedBy from './part-detail/used-by.vue'

// 定义组件属性
const props = defineProps({
  partId: {
    type: String,
    default: ''
  }
})
console.log(props)
// 标签页配置
const tabs = [
  { name: 'property', label: '属性信息' },
  { name: 'docs', label: '关联文档' },
  { name: 'history', label: '历史记录' },
  { name: 'usedBy', label: '被用于' }
]
// 当前激活的标签页
const activeTab = ref('property')

// 加载零部件数据
onMounted(() => {
  // 这里可以根据 props.partId 从 API 获取零部件详细信息
  console.log('加载零部件详情，ID:', props.partId)
})
</script>

<style scoped lang="less">
.active-item {
  color: #1856eb;
  border-bottom: 2px solid #1856eb;
}
</style>
