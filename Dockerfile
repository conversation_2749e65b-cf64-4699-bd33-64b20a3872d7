# 使用Node.js的官方镜像作为构建阶段的基础镜像
FROM  registry.cn-hangzhou.aliyuncs.com/neue_galaxy/node:21.5-alpine-with-pnpm AS build

# 设置工作目录
WORKDIR /home/<USER>

# 复制package.json和package-lock.json到工作目录
COPY package*.json ./

# 复制npmrc到工作目录
COPY .npmrc ./
# 列出文件
RUN ls -la
# 列出文件
RUN ls -la /home/<USER>

RUN cat .npmrc
# 设置镜像
#RUN pnpm config set registry https://registry.npmmirror.com
# RUN pnpm config set registry http://*************:8081/repository/npm-hosted/

# ENV NPM_TOKEN=NpmToken.************************************
# RUN npm config set //*************:8081/repository/npm-public/:_authToken $NPM_TOKEN
# 安装依赖
RUN pnpm install


# 复制项目文件到工作目录
COPY . .


# 构建项目
RUN pnpm run build

# 使用Nginx作为生产阶段的基础镜像
FROM registry.cn-hangzhou.aliyuncs.com/neue_galaxy/nginx:1.27.0-alpine3.19

# 将构建阶段的输出复制到Nginx的html目录
COPY --from=build /home/<USER>/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/nginx.conf

# 暴露端口
EXPOSE 8080

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]


