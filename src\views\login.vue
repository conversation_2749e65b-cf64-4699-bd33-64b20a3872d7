<template>
  <div class="login-container">
    <!-- <div class="login-content"> -->
    <div class="login-desc">
      <div class="login-desc-content">
        <div class="login-logo">
          <img src="@/assets/images/login/logo.svg" width="90" alt="" />
        </div>
        <div class="login-title">诺源CDP</div>
        <div class="login-sub-title">Access to thousands of designer</div>
        <div class="login-sub-title">sources and templates</div>
        <div class="login-version">NEUE CDP V1.0.1</div>
        <div class="login-copy">Copyright @ 2025-2026 NEUE. All Rights Reserved</div>
      </div>
    </div>
    <div class="login-box">
      <div class="login-box-content">
        <div class="login-box-title">登录</div>
        <div class="login-box-form">
          <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef">
            <el-form-item prop="name">
              <el-input v-model="ruleForm.name" style="width: 400px; height: 48px; border-radius: 12px"
                placeholder="请输入邮箱地址" @keyup.enter="doLogin(ruleFormRef)">
                <template #prefix>
                  <i class="iconfont icon-general-user"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="pass">
              <el-input v-model="ruleForm.pass" type="password"
                style="width: 400px; height: 48px; border-radius: 12px !important" placeholder="请输入密码"
                @keyup.enter="doLogin(ruleFormRef)" show-password>
                <template #prefix>
                  <i class="iconfont icon-Vector"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" class="login-button" @click="doLogin(ruleFormRef)">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <!-- </div> -->
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { account as accountService } from '@/service'
import { validateEmail } from '@/utils/validate'
import storage from '@/utils/storage'
// import { setCookie } from '@/utils/cookie'
import cookie from 'cookiejs'

const router = useRouter()
const route = useRoute()


const redirect = route.query.redirect
const organize = import.meta.env.VITE_ORGANIZE

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  name: '',
  pass: ''
})

const validatePass = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    callback()
  }
}

const validateEmailStr = (rule: any, value: any, callback: any) => {
  console.log('validateEmailStr', value, ruleForm.name, validateEmail(value))
  if (value === '') {
    callback(new Error('请输入邮箱地址'))
  } else if (!validateEmail(value)) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules<typeof ruleForm>>({
  pass: [{ validator: validatePass, trigger: 'blur' }],
  name: [{ validator: validateEmailStr, trigger: 'blur' }]
})

const doLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) {
    return
  }
  ruleFormRef.value?.validate(async (valid, fields) => {
    if (valid) {
      const res = await accountService.postOperationsLogin({
        identify: ruleForm.name?.toString(),
        passInfo: ruleForm.pass?.toString()
      })
      let { data } = res
      if (data.token) {
        storage.set('token', data.token)
        storage.set('uid', data.userList[0].ncid)
        storage.set('accountNcid', data.userList[0].account.ncid)
        // 存储到cookie
        // 判断是否前往cax页面
        redirect ? router.push({ path: '/cax' }) : router.push({ path: '/home' })
        return
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style scoped lang="less">
:deep(.el-form-item__content) {
  display: flex;
  flex-direction: column;
  align-items: center;

  // .el-input__inner {
  //   font-size: 16px !important;
  // }
  i {
    font-size: 20px;
    color: @text-color-3;
  }
}

.login-box-form {
  width: 400px;
  margin: 0 auto;

  .el-button--primary {
    border: 0 none !important;
  }

  .el-input__wrapper {
    border-radius: @border-radius-4 !important;
  }
}

.login-container {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/login/bg5.webp');
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  // justify-content: ;
  // align-items: center;
  .login-desc {
    margin-left: 14.18vw;
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;

    // width:45%;
    .login-desc-content {
      width: 400px;
      color: #fff;
    }

    .login-logo {
      width: 200px;
      margin-top: 20px;
      margin-bottom: 40px;
    }

    .login-title {
      font-weight: @font-weight-3;
      font-size: @text-9;
    }

    .login-sub-title {
      font-size: @text-3;
    }

    .login-version {
      font-size: @text-1;
      margin-top: 30vh;
    }

    .login-copy {
      font-size: @text-1;
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .login-box {
    width: 45%;
    min-width: 520px;
    max-width: 1152px;
    box-shadow: 0px 8px 20px 4px rgba(0, 0, 0, 0.1216);
    border-radius: @border-radius-2;
    background: #ffffff;
    box-shadow: 0px 8px 20px 4px rgba(0, 0, 0, 0.1216);

    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .login-box-content {
      margin: 0 auto;
      width: 400px;
      height: 456px;
    }

    .login-box-title {
      // text-align: center;
      height: 40px;
      line-height: 40px;
      margin-bottom: 34px;
      font-size: @text-7;
      font-weight: @font-weight-3;
      color: @text-color-1;
    }

    .el-form-item {
      margin-bottom: 40px;
    }

    .login-button {
      width: 400px;
      height: 48px;
      font-size: @text-3 !important;
      background-color: @primary-6;
      border-radius: @border-radius-4;
    }
  }
}
</style>

<style lang="less">
.el-form-item__error {
  padding-top: 4px;
}

.login-container {
  .el-input__wrapper {
    font-size: @text-1 !important;
    border-radius: @border-radius-4 !important;
  }
}
</style>
