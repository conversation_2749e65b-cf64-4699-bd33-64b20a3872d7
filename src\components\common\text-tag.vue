<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-22 15:47:24
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-16 10:01:42
 * @FilePath: /neue-cloud-vdi/src/components/common/text-tag.vue
-->
<template>
  <span :class="`text-tag ellipsis ${size} text-${type}`">{{ text }}</span>
</template>
<script setup lang="ts">
const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    // card 卡片tag，应用标签；
    // type 应用类型tag
    default: ''
  }
})
</script>
<style lang="less">
.text-tag {
  display: inline-block;
  padding: 2px 4px;
  max-width: 82px;
  font-size: @text-0;
  color: @text-color-2;
  border-radius: 2px;
  .el-tag__content {
    max-width: 74px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &.small,
  &.medium,
  &.large {
    padding: 0 3px;
    font-size: @text-0;

  }
  &.small {
    height: 18px;
    line-height: 16px;
  }
  &.medium {
    height: 18px;
    line-height: 18px;
  }
  &.large {
    height: 22px;
    line-height: 22px;
  }
  &.text-card {
    background: @primary-2;
    color: @text-color-4;
  }
  &.text-card0 {
    background: @link-2;
    color: @text-color-4;
  }
  &.text-apptype {
    background: @warning-1 !important;
    color: @warning-6 !important;
    border-radius: 2px;
  }
  &.text-apptag2 {
    background: @tag-bgcolor-2 !important;
    color: @tag-color-2 !important;
  }
  &.text-apptag1 {
    background: @tag-bgcolor-1 !important;
    color: @tag-color-1 !important;
  }
  &.text-roletag {
    background: @tips-1 !important;
    color: @tips-6 !important;
    border: @tips-6 solid 1px !important;
    padding-top: 0!important;
    padding-bottom: 0!important;
  }
}
</style>
