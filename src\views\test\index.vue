<template>
  <div>
    <el-label for="eventType">请选择事件类型：</el-label>
    <el-select v-model="selectedEvent" id="eventType">
      <el-option value="openModal1">打开弹窗1</el-option>
      <el-option value="openModal2">打开弹窗2</el-option>
      <el-option value="openNewPage">打开新页面</el-option>
      <el-option value="fetchData">请求接口</el-option>
    </el-select>
    <el-button @click="handleEvent">事件响应</el-button>

    <!-- 弹窗1 -->
    <div v-if="showModal1" class="modal">
      <p>这是弹窗1的内容</p>
      <el-button @click="closeModal1">关闭</el-button>
    </div>

    <!-- 弹窗2 -->
    <div v-if="showModal2" class="modal">
      <p>这是弹窗2的内容</p>
      <el-button @click="closeModal2">关闭</el-button>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import { account as accountService } from '@/service'

export default {
  setup() {
    const selectedEvent = ref('openModal1'); // 默认值
    const showModal1 = ref(false);
    const showModal2 = ref(false);

    const handleEvent = () => {
      switch (selectedEvent.value) {
        case 'openModal1':
          showModal1.value = true;
          break;
        case 'openModal2':
          showModal2.value = true;
          break;
        case 'openNewPage':
          window.open('https://www.baidu.com/', '_blank');
          break;
        case 'fetchData':
          fetchData();
          break;
        default:
          alert('请选择一个有效的事件类型');
      }
    };

    const closeModal1 = () => {
      showModal1.value = false;
    };

    const closeModal2 = () => {
      showModal2.value = false;
    };

    const fetchData = () => {
      accountService
        .postOperationsListUsers({})
        .then((res) => {
          console.log(res)
          if (res.code === 200) {
            alert('请求成功')
          } else {
            alert('请求失败')
          }
        })
        .catch((err) => {
          console.error(err)
          alert('请求失败')
        })
    };

    return {
      selectedEvent,
      showModal1,
      showModal2,
      handleEvent,
      closeModal1,
      closeModal2,
    };
  },
};
</script>

<style>
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  border: 1px solid #ccc;
  background-color: #fff;
  z-index: 1000;
}
</style>
