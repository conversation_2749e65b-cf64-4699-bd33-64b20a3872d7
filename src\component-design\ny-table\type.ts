import { ElTable, type PaginationProps, type TableProps } from 'element-plus'
import { PropType } from 'vue'
export interface TableColumnProps {
  prop: string
  label: string
  type?: string | 'default' | 'selection' | 'index' | 'expand'
  sortable?: boolean
  key?: string
  width?: number | string
  [x: string]: any
}
export interface TablePaginationProps extends Partial<PaginationProps> {
  currentPage: number
  pageSize: number
}
export interface NyTableProps<T = any> extends TableProps<T> {
  columns: TableColumnProps[]
  data: T[]
  pagination?: TablePaginationProps | boolean
  defaultPagination?: TablePaginationProps
}
export const nyTableProps = {
  ...ElTable.props,
  columns: {
    type: Array as PropType<TableColumnProps[]>,
    default: () => []
  },
  data: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  pagination: {
    type: [Boolean, Object] as PropType<boolean | TablePaginationProps>,
    default: true
  },
  defaultpagination: {
    type: Object as PropType<TablePaginationProps>,
    default: () => ({
      currentPage: 1,
      pageSize: 10
    })
  }
}
export default NyTableProps
