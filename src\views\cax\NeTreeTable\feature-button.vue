<template>
    <el-button v-bind="props" id="cax-button" >
        <template #icon>
            <i :class="`iconfont ${props.featureIcon}`" :style="{ color: featureColor }"></i>
        </template>
        <span :style="{ color: featureColor }">{{ props.featureText }}</span>
    </el-button>

</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import type { ButtonProps } from 'element-plus';
interface NeButtonProps extends Partial<ButtonProps> {
    featureText?: string 
    featureIcon?: string
    featureColor?: string
}

const props = defineProps<NeButtonProps>()


// const props = defineProps({
//     text: {
//         type: String,
//         default: '按钮',
//     },
//     icon: {
//         type: String,
//         default: 'icon-interactive-search'
//     },
//     method: {
//         type: Function as PropType<(event: MouseEvent) => void>,
//         default: () => {},
//     },
//     otherProps: {
//         type: Object as PropType<ButtonProps>,
//         default: () => ({}),
//     },
// })



</script>

<style scoped lang="less">
.el-button--primary{
    font-size: 14px !important;
    font-weight: 500 !important;
}
.el-button--primary,
.el-button--primary:hover {
  background-color: @primary-6 !important;
    border: 0 none !important;
    color: #FFFFFF !important;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover {
  background-color: @primary-4 !important;
  border: 0 none !important;
  color: #FFFFFF !important;
}

.is-text{
    font-size: 14px !important;
}

.is-text:hover {
    background-color: @primary-2 !important;
}
.is-text:active {
    background-color: @primary-2 !important;
    i {
    color: @link-6 !important;
    }
}

i {
    margin-right: 5px;
}


</style>