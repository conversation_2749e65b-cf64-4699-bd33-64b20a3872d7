// 设计协作区
export interface CollaborationZone {
  isActive?: boolean
  code?: string
  name?: string
  ncid?: string
  description?: string
  type?: string
  createdBy?: string
  createdAt?: string
  modifiedBy?: string
  modifiedAt?: string
  schemaVersion?: string
  schemaType?: string
  // projectSpaceId?: string
  // projectSpace?: {
  //   ncid?: string
  // }
  // owner?: {
  //   ncid?: string
  // }
  // thumbnail?: {
  //   ncid?: string
  // }
}
// 设计协作区空间
export interface CollaborationSpace {
  ncid?: string
  name?: string
  description?: string
  createdAt?: string
  projectSpace?: {
    ncid?: string
  }
  owner?: {
    ncid?: string
  }
  thumbnail?: {
    ncid?: string
  }
}

export interface CollaborationSpaceCreateParams {
  name?: string
  projectSpace?: {
    ncid?: string
  }
}