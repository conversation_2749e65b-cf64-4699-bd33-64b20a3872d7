<template>
  <div ref="textContent" class="ny-tooltip-ellipsis">
    <el-tooltip v-if="isOverflow" v-bind="realProps">
      <template #content>
        <content />
      </template>
      <slot name="default" style="white-space: nowrap;"></slot>
    </el-tooltip>
    <span v-else ref="textElement">
      <slot name="default"></slot>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { useResizeObserver } from '@/hooks';
import { ElTooltipProps } from 'element-plus';
import { ref, onMounted, useSlots, computed, nextTick, createVNode, watch,  VNode } from 'vue'
interface MySlots {
  default?: (arg:any)=>VNode[];
  content?:(arg:any)=>VNode[];
}
const props = defineProps<ElTooltipProps>()
let realProps = computed(() => {
  return { placement: 'top', showArrow: true } as ElTooltipProps
})
const slots = useSlots() as MySlots
const textElement = ref()
const textContent = ref()
const isOverflow = ref(false)
//暂时没有启用宽度变化监听功能  性能开销很大
let { width } = useResizeObserver(textElement)
const content = createVNode('div', {}, slots.content?.({})||slots.default!({}));
let init = () => {
  let { width: cWidth } = textContent.value.getBoundingClientRect()
  let { width: tWidth } = textElement.value.getBoundingClientRect()
  if (cWidth < tWidth ) {
    isOverflow.value = true
  } else {
    isOverflow.value = false
  }
}
nextTick(init)
watch(
  () => width,
  (newValue) => {
    init()
  }
)
</script>

<style lang="less">
.ny-tooltip-ellipsis {
  width: 100%;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  .el-only-child__content {
    line-height: 22px;
  }
}
</style>
