<template>

  <el-dialog
    v-model="checkInVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="updateVisible"
    title="打开"
    class="open-modal"
    width="640"
    :destroy-on-close="true"

  >
  <div class="open-title">选择路径</div>
  <div class="open-content">
    <div v-for="item in data" :key="item.id" @click="select($event, item)" :class="[item.name === selectPath ? 'active' : '']">
      <i class="iconfont icon-bin"></i>
      <span>{{ item.name }}</span>
    </div>
  </div>
    <template #footer>
      <div>
        <el-radio-group v-model="radio">
          <el-radio value="1" size="large">检出</el-radio>
          <el-radio value="2" size="large">参考</el-radio>
        </el-radio-group>
      </div>
      <div class="dialog-footer">
        <el-button @click="updateVisible">取消</el-button>
        <el-button type="primary" @click="confirm" :disabled="confirmDisable">确定</el-button>
      </div>
    </template>
  </el-dialog>
    <el-dialog
    v-model="progressVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    title="下载进度"
    width="640"
  >
      <div class="progress-container">
        <div class="progress-background" 
              :style="{
                  width: `${percentage}%`,
                  borderRadius: '4px'
                }">

        </div>
        <div class="progress-content">
          <img :src="bomIcon" alt="">
          <div>文件名字</div>
          <div class="right">{{ percentage }}%</div>
        </div>      
      </div>
      <div class="progress-text">{{ progressText }}</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" disabled v-if="progressIng">下载中...</el-button>
          <el-button type="primary" v-else @click="close">关闭</el-button>
        </div>
      </template>
  </el-dialog>
  <el-dialog
    v-model="failVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="failVisible = false"
    title="打开"
    width="640"
  >
  <div class="text-content">
    <i class="iconfont icon-tips-fill-fail"></i>
    <span>打开失败</span>
  </div>
  <div class="text-bottom">打开失败，请重新打开</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="failVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="successVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="successVisible = false"
    title="打开"
    width="640"
  >
  <div class="text-content success">
    <i class="iconfont icon-tips-fill-success"></i>
    <span>打开成功</span>
  </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="successVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue';
import bomIcon from '@/assets/images/cax/bom-icon.svg'
import { ElMessage } from 'element-plus';

 const data = ref([
  {
    id: 1,
    name: '文件1',
    type: 'BIN',
  },
  {
    id: 2,
    name: '文件2',
    type: 'BIN',
  },
  {
    id: 3,
    name: '文件3',
    type: 'BIN',
  },
  {
    id: 4,
    name: '文件4',
    type: 'BIN',
  },
 ])


const props = defineProps({
  checkVisible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:checkVisible']);
const percentage = ref(20);
const selectPath = ref(''); // 选中的路径
const radio = ref('2'); // 选中的radio

// 弹窗的显示状态s
const checkInVisible = ref(false);
const progressVisible = ref(false);
const failVisible = ref(false);
const successVisible = ref(false);
const confirmDisable = ref(true);
const progressIng = ref(false); // 进度条是否在进行中


// 进度条的文本内容
const progressText = ref('当前文件进程');
const textArr = ref([
  '反写模型属性...',
  '生成idi格式文件...',
  '上传文件...',
  '创建/更新数据对象...',
  '更新CADBOM结构',
]);

const updateVisible = () => {
  emit('update:checkVisible', false);
  selectPath.value = '';
  checkInVisible.value = false;
  confirmDisable.value = true;
};

const select = (e: Event, data: any) => {
  e.stopPropagation();
  selectPath.value = data.name;
  confirmDisable.value = false;
};


const confirm = () => {
  // 关闭打开弹窗
  updateVisible();
  // 打开进度条弹窗
  progressVisible.value = true;
  progressIng.value = true;
  const interval = setInterval(() => {
    if (percentage.value < 100) {
      percentage.value += 10;
    } else {
      clearInterval(interval);
      progressIng.value = false;
      success()
    }
  }, 1000);
};

const success= () => {
  ElMessage.success('打开成功');
};
const fail = () => {
  ElMessage.error('打开失败');
};


const close = () => {
  progressVisible.value = false;
};


watch(() => props.checkVisible, (newVal) => {
  if (newVal) {
    checkInVisible.value = true;
  } else {
    checkInVisible.value = false;
  }
});


onMounted(() => {

});

</script>


<style lang="less">
.open-modal {
  padding-left: 0 !important;
  padding-right: 0 !important;
  header{
    padding: 0 28px !important;
    margin-bottom: 20px !important;
  }
  footer{
    padding: 0 28px !important;
    margin-top: 20px !important;
    display: flex;
    justify-content: space-between;
    .el-radio__label {
    color: @text-color-2 !important;
  }
    .el-radio__input.is-checked+.el-radio__label {
    color: @text-color-2 !important;
  }

  }
  .el-dialog__body{
    border-top: 1px solid rgba(226, 232, 240, 0.5) !important;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
    padding: 12px 28px 28px 28px !important;
  }
}
</style>


<style lang="less" scoped>

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover {
  background-color: @primary-4 !important;
  border: 0 none !important;
  color: #FFFFFF !important;
}
.open-title {
  margin-bottom: 12px;
  font-size: @text-1;
  color: @text-color-2;
  text-align: left;
}
.open-content {
  text-align: left;
  background-color: @bg-color-2;
  &>div{
    height: 42px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
    display: flex;
    align-items: center;
    &:hover{
      background-color: @primary-2;
      cursor: pointer;
    }
    &:focus{
      background-color: @primary-2;
      cursor: pointer;
    }
  }
  .active{
    background-color: @primary-2;
  }
  i{
    font-size: 22px;
    color: #FDCD4B;
    margin-left: 28px;
    margin-right: 8px;
  }
  span{
    display: inline-block;
    font-size: 12px;
    color: @text-color-2;
  }
}


.progress-container {
  margin-top: 20px;
  // margin-bottom: 29px;
  width: 100%;
  height: 50px;
  border-radius: 4px;
  background-color: @bg-color-3;
  display: flex;
  align-items: center;
  position: relative;
  .progress-background{
    position: absolute;
    height: 50px;
    top: 0;
    left: 0;
    border-radius: 4px;
    background-color: @link-1;
  }
  .progress-content {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    height: 50px;
    display: flex;
    align-items: center;
    padding-left: 14px;
    font-size: 14px;
    color: #0F172A;
    img{
      margin-right: 14px;
    }
    .right{
      position: absolute;
      right: 14px;
    }
  }
}
.progress-text {
  margin-top: 4px;
  font-size: @text-0;
  color: @text-color-4;
  text-align: left;
}

.text-content{
  margin-top: 20px;
  text-align: left;
  font-size: 14px;
  color: @text-color-2;
  font-weight: 500;
  height: 24px;
  display: flex;
  align-items: center;
  i{
    font-size: 24px;
    color: @danger-6;
    margin-right: 12px;
  }
  span{
    display: inline-block;
  }
}
.success{
  i{
    color: @success-6;
  }
}
.text-bottom{
  text-align: left;
  font-size: 12px;
  color: @text-color-4;
  font-weight: 400;
  height: 18px;
  margin-left: 36px;
  span{
    color: #1856EB;
    font-weight: 600;
  }
}

</style>