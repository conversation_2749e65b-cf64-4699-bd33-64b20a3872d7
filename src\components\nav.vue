<template>
  <aside class="nav">
    <el-menu :default-active="String(defaultActive)" class="el-menu-vertical-demo" @open="handleOpen"
      @close="handleClose">
      <template v-for="(nav, index) in navList">
        <el-menu-item v-if="!nav.children" :index="String(index + 1)" :data-index="String(index + 1)"
          @click="navClick(nav)">
          <img v-if="nav.icon" class="menu-icon" :src="`${nav.isActive ? nav.iconActive : nav.icon}`" alt="" />
          {{ nav.name }}
        </el-menu-item>
        <el-sub-menu v-else :index="String(index + 1)" :data-index="String(index + 1)"
          :class="{ 'is-active': nav.isActive }">
          <template #title>
            <img v-if="nav.icon" class="menu-icon" :src="`${nav.isActive ? nav.iconActive : nav.icon}`" alt="" />
            <span>{{ nav.name }}</span>
          </template>
          <el-menu-item v-for="(item, k) in nav.children" :index="`${(index + 1) * 100 + k}`"
            :data-index="String((index + 1) * 100 + k)"
            :class="{ 'is-active': item.isActive }"
            @click="navClick(item)"
          >
            {{ item.name }}
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
  </aside>
</template>

<script lang="ts" setup>
import { onMounted, watch, ref, watchEffect, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NavItem } from '../common/types'
import { nomalNavList, consoleNavsList } from '@/common/filters/nav'
// import { nomalNavList } from '@/common/filters/nav'
import { useUserInfoStore } from '@/store/user/userInfo'

const userInfoStore = useUserInfoStore()
const router = useRouter()
const route = useRoute()

const navList = ref<Array<NavItem>>([])
const defaultActive = ref<number>(100)


const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

const changeNavActive = (currentPath: string) => {
  const endPath = currentPath.split('/').pop()
  let subIndex = 0
  console.log('navList',navList)
  navList.value.forEach((item: NavItem, index: number) => {
    const temp = item
    let isSubActive = false
    temp.isActive = false
    subIndex = 0
    if (temp.highlight?.some((path: string) => path === endPath)) {
      temp.isActive = true
      console.log('temp==>', endPath)
    }
    temp.children?.forEach((childItem: NavItem, k: number) => {
      const subTemp = childItem
      subTemp.isActive = false
      // subIndex = 0
      // temp.isActive = false
      // subTemp.isActive = currentPath.includes(subTemp.path)
      if (subTemp.highlight?.some((path: string) => path === endPath)) {
        subTemp.isActive = true
        temp.isActive = true
        isSubActive = true
        subIndex = k
        console.log('subtemp==>', endPath, subIndex)
      }
      return subTemp
    })
    // TODO： 菜单调试注释，刷新问题
    if (temp.isActive) {
      if (!temp.children) {
        defaultActive.value = index + 1
      } else {
        defaultActive.value = (index + 1) * 100 + subIndex
      }
    }
    return temp
  })
}


const refreshNav = () => {
  const consoleNavs = userInfoStore.getUserNavList
  console.log(consoleNavs)
  navList.value = route.path.indexOf('console') > -1 ? consoleNavs : nomalNavList
  changeNavActive(router.currentRoute.value.path)
}

watch(
  () => router.currentRoute.value.path,
  () => {
    refreshNav()
  }
)


const navClick = (e: NavItem) => {
  console.log('navClick===>', e.path)
  router.push(e.path)
}

onMounted(() => {
  refreshNav()
})

</script>

<style scoped lang="less">
.nav {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 12px;

  .nav-list {
    .iconfont {
      color: @text-color-5;
      font-size: 22px;
      margin-right: 12px;
    }

    .nav-item {
      display: flex;
      box-sizing: border-box;
      width: 100%;
      height: 44px;
      line-height: 44px;
      padding: 0 12px;
      border-radius: 5px;
      cursor: pointer;
      margin: 2px 0;
      font-size: @text-1;

      &.active,
      &:hover {
        color: @primary-6;
        font-weight: @font-weight-2;
        background-color: @other-color-2;

        .iconfont {
          color: @primary-6;
          font-weight: @font-weight-1;
        }
      }

    }

    .el-sub-menu.is-active {
      color: @primary-6;
      font-weight: @font-weight-2;

      .iconfont {
        color: @primary-6;
        font-weight: @font-weight-1;
      }
    }

    .el-menu-item.is-active {
      color: @primary-6;
      font-weight: @font-weight-2;
      background-color: @other-color-2;

      .iconfont {
        color: @primary-6;
        font-weight: @font-weight-1;
      }
    }
  }

}
</style>
<style lang="less">
.nav {
  .el-sub-menu.is-active {
    .el-sub-menu__title {
      color: @primary-6;
      font-weight: @font-weight-2;
    }

    .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }

  .el-menu-item.is-active {
    color: @primary-6;
    font-weight: @font-weight-2;
    background-color: @other-color-2;

    .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }
}
</style>
