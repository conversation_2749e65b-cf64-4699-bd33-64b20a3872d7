/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2025-01-23 10:23:02
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-11 13:43:25
 * @FilePath: /neue-cloud-vdi/src/service/account/index-url.ts
 */
export default {
  // 权限校验
  operationsAccess: `cdp/202512/users/me`,
  // 登录
  operationsLogin: `/account/202512/webkeys/actions/login`,
  // 重置密码
  operationsResetPassword: (userId: any | number) =>
    `/account/users/${userId}/operations/resetPassword`,
  operationsListApp: (userId: any | number) =>
    `/account/users/${userId}/operations/listApp`,
  // 新建用户
  accountUser: `/account/users`,
  // 编辑用户信息
  accountUserUid: (userId: any | number) => `/account/users/${userId}`,
  // 用户列表
  operationsList: `/account/users/operations/list`,
  // 删除用户
  operationsBatchDelete: `/account/users/operations/batchDelete`,
  // 获取用户时延,信息不全暂未调用
  operationsGetLatency: `/account/users/operations/getLatency`,
  // 修改用户组
  operationsAllocate: (userId: any | number) =>
    `/account/users/${userId}/groups/operations/allocate`,
  // 查找用户组
  operationsFindGroups: (userId: any | number) =>
    `/account/users/${userId}/operations/findGroups`,
  operationsListGroups: (userId: any | number) =>
    `/account/users/${userId}/operations/listGroups`,
  // 创建用户组
  accountGroups: `/account/groups`,
  // 编辑用户组|删除用户组
  accountGroupsId: (groupId: number | any) => `/account/groups/${groupId}`,
  // 删除用户组
  operationsRemoveGroup: (groupId: number | any) =>
    `/account/groups/${groupId}/operations/removeGroup`,
  // 获取用户组成员
  operationsListUsers: (groupId: number | any) =>
    `account/groups/${groupId}/operations/listUsers`,
  // 添加用户组成员
  operationsAddAccounts: (groupId: number | any) =>
    `account/groups/${groupId}/operations/addAccounts`,
  // 删除用户组成员
  operationsRemoveAccounts: (groupId: number | any) =>
    `account/groups/${groupId}/operations/removeAccounts`,

  operationsListAccounts: (groupId: number | any) =>
    `account/groups/${groupId}/operations/listAccounts`,

  operationsListAssignableAccounts: (groupId: number | any) =>
    `account/groups/${groupId}/operations/listAssignableAccounts`
}
