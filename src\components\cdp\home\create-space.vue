<template>
  <el-dialog
    id="dialog-space-add"
    v-model="props.visible"
    title="新建空间"
    :style="{
      width: '520px',
      height: '318px'
    }"
    class="dialog-space-add"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <template #default>
      <div class="dialog-content">
        <el-form id="dialog-space-add" ref="ruleFormRef" :model="ruleForm" :rules="rules" >
          <el-form-item label="空间名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              class="w-[320px]"
              show-word-limit
              minlength="1"
              maxlength="64"
              placeholder="必须以字母或中文开头"
            />
          </el-form-item>
          <el-form-item label="关联项目" prop="projectNcid">
            <!-- {{ projectList }} -->
            <el-select class="w-[320px]"  v-model="ruleForm.projectNcid" placeholder="请选择">
              <el-option
                v-for="(item, index) in projectList"
                :key="item.ncid"
                :value="item.ncid"
                :label="item.name"
              >{{  item.name  }}</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="ruleForm.description"
              show-word-limit
              maxlength="255"
              class="space-description"
              type="textarea"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button id="console-btn-cancel" @click="handleClose">取消</el-button>
        <el-button id="console-btn-submit" type="primary" @click="handleConfirm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router'
import {
  ElMessage,
  type ComponentSize,
  type FormInstance,
  type FormRules
} from 'element-plus'
import { reactive, ref, defineEmits, onMounted, computed, watchEffect } from 'vue'
import { validateNameStart } from '@/utils/validate'
import { CollaborationSpace } from '@/common/types/space'
import cdp from '@/service/cdp'
import { useProjectInfo } from '@/store/projectSpace/projectInfo'
import { useUserInfoStore } from '@/store/user/userInfo'
import { watch } from 'fs'

const projectInfoStore = useProjectInfo()
const userInfoStore = useUserInfoStore()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  activeDetail: {
    type: Object,
    default: () => {
      return {
        ncid: '',
        name: '',
        thumbnail: {
          ncid: ''
        }
      }
    }
  }
})

const emit = defineEmits(['update:visible', 'handleCreated'])
const ruleFormRef = ref<FormInstance>()
// const projectList = ref(projectInfoStore.getProjectList)

const projectList = computed(() => {
  return projectInfoStore.projectList
})


interface ProjectSpaceParams {
  name: string
  projectNcid: string
  description?: string
  thumbnail?: {
    ncid: string
  }
}
const ruleForm: ProjectSpaceParams = reactive({
  name: '',
  projectNcid: props.activeDetail.ncid,
  description: '',
  thumbnail: {
    ncid: 'ncid1.plt0storageitementity.local..c108c48a-651e-4032-9db0-08aa5940bb47'
  }
})

const validateNameStr = (rule: any, value: any, callback: any) => {
  const msg = '支持1-64位字符，必须以字母或中文开头'
  if (value === '') {
    console.log('value', value)
    callback(new Error(msg))
  } else if (value.length < 1 || value.length > 64) {
    callback(new Error(msg))
  } else if (!validateNameStart(value)) {
    callback(new Error(msg))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  name: [{ required: true, trigger: 'blur', validator: validateNameStr }],
  projectNcid: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
})


const handleClose = () => {
  resetForm()
  console.log('handleClose111')
  emit('update:visible', false)
}


const resetForm = () => {
  ruleFormRef.value?.resetFields()
  ruleForm.name = ''
  ruleForm.projectNcid = ''
  ruleForm.description = ''
}
const handleConfirm = async () => {
  await ruleFormRef.value?.validate((valid) => {
    if (valid) {
      handleSubmit()
    } else {
      ElMessage.error('表单验证失败')
    }
  })
}

const handleSubmit = async () => {
  try {
    const params: CollaborationSpace = {
      name: ruleForm.name,
      projectSpace: {
        ncid: ruleForm.projectNcid
      },
      description: ruleForm.description,
      owner: {
        ncid: userInfoStore.userInfo?.id,
      },
      thumbnail: ruleForm.thumbnail
    }
    const res = await cdp.postBusinessobjectCreate(params)
    if (res) {
      ElMessage.success('创建成功')
      emit('handleCreated', res)
      handleClose()
    }
  } catch (error) {
    ElMessage.error('创建失败: ')
  }
}

watchEffect(() => {
  if (props.activeDetail.ncid) {
    ruleForm.projectNcid = props.activeDetail.ncid
  } else {
    ruleForm.projectNcid = ''
  }
})

// watch(
//   () => props.visible,
//   (newVal) => {
//     if (!newVal) {
//       resetForm()
//     }
// })

onMounted(() => {
  // const projectInfoStore = useProjectInfo()
  // projectList.value = projectInfoStore.projectList
  console.log('projectList', projectList.value)
})

</script>

<style lang="less">
.icon-exclamation-circle-fill {
  color: @warning-5;
  font-size: @text-5 !important;
}
.dialog-space-add {
  display: flex;
  flex-direction: column;
  font-weight: 400;
  .el-dialog__header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: left !important;
    padding-bottom: 0;
  }
  .el-dialog__body{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .el-dialog__footer {
    padding-top: 0 !important;
  }
  .el-select .el-input__inner .el-select__input__inner::placeholder {
    color: @border-color-1 !important;
  }

  .dialog-content {
    color: @text-color-2;
    .dialog-title {
      text-align: left;
    }
    .el-form-item {
      margin-bottom: 16px;
      
      .el-form-item__label {
        width: 112px;
        font-weight: 400;
      }
      .space-description {
        width: 320px;
        height: 80px!important;
        min-height: 80px !important;
        textarea {
          min-height: 80px !important;
        }
      }
    }
  }
}
</style>
