// 检出状态枚举
export const checkOutStatusEnum: Record<string, string> = {
    MYSELF: '本人检出',
    OTHER: '他人检出',
    RFL: '参考特定版本',
    RFS: '参考最新版本'
};

// 状态枚举
export const statusEnum: Record<string, string> = {
    WORKING: '工作中',
    PUBLISHING: '发布中',
    PUBLISHED: '已发布',
};

// 工作台状态枚举
export const workbenchStatusEnum: Record<string, string> = {
    OUT: '取出',
    IN: '放回'
};

// 类型枚举
export const typeEnum: Record<string, string> = {
    PART: '零件',
    ASSEMBLY: '装配',
    DRAWING: '图纸',
    FEM: '仿真'
};

// 时间字段枚举
export const fieldTimeEnum: Record<string, string> = {
    checkOutTime: '检出时间',
    createAt: '创建时间',
    updateAt: '修改时间',
};
// 字段名枚举
export const fieldNameConfig: Array<{ label: string; prop: string; width: string; fixed?: boolean }> = [
    {
        label: '对象',
        prop: 'object',
        width: '280',
        fixed: true,
    },
    {
        label: '类型',
        prop: 'itemType',
        width: '154',
    },
    {
        label: '工作台状态',
        prop: 'workbenchState',
        width: '136',
    },
        {
        label: '名称',
        prop: 'name',
        width: '154',
    },
    {
        label: '编号',
        prop: 'partNo',
        width: '120',
    },
        {
        label: '检出状态',
        prop: 'checkOutState',
        width: '154',
    },
    {
        label: '当前版本',
        prop: 'currentRevision',
        width: '154',
    },
    {
        label: '新版本',
        prop: 'lastRevision',
        width: '154',
    },
    {
        label: '状态',
        prop: 'itemState',
        width: '120',
    },
    {
        label: '责任人',
        prop: 'owner',
        width: 'auto',
    },

    // {
    //     label: '检出人',
    //     prop: 'checkOutUser',
    //     width: '100',
    // },
    // {
    //     label: '检出时间',
    //     prop: 'checkOutTime',
    //     width: '100',
    // },
    // {
    //     label: '创建人',
    //     prop: 'createBy',
    //     width: '100',
    // },
    // {
    //     label: '创建时间',
    //     prop: 'createAt',
    //     width: '100',
    // },
    // {
    //     label: '修改人',
    //     prop: 'updateBy',
    //     width: '100',
    // },
    // {
    //     label: '修改时间',
    //     prop: 'updateAt',
    //     width: 'auto',
    // },
]
