apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{name}}
  labels:
    app: {{name}}
spec:
  replicas: {{replicas}}
  selector:
    matchLabels:
      app: {{name}}
  template:
    metadata:
      labels:
        app: {{name}}
    spec:
      containers:
        - name: {{name}}
          image: {{image}}:{{tag}}  # Use both image and tag
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: PROFILE_OPTS
              value: {{profileOpts}}
            - name: SERVER_DOMAIN
              value: {{serverDomain}}
            - name: VOLUME_DATA
              value: {{volumeData}}
          ports:
            - containerPort: {{port}}
          resources:
            requests:
              memory: {{requests.memory}}
              cpu: {{requests.cpu}}
            limits:
              memory: {{limits.memory}}
              cpu: {{limits.cpu}}
          livenessProbe:
            httpGet:
              path: {{livenessPath}}  # Configurable liveness probe path
              port: {{port}}
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: {{readinessPath}}  # Configurable readiness probe path
              port: {{port}}
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          volumeMounts:
            - name: storage-data
              mountPath: {{volumeData}}
      imagePullSecrets:
        - name: {{imagePullSecrets}}
      nodeSelector:
        kubernetes.io/hostname: {{nodeSelector}}
      volumes:
        - name: storage-data
          hostPath:
            path: {{volumeData}} # 宿主机上的存储路径
            type: DirectoryOrCreate  # 如果目录不存在，则创建
---
apiVersion: v1
kind: Service
metadata:
  name: {{name}}
spec:
  selector:
    app: {{name}}
  ports:
    - protocol: TCP
      port: {{port}}
      targetPort: {{port}}
  type: {{serviceType}}
