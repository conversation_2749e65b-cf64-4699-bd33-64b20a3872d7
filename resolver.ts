// 内联定义 ComponentResolver 类型，与 unplugin-vue-components 兼容
interface ComponentResolverObject {
  type: 'component' | 'directive'
  resolve: (name: string) =>
    | {
        name: string
        from: string
        sideEffects?: string | string[] | undefined
      }
    | undefined
}

type ComponentResolver = ComponentResolverObject

export interface NeuePlusResolverOptions {
  /**
   * import style css or sass with components
   *
   * @default 'css'
   */
  importStyle?: boolean | 'css' | 'sass'

  /**
   * use commonjs lib & source css or scss
   *
   * @default false
   */
  ssr?: boolean

  /**
   * specify neue-plus version for better resolving
   */
  version?: string

  /**
   * auto import for directives
   *
   * @default true
   */
  directives?: boolean

  /**
   * exclude component name, if match do not resolve the name
   */
  exclude?: RegExp | ((name: string) => boolean)
}

function getSideEffects(
  dirName: string,
  options: NeuePlusResolverOptions
): string[] | undefined {
  const { importStyle = true } = options

  if (!importStyle) return undefined

  if (importStyle === 'sass') {
    return [`@neue/neue-plus/theme-chalk/src/${dirName}.scss`]
  } else {
    return [`@neue/neue-plus/theme-chalk/ne-${dirName}.css`]
  }
}

function resolveComponent(
  name: string,
  options: NeuePlusResolverOptions
): { name: string; from: string; sideEffects: string[] | undefined } | undefined {
  if (!name.match(/^Ne[A-Z]/) || name === 'NeIcon') return undefined

  const partialName = name.slice(2)
  const fileName = partialName
    .replace(/([A-Z])([A-Z])/g, '$1-$2')
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .toLowerCase()

  return {
    name: `Ne${partialName}`,
    from: `@neue/neue-plus/es/components/${fileName}/index`,
    sideEffects: getSideEffects(fileName, options)
  }
}

function resolveDirective(
  name: string,
  options: NeuePlusResolverOptions
): { name: string; from: string; sideEffects: string[] | undefined } | undefined {
  if (!name.match(/^Ne[A-Z]/)) return undefined

  const partialName = name.slice(2)
  const fileName = partialName
    .replace(/([A-Z])([A-Z])/g, '$1-$2')
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .toLowerCase()

  return {
    name: `Ne${partialName}`,
    from: `@neue/directives/${fileName}`,
    sideEffects: getSideEffects(fileName, options)
  }
}

/**
 * Resolver for Neue Plus
 *
 * @param options
 */
export function NeuePlusResolver(
  options: NeuePlusResolverOptions = {}
): ComponentResolver[] {
  const { directives = true, exclude } = options

  return [
    {
      type: 'component' as const,
      resolve: (name: string) => {
        if (
          exclude &&
          ((exclude instanceof RegExp && exclude.test(name)) ||
            (typeof exclude === 'function' && exclude(name)))
        ) {
          return undefined
        }

        if (name.match(/^Ne[A-Z]/)) {
          return resolveComponent(name, options)
        }
        return undefined
      }
    },
    ...(directives
      ? [
          {
            type: 'directive' as const,
            resolve: (name: string) => {
              if (
                exclude &&
                ((exclude instanceof RegExp && exclude.test(name)) ||
                  (typeof exclude === 'function' && exclude(name)))
              ) {
                return undefined
              }

              if (name.match(/^Ne[A-Z]/)) {
                return resolveDirective(name, options)
              }
              return undefined
            }
          }
        ]
      : [])
  ]
}
