<template>
  <div>
    <NeDynamicForm
      :schema="formSchema"
      :model="formData"
      ref="ruleFormRef"
      label-width="100px"
      class="design-form"
      @onValuesChange="handleFormChange"
    >
      <div class="flex w-full justify-center">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </NeDynamicForm>

    <!-- 历史记录时间线 -->
    <div class="px-[28px] text-[14px]">
      <div class="flex" v-for="record in historyRecords">
        <div class="timeline-left">
          <div class="date-time pt-[12px] text-[#7F8C9F]">
            <div class="text-right">{{
              dayjs(record.createdAt).format('YYYY-MM-DD')
            }}</div>
            <div class="text-right">{{
              dayjs(record.createdAt).format('HH:mm:ss')
            }}</div>
          </div>
        </div>
        <div class="item-left">
          <div class="flex">
            <div class="px-[12px] flex items-center justify-center flex-col py-[12px]">
              <div
                class="border-[#1856EB] w-[12px] h-[12px] rounded-[12px] mb-[8px]"
                style="border: 2px solid #1856eb"
              ></div>
              <el-divider direction="vertical h-[32px]"></el-divider>
            </div>
            <div class="mr-[12px]">
              <span
                class="flex w-[32px] h-[32px] items-center justify-center bg-[#3D5495] rounded-[32px] text-[#fff]"
                >{{
                  record.modifiedBy.name.charAt(record.modifiedBy.name.length - 1)
                }}</span
              >
            </div>
            <div>
              <div class="text-[#7F8C9F] mb-[4px]">{{ record.modifiedBy.name }}</div>
              <div class="flex items-center">
                <el-tag :type="getCheckState(record.changeType)?.type" size="small">
                  {{ getCheckState(record.changeType)?.text }}
                </el-tag>
                <span class="version-info">【{{ record.ncid }}】</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DynamicFormExposeProps,
  SchemaItemProps
} from '@/component-design/ne-dynamic-form/index.vue'
import actions from '@/service/actions'
import dayjs from 'dayjs'
import { ChangeTypeEmu } from '@/constant'
import { reactive, computed, onMounted, ref } from '@vue/runtime-core'

// 定义组件属性
const props = defineProps({
  partId: {
    type: [String, Number],
    required: true
  }
})
// 表单引用
const ruleFormRef = ref<DynamicFormExposeProps>()

// 表单数据
const formData = reactive({
  partNumber: '',
  partName: ''
})

// 表单架构
const formSchema = computed<SchemaItemProps[]>(() => [
  {
    type: 'select',
    label: '更新类型',
    prop: 'updateType',
    placeholder: '请选择',
    span: 8,
    options: [
      { label: '创建', value: 'create' },
      { label: '更新', value: 'update' },
      { label: '检出', value: 'checkout' }
    ]
  },
  {
    type: 'input',
    label: '相关人员',
    prop: 'updateType',
    span: 8
  },
  {
    type: 'daterange',
    label: '更新时间',
    prop: 'updateTime',
    span: 8
  }
])
// 表单值变化处理
const handleFormChange = (val: any) => {
  Object.assign(formData, val)
}
// 重置处理
const handleReset = () => {
  if (!ruleFormRef.value) return
  const { formIns } = ruleFormRef.value
  formIns?.resetFields()
}
// 筛选条件
const filters = reactive({
  updateType: '',
  person: '',
  dateRange: []
})

// 历史记录数据
const historyRecords = ref()

const getCheckState = (status: string | undefined) => {
  return ChangeTypeEmu[status as keyof typeof ChangeTypeEmu]
}

// 处理搜索
const handleSearch = () => {
  console.log('搜索条件:', filters)
  // 这里可以根据筛选条件过滤历史记录
}

let getData = async () => {
  let res = await actions.ActivityObjectHistory({
    classId: 'plt0NeueCADPart',
    ncid: props.partId,
    childClass: 'plt0ActivityObjectHistory',
    pageParams: {
      limit: 9999,
      page: '1',
      sorts: null
    },
    condition: undefined
  })
  console.log(res)
  historyRecords.value = res.data
}
onMounted(() => {
  // 这里可以根据筛选条件过滤历史记录
  getData()
})
</script>

<style scoped lang="less"></style>
