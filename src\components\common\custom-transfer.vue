<!--
 * @Author: wangyan-judy
 * @Description:定制版穿梭框
 * @Date: 2024-11-19 14:38:39
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-11-19 14:38:40
 * @FilePath: /neue-cloud-vdi/src/components/common/custom-transfer.vue
-->
<template>
  <div class="custom-transfer">
    <div class="transfer-panel">
      <el-transfer
        v-model="leftChecked"
        :data="leftData"
        :titles="['Available', 'Selected']"
        :button-texts="['To Right', 'To Left']"
        @change="handleTransferChange"
        class="transfer"
      />
    </div>
    <div class="transfer-panel">
      <el-transfer
        v-model="rightChecked"
        :data="rightData"
        :titles="['Selected', 'Available']"
        :button-texts="['To Left', 'To Right']"
        @change="handleTransferChange"
        class="transfer"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { ElTransfer } from 'element-plus';

export default defineComponent({
  components: {
    ElTransfer
  },
  setup() {
    const leftChecked = ref<number[]>([]);
    const rightChecked = ref<number[]>([]);
    const leftData = ref<any[]>([
      { key: 1, label: 'Option 1' },
      { key: 2, label: 'Option 2' },
      { key: 3, label: 'Option 3' },
      { key: 4, label: 'Option 4' },
    ]);
    const rightData = ref<any[]>([]);

    const handleTransferChange = (options: any, direction: string, movedKeys: number[]) => {
      if (direction === 'left') {
        rightChecked.value = movedKeys;
      } else {
        leftChecked.value = movedKeys;
      }
    };

    return {
      leftChecked,
      rightChecked,
      leftData,
      rightData,
      handleTransferChange
    };
  }
});
</script>

<style scoped>
.custom-transfer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transfer-panel {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.transfer {
  width: 80%;
}
</style>
