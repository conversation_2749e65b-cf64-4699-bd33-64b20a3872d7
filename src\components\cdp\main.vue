<template>
  <router-view v-if="judgeRoute(getRouteName())" />
  <!-- 控制台 -->
  <main v-else :class="`main-container common-layout ${'home' === getRouteName() ? '' : 'nomal-layout'}`">
    <el-container class="content-layout">
      <el-header :class="`header-container ${'home' === getRouteName() ? '' : 'nomal-header'}`">
        <Header />
      </el-header>
      <el-container class="content-container">
        <template v-if="'home' === getRouteName() || '' === getRouteName()">
          <el-aside class="main-aside" width="240px">
            <Nav />
          </el-aside>
          <router-view />
        </template>
        <template v-else>
          <router-view />
        </template>
      </el-container>
    </el-container>
  </main>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import Header from '@/components/cdp/header.vue'
import Nav from '@/components/cdp/nav.vue'

const route = useRoute();

const specialLayout = ['login', 'cax','vdi']


const judgeRoute = (routeName: string) => {
  if(specialLayout.includes(routeName)) {
    return true;
  }
  if(routeName.indexOf('cax') !== -1) {
    return true;
  }
  return false;

}


const getRouteName = () => {
  return route.name?.toString().toLowerCase()??'';
}

console.log('START::', new Date())
</script>

<style scoped lang="less">

.main-container {
  width: 100%;
  min-width: 1024px;
  height: 100%;
  background-color: @bg-color-4;
  display: flex;
  justify-content: center;
  // align-items: center;
  .content-layout {
    width: 100%;
    height: calc(100% );
    overflow: hidden;
    border-radius: 10px;
    .content-container {
      overflow: hidden;
    }
    .header-container {
      height: 68px;
    }
    .nomal-header {
      border-bottom: solid 1px rgba(0, 0, 0, 0.1);
    }
  }
  .main-aside {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .nav {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .nav-item {
        padding: 10px 20px;
        cursor: pointer;
        // &:hover {
        //   background-color: @link-2;
        // }
      }
    }
    .design-parts {
      padding: 10px 20px;
      cursor: pointer;
      &:hover {
        background-color: @link-2;
      }
    }
  }
}
.nomal-layout {
  background: 0 none !important;
}
</style>
