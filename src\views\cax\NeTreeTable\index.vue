<template>
  <div class="cax-container">
    <div class="cax-project">
      <feature-button featureText="检出" type="primary" featureIcon="icon-export" 
      :disabled="isExport" featureColor="#FFFFFF"  @click="exportBom" ></feature-button>
      <feature-button featureText="检入" type="primary" :disabled="isImport" featureIcon="icon-import" featureColor="#FFFFFF" @click="importBom" ></feature-button>
      <feature-button featureText="刷新" featureIcon="icon-interactive-button-refresh" :text="true" @click="refresh" ></feature-button>
      <feature-button v-if="isExpand" featureText="收起" featureIcon="icon-shouqi" :text="true" @click="allCollapse"></feature-button>
      <feature-button v-else featureText="展开" featureIcon="icon-zhankai" :text="true" @click="allExpand"></feature-button>
    </div>
    <div class="cax-table">
      <el-table
        id="cax-table"
       :data="tableData"
       row-key="itemId"
       ref="table"
       @row-click="rowClick"
        @row-contextmenu="rightClick"
        header-cell-class-name="cax-header"
        row-class-name="cax-table-row"
        :highlight-current-row="true"
        default-expand-all
        :row-style	="rowStyle"
        style="width: 100%;"
     >
       <el-table-column v-if="props.selectable" type="selection" width="55" fixed/>
       <el-table-column v-for="(item, index) in fieldNameConfig"  min-width="120" 
          :key="item.label"
          :prop="item.prop"
          :label="item.label"
          :fixed="item.fixed"
          :width="item.width"
         >
         <template #header="{ column }">
             <div
               class="cax-header-cell"
             >
               {{ column.label }}
             </div>
         </template>
         <template #default="{ row }">
             <span style="display: inline-block;" v-if="item.prop === 'object'">
                <cax-icon-text :type="row.itemType" :content="row.object"></cax-icon-text>
              </span>
              <div v-else-if="item.prop === 'itemState'">
                <cax-tag-border :type="row.itemState" :content="statusEnum[row.itemState]"></cax-tag-border>
              </div>
              <div v-else-if="item.prop === 'workbenchState'">
                <cax-icon-text :type="row.workbenchState" :content="workbenchStatusEnum[row.workbenchState]" iconSize="12px" textSize="12px"></cax-icon-text>
              </div>
              <div v-else-if="item.prop === 'checkOutState'">
                <cax-tag :type="row.checkOutState" :content="checkOutStatusEnum[row.checkOutState]"></cax-tag>
              </div>
              <div v-else-if="fieldTimeEnum[item.prop]" class="table-time">
                <div>{{ row[item.prop]?.split?.(" ")[0] || '' }}</div>
                <div>{{ row[item.prop]?.split?.(" ")[1] || '' }}</div>
              </div>
              <div v-else class="ellipsis" :title="row[item.prop]">
                {{ row[item.prop] }}
              </div>
         </template>
       </el-table-column>
     </el-table>
     <ToolBar />
    </div>
     <right-menu :menus="rightMenusConfig" ref="rightClickMenu"/>
     <check-in-progress v-model:checkVisible="checkVisible"/>
  </div>
   </template>
   
 <script lang="ts" setup>
 

 import {nextTick, onMounted, ref } from 'vue'

  import featureButton from './feature-button.vue'
  import rightMenu from './right-menu.vue'
  import rightMenusConfig from './right-menu-config'
  import {checkOutStatusEnum, statusEnum, workbenchStatusEnum, typeEnum, fieldNameConfig, fieldTimeEnum} from './type'
  import caxTag from './cax-tag.vue'
  import caxTagBorder from './cax-tag-border.vue'
  import caxIconText from './cax-icon-text.vue'
  import { ElMessage } from 'element-plus'
  import checkInProgress from './check-in-progress.vue'
  import ToolBar from "@/components/ufrEngine/toolbar/index.vue"
  import { socket, getModelList } from '../websocket/sockets';
  import cax from '@/service/cax/index';


  export interface FieldName {
  itemId: string; // 唯一标识
  object: string; // 对象
  itemType: string; // 类型
  workbenchState: string; // 工作台状态
  partNo: string; // 编号
  name: string; // 名称
  currentRevision: string; // 当前版本
  lastRevision: string; // 新版本
  itemState: string; // 状态
  owner: string; // 责任人
  checkOutState: string; // 检出状态
  checkOutUser: string; // 检出人
  checkOutTime: string; // 检出时间
  createBy: string; // 创建人
  createAt: string; // 创建时间
  updateBy: string; // 修改人
  updateAt: string; // 修改时间
}

  export interface tableDataConfig {
    itemId: string; // 唯一标识
    object: string; // 对象
    itemType: string; // 类型
    children?: FieldName[]; // 子文件夹
  }


  const props = defineProps({
    selectable: {
      type: Boolean,
      default: false,
    },
    // 是否可以拖拽
    rowDrop: {
      type: Boolean,
      default: false,
    },
    columnDrop: {
      type: Boolean,
      default: false,
    },
    // 是否显示右键菜单
    rightMenus: {
      type: Boolean,
      default: false,
    },
  })

 // 最大层级
const max = ref(1)
// 是否已经展开
const isExpand = ref(true)

//是否检入
const isImport = ref(true)
// 是否检出
const isExport = ref(true)

// 搜索框的值
const inputName = ref('')
// 表格的实列
const table = ref()
 // 表格数据
const tableData = ref<tableDataConfig[]>([])
  // 右键菜单实列
const rightClickMenu = ref<InstanceType<typeof rightMenu>>()

const clickData = ref<FieldName>({} as FieldName)

// 表格配置数据
interface TableColumn {
  label: string;
  prop: string;
  width?: string;
  fixed?: string;
}
const checkVisible = ref(false)

// 表格配置
const tableConfig = ref<TableColumn[]>([])


const rowStyle = (data: { row: any, rowIndex: number }) => {
  const { row, rowIndex } = data
  if(row.checkOutState === '2'){
    return {
      'background-color': '#FEF2F2',
    }
}
}


const refresh = () => {
  // 重新刷新表格数据
  ElMessage({
    message: '刷新成功',
    type: 'success',
  })
}


// 行点击事件
 const rowClick = (row:FieldName) => {
     console.log(row);
     if(row.checkOutState === '3' || row.checkOutState === '4'){
          isExport.value = false
          isImport.value = true
     }else if(row.checkOutState === '1' ){
          isExport.value = true
          isImport.value = false
     }else{
          isExport.value = true
          isImport.value = true
     }
      clickData.value = row
 }

 // 检入
  const importBom = () => {
    if(clickData.value.name){
      console.log('检入')
       checkVisible.value = true
    }else{
      ElMessage({
        message: '未命名，请重新命名后检入',
        type: 'error',
      })
      return
    }
      
  }
  
  // 检出
  const exportBom = () => {
      console.log('检出')
      ElMessage({
        message: '检出成功',
        type: 'success',
      })
      ElMessage({
        message: '检出失败',
        type: 'error',
      })
  }
 
 const allExpand = () => {
     forArr(tableData.value, true, max.value)
      isExpand.value = true

 }
 
 const allCollapse = () => {
     forArr(tableData.value, false, max.value)
      isExpand.value = false

 }
  
 // 递归展开或收起
 const forArr = (arr:tableDataConfig[], isExpand:boolean, level:number) =>{
     arr.forEach(i => {
         table.value.toggleRowExpansion(i, isExpand)
         if (i.children) {
             let newLevel = level -1
             if(newLevel > 0){
                 forArr(i.children, true, newLevel)
             }else{
                 forArr(i.children, false, newLevel)
             }
         }
   })
 }
 

 // 寻找tableData的最大层级
 const findMax = (data: tableDataConfig[], level = 1) => {
   data.forEach((item) => {
     if (item.children) {
       max.value = Math.max(max.value, level)
       findMax(item.children, level + 1)
     }
   })
 }

 // 右键打开菜单
const rightClick = (row: any, column: any, e: MouseEvent) => {
  if (!props.rightMenus) return
  e.preventDefault()
  e.stopPropagation()
  rightClickMenu.value?.open(e)
}
// 点击页面其他地方关闭右键菜单
const dealCloseMenu = () => {
    document.querySelector('.cax-container')?.addEventListener('click', () => {
    rightClickMenu.value?.close()
  })
  document.querySelector('.cax-container')?.addEventListener('contextmenu', e => {
    e.preventDefault()
    e.stopPropagation()
    rightClickMenu.value?.close()
  })
}
  socket.on('AC11000', (data: any) => {
    console.log('AC11000', data);
    if(data){
    let newData = Object.assign({}, ...data.body.AC11000)
    getModelFile(newData)
    }
  })

  const getModelFile = (data: any) => {
    const sendData = {
      modelItems: data,
    }
    cax.postGetModelFileList(sendData).then((res) => {
      if (res.data) {
        dealResult(res.data.modelItems)
      }
    }).catch((err) => {
      console.error(err)
      ElMessage({
        message: '获取模型文件列表失败',
        type: 'error',
      })
    })
  }


  const dealResult = (data: any) => {
    // 处理返回的数据
    const responseData: tableDataConfig[] = []
    Object.keys(data).forEach((key) => {
      const item = data[key]
      // 生成表格数据
      const tableItem: tableDataConfig = {
        itemId: Math.random().toString(36).substr(2, 9), // 生成唯一标识
        object: key,
        itemType: 'BIN',
        children: item
      }
      responseData.push(tableItem)
    })
    console.log('responseData', responseData);
    // // 生成表格配置
    // // 寻找最大层级
    findMax(responseData)
    // 设置表格数据
    tableData.value = responseData
    // // 重新计算表格布局
    nextTick(() => {
      table.value.doLayout()
      allExpand() // 默认展开
    })
  }

  const generateConfig = (data: any) => {
    // 生成表格配置
    const config:any = []
    fieldNameConfig.forEach((item) => {
      if (
        data[0]?.children?.[0] &&
        item.prop in data[0].children[0]
      ) {
        config.push(item)
      }
    })
    tableConfig.value = config
  }

 
 onMounted(() => {
    //处理右键菜单默认事件
    if(props.rightMenus){
      dealCloseMenu()
    }
    nextTick(() => {
      // 发送获取模型列表的请求
      socket.send(getModelList())
    })
 })
 
   </script>
 
 <style scoped lang="less">
 .cax-container{
  width: 100%;
  padding: 0 24px;
 }
 .cax-project{
  height: 72px;
   display: flex;
   align-items: center;
 }
  .cax-table{
    // :deep(.el-scrollbar__view){
    //   display: block !important;
    // }

    :deep( .cax-header){
      background-color: @bg-color-3 !important;
      padding: 9px 0;
      .cax-header-cell{
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-weight: 600;
        color: @text-color-2;
      }
    }
    :deep(.cax-table-row){
      .el-table__cell{
        padding: 0 0;
        .cell{
          height: 42px;
          line-height: 42px;
          span {
            font-size: 14px;
          }
          .table-time{
            div{
              font-size: 12px;
              height: 21px;
              line-height: 18px;
              &:nth-child(1){
              line-height: 24px;
              }
            }

          }
        }
      }
  
    }
  }

 .el-input {
     width: 200px;
     margin-left: 10px;
     margin-right: 20px;
 }
 </style>