import type { DefineComponent } from 'vue'
import NyButtonProps from './ny-button'
import NyInputProps from './ny-input'
import NyPageTitleProps from './ny-page-title'
import NyCheckboxNormalProps from './ny-checkbox-normal'
import NyPopoverProps from './ny-popover'
import NyDrawerProps from './ny-drawer'
import NyTooltipProps from './ny-tooltip'
import NyDialogProps from './ny-dialog'
import NyTransferProps from './ny-transfer'
import NyServerTableProps from './ny-server-table'
import NyTableProps from './ny-table/type'
import NeTreeTableProps from './ne-tree-table/type'
import NyTooltipEllipsis from './ny-tooltip-ellipsis'
import NyTextareaProps from './ny-textarea'

declare module 'vue' {
  export interface GlobalComponents {
    NyButton: DefineComponent<NyButtonProps>
    NyPopover: DefineComponent<NyPopoverProps>
    NyDrawer: DefineComponent<NyDrawerProps>
    NyInput: DefineComponent<NyInputProps>
    NyPageTitle: NyPageTitleProps
    NyCheckboxNormal: DefineComponent<NyCheckboxNormalProps>
    NyTooltip: DefineComponent<NyTooltipProps>
    NyDialog: DefineComponent<NyDialogProps>
    NyTransfer: DefineComponent<NyTransferProps>
    NyServerTable: DefineComponent<NyServerTableProps>
    NyTable: DefineComponent<NyTableProps>
    NyTooltipEllipsis: DefineComponent<NyTooltipEllipsis>
    NyTextarea: DefineComponent<NyTextareaProps>
    NeTreeTableProps: DefineComponent<NeTreeTableProps>
  }
}
