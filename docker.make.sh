###
 # @Author: wangyan-judy
 # @Description:
 # @Date: 2024-09-18 16:59:22
 # @LastEditors: wangyan-judy
 # @LastEditTime: 2024-09-18 17:23:58
 # @FilePath: /neue-cloud-vdi/docker.make.sh
###
#!/bin/bash

# 检查是否传入了足够的参数
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <ImageId> <镜像版本号>"
    exit 1
fi

# 读取传入的参数
IMAGE_ID=$1
VERSION=$2

# 登录到阿里云 Docker Registry
docker login registry.cn-hangzhou.aliyuncs.com

# 构建 Docker 镜像
docker build -t sprint2_01:latest .

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo "Docker build failed."
    exit 1
fi

# 获取最新构建的镜像 ID
# 这里假设你的镜像名称是 sprint2_01:latest
# 如果你的镜像名称不同，请相应修改
LATEST_IMAGE_ID=$(docker inspect --format '{{.Id}}' sprint2_01:latest)

# 检查是否成功获取到 IMAGE_ID
if [ -z "$LATEST_IMAGE_ID" ]; then
    echo "Failed to get image ID after build."
    exit 1
fi

echo "Latest image ID is: $LATEST_IMAGE_ID"

# 打标签
docker tag $LATEST_IMAGE_ID registry.cn-hangzhou.aliyuncs.com/neue_galaxy/galaxy_dev:$VERSION

# 推送镜像到阿里云 Docker Registry
docker push registry.cn-hangzhou.aliyuncs.com/neue_galaxy/galaxy_dev:$VERSION
