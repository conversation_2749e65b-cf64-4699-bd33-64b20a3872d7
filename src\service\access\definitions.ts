/*
 * @Author: fangshi<PERSON>e <EMAIL>
 * @Date: 2025-01-24 09:11:25
 * @LastEditors: fangshijie <EMAIL>
 * @LastEditTime: 2025-01-24 11:35:39
 * @FilePath: \5.3fangshijie\src\service\access\definitions.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { number } from "echarts";

export interface CommonSortParams {
  sortBy?: string;
  sortOrder?: number;
}

export interface CommonPageQueryParams {
  limit?: number;
  page?: string;
  sorts?: CommonSortParams[];
}

export interface CreateRole {
  name: string; // 角色名称, 最大长度: 50
  description?: string; // 角色描述, 最大长度: 255
  isPre: number; // 是否预制(1预制，0自定义)
  roleType: number; // 角色类型(1 超管,2 管理员,3 普通用户,4 自定义)
  meta?: string | number; // 角色元数据, 最大长度: 50
  managedResources?: string[] | number[]; // 受管理的资源
}

export interface ListRoles {
  pageParams?: CommonPageQueryParams; // 分页公共参数
  name?: string; // 角色名称
}

export interface UpdateRole {
  name?: string; // 角色名称
  description?: string; // 角色描述
  managedResources?: string[] | number[]; // 受管理的资源
}

export interface RoleAttachUsers {
  userIds: string[] | number[]; // 用户ID列表, 最少1个
}
export interface AttachUserRole {
  roleId: string | number; // 角色ID
}

export interface RoleGetAccounts{
  accountName?: string; // 账户名称
  pageParams?: CommonPageQueryParams; // 分页公共参数
}
