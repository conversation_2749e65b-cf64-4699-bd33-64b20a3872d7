html,
body {
  position: relative;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  // min-height: 728px;
  overflow-y: auto;
  font-size: @text-0;
  font-family: -apple-system, BlinkMacSystemFont, PingFang SC, Source Han Sans SC,
    Helvetica Neue, Helvetica, Roboto, Arial, Hiragino Sans GB, Segoe UI,
    Microsoft YaHei, sans-serif;
  font-size: @text-0;
}

ul,
li,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Webkit 浏览器 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f0f0f0; /* 轨道颜色 */
}

::-webkit-scrollbar-thumb {
  background: #888; /* 滑块颜色 */
  border-radius: 10px; 
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
/* 单行省略 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* Firefox */
// html {
//   scrollbar-width: thin;
//   scrollbar-color: #888 #f0f0f0;
// }
