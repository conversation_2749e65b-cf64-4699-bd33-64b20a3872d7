<template>
  <i :class="icon"></i>
</template>

<script setup lang="ts">
import { ref, defineProps, onMounted, computed } from 'vue'
import { PartType } from '..'

// 定义组件属性
const props = defineProps({
  partType: {
    type: String,
    default: PartType.NeueASM
  }
})

let icon = computed(() => {
  console.log(props, 'props')
  if (props.partType == PartType.NeueASM) {
    return 'iconfont icon-tree-assembly !text-[18px] text-[#A578E4]'
  } else if (props.partType == PartType.NeuePrt) {
    return 'iconfont icon-setting  !text-[18px] text-[#92A6D4]'
  } else {
    return 'iconfont icon-a-3D-boom  !text-[18px] text-[#4FC3CB]'
  }
})
// 加载零部件数据
onMounted(() => {
  // 这里可以根据 props.partId 从 API 获取零部件详细信息
})
</script>

<style scoped lang="less">
.active-item {
  color: #1856eb;
  border-bottom: 2px solid #1856eb;
}
</style>
