<template>
  <div class="cax-icon-text">
    <i :class="['iconfont', iconConfig[props.type]]" 
       :style="{ color: colorConfig[props.type], fontSize: props.iconSize }"></i>
  </div>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default'
  },
  iconSize: {
    type: String,
    default: '16px'
  },
  textSize: {
    type: String,
    default: '14px'
  },
})

const iconConfig: Record<string, string> = {
  'NEUE_ASM': 'icon-tree-assembly',
  'DRAWING': 'icon-tree-drawing',
  'NEUE_PRT': 'icon-tree-part',
  'Normal': 'icon-tree-jiegou',
  'BOM': 'icon-bin',
}

const colorConfig: Record<string, string> = {
  'NEUE_ASM': '#9747FF',
  'DRAWING': '#EDB581',
  'NEUE_PRT': '#92A6D4',
  'Normal': '#31D0DC',
  'BOM': '#FDCD4B',
}
</script>
