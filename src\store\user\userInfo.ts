/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-29 14:27:56
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-13 13:11:28
 * @FilePath: /neue-cloud-vdi/src/store/user/userInfo.ts
 */

import { defineStore } from 'pinia'
import { login as loginService } from '@/service'
import { account as accountService } from '@/service'
import { isEmpty } from 'lodash-es'
import { Errors } from '@/common/filters/error'
import { UserAccess, UserDetail } from '@/common/types/console/user'
import { RoleDetail } from '@/common/types/console/role'
import { NavItem } from '@/common/types'
import { RouteRecordRaw } from 'vue-router'

/**
 * 用户信息状态
 */
type UserInfoState = {
  userInfo: UserDetail
  access: UserAccess
  userRoleDetail: Array<RoleDetail>
  userNavList: Array<NavItem>
  permissionList: Array<string>
  consoleNavList: Array<NavItem>
}

const state: UserInfoState = {
  userInfo: {
    id: 0,
    user: '',
    email: '',
    phone: '',
    role: '',
    ctime: '',
    createdTime: '',
    password: '',
    adPassword: '',
    organize: '',
    extra: '',
    tag: '',
    icon: '',
  },
  access: {
    uid: 0,
    userId: 1,
    user: '',
    role: '',
    roles: [],
    storeid: '',
    roleType: 0
  },
  userRoleDetail: [],
  userNavList: [],
  permissionList: [],
  consoleNavList: []
}

export const useUserInfoStore = defineStore('userInfo', {
  state: () => state,
  getters: {
    getUserInfo: (state) => state.userInfo,
    getAccess: (state) => state.access,
    getUserRoleDetail: (state) => state.userRoleDetail,
    getUserNavList: (state) => state.userNavList,
    getPermissionList(state) {
      return state.permissionList
    },
    getConsoleNavList: (state) => state.consoleNavList,
    // 是否是管理员
    isAdmin: (state) => {
      console.log('isAdmin', state.userInfo.role, state.access.role)
      return state.userInfo.role === 'admin' || state.access.role === 'admin'
    },

    // 是否有管理权限
    hasManageRole: (state) => {
      console.log('state.access.roles', state.access.roles, state.access)
      if (!state.access.roles || state.access.roles.length === 0) {
        return false
      }
      for (const role of state.access.roles) {
        console.log('role====>', role, Number(role.roleType) !== 3)
        if (Number(role.roleType) !== 3) {
          return true
        }
      }
      return false
    }
  },
  actions: {
    /**
     * 设置用户信息
     * @param data 用户数据
     */
    setUserInfo(data: UserDetail) {
      this.userInfo = data
    },
    setAccess(data: UserAccess) {
      console.log('UserAccess===>', data)
      this.access = data
    },
    setUserRoleDetail(data: Array<RoleDetail>) {
      this.userRoleDetail = data
    },
    setUserNavList(data: Array<NavItem>) {
      this.userNavList = data
      console.log('userNavList', this.userNavList)
    },
    setPermissionList(permissionList: Array<string>) {
      this.permissionList = permissionList
    },
    setConsoleNavList(data: Array<NavItem>) {
      this.consoleNavList = data
    },
    /**
     * 检查页面权限
     * @param force 是否强制请求接口
     * @returns
     */
    async checkAccess(force = false) {
      let access: any = this.access
      if (isEmpty(access) || force) {
        // 获取页面权限
        try {
          access = await accountService.postOperationsAccess()
          if (isEmpty(access)) {
            throw new Error(Errors.GET_ACCESS_FAILED)
          }
        } catch (err) {
          throw new Error(Errors.GET_ACCESS_FAILED)
        }
      }
      this.access = access
      return this.access
    }
  }
})
