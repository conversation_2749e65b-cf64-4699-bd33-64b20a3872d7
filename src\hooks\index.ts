import { ROLELIST } from '@/constant'
import { onBeforeUnmount, onMounted, Ref, ref } from 'vue'

export const useGetRoleType = () => {
  const getRoleList = async () => {
    return ROLELIST
  }
  return {
    roleListOp: ROLELIST.map((item: any) => {
      if (item.value == 3) {
        item['disabled'] = true
      }
      return {
        ...item
      }
    }),
    getRoleList
  }
}

export const useContextMenu = (ref: Ref) => {
  const onContextMenu = (event: MouseEvent) => {
    const cm = ref.value
    cm.show(event)
  }

  return {
    onContextMenu
  }
}

export function useResizeObserver<T extends HTMLElement>(elementRef: Ref<T | null>) {
  const width = ref(0)
  const updateWidth = () => {
    if (elementRef.value) {
      width.value = elementRef.value.getBoundingClientRect().width
    }
  }

  onMounted(() => {
    updateWidth()
    const resizeObserver = new ResizeObserver(updateWidth)
    if (elementRef.value) {
      resizeObserver.observe(elementRef.value)
    }
    onBeforeUnmount(() => {
      if (elementRef.value) {
        resizeObserver.unobserve(elementRef.value)
      }
    })
  })

  return {
    width
  }
}
