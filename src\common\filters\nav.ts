/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-29 12:01:10
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-30 23:08:15
 * @FilePath: /neue-cloud-vdi/src/common/filters/nav.ts
 */
import { NavItem } from '../types'
import shouye from '@/assets/images/nav/icon-sidebar-shouye.svg'
import shouyeActive from '@/assets/images/nav/icon-sidebar-shouye-xuanzhong.svg'
import user from '@/assets/images/nav/icon-general-user.svg'
import userActive from '@/assets/images/nav/icon-general-user-xuanzhong.svg'
export const nomalNavList: NavItem[] = [
  {
    name: '主页',
    isActive: false,
    // icon: 'icon-sidebar-shouye',
    // iconActive: 'icon-sidebar-shouye-xuanzhong',
    icon: shouye,
    iconActive: shouyeActive,
    path: '/home',
    highlight: ['home']
  },
]

export const consoleNavsList: Array<NavItem> = [
  {
    name: '用户管理',
    isActive: false,
    icon: user,
    iconActive: userActive,
    path: 'user-manage-main',
    children: [
      {
        name: '用户',
        isActive: false,
        path: 'user-manage',
        highlight: ['user-manage']
      }
    ]
  }
]
