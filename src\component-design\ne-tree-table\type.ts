import { PropType } from 'vue'
import NyTableProps, { nyTableProps } from '../ny-table/type'

export interface NeTreeTableProps<T = any> extends NyTableProps<T> {
  treeProps?: {
    children?: string
    hasChildren?: string
  }
  lazy?: boolean
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
  defaultExpandAll?: boolean
  expandRowKeys?: string[]
  rowKey?: string
  showExpandControls?: boolean
}

export const neTreeTableProps = {
  ...nyTableProps,
  treeProps: {
    type: Object as PropType<{
      children?: string
      hasChildren?: string
    }>,
    default: () => ({
      children: 'children',
      hasChildren: 'hasChildren'
    })
  },
  lazy: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  load: {
    type: Function as PropType<
      (row: any, treeNode: any, resolve: (data: any[]) => void) => void
    >,
    default: undefined
  },
  defaultExpandAll: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  expandRowKeys: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  rowKey: {
    type: String as PropType<string>,
    default: 'id'
  },
  showExpandControls: {
    type: Boolean as PropType<boolean>,
    default: true
  }
}

export default NeTreeTableProps
