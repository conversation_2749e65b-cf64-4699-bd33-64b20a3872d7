/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-12-06 15:04:42
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-10 11:58:50
 * @FilePath: /neue-cloud-vdi/src/router/auth-guard.ts
 */
import { RoleDetail, PermissionDetail } from '@/common/types/console/role'
import { NavItem } from '@/common/types/index'
import _ from 'lodash-es'

export type RouteDetail = {
  path: string
  name: string
  component?: any
  redirect?: string
  key?: string
}

export type RouteKeyMap = {
  [key: string]: string
}

export type permissionType = {
  [key: string]: Array<string>
}

export const allPermissions: permissionType = {
  用户管理: ['user-manage'],
  用户组管理: ['group-manage'],
  角色管理: ['role-manage', 'role-create'],
  应用管理: ['app-manage', 'app-manage-list', 'app-create', 'app-manage-list-basic'],
  镜像管理: ['image-manage', 'image-create'],
  资源管理: ['instance']
}

export function checkPermissionExists(path: string) {
  for (const permissions of Object.values(allPermissions)) {
    if (permissions.includes(path)) {
      return true
    }
  }
  return false
}

export function extractUniquePermissions(roles: Array<RoleDetail>) {
  const permissionsSet = new Set()
  const result: Array<PermissionDetail> = []

  roles.forEach((role) => {
    // 检查角色是否有权限
    if (role.permissions && role.permissions.managers) {
      role.permissions.managers.forEach((permission) => {
        const permissionStr = JSON.stringify(permission)

        // 检查 Set 中是否已经有这个权限对象
        if (!permissionsSet.has(permissionStr)) {
          permissionsSet.add(permissionStr)
          result.push(permission)
        }
      })
    }
  })
  return result
}

export function mapPermissionsToPaths(resources: Array<PermissionDetail>) {
  const paths: Array<string> = []

  resources.forEach((resource) => {
    // 检查 allPermissions 对象中是否存在当前资源的名称
    if (allPermissions[resource.resourceName]) {
      paths.push(...allPermissions[resource.resourceName])
    }
  })

  return paths
}

export function filterNavs(menuData: Array<NavItem>, pathList: Array<string>) {
  const tmpMenuData = _.cloneDeep(menuData)
  return tmpMenuData.filter((item) => {
    // 如果当前项的 path 存在于 pathList 中，直接保留
    if (pathList.includes(item.path)) {
      return true
    }

    // 如果当前项有子项，检查子项中的 path 或 highlight 是否有匹配的内容
    if (item.children && item.children.length > 0) {
      item.children = item.children.filter(
        (child) =>
          pathList.includes(child.path) ||
          (child.highlight && child.highlight.some((path) => pathList.includes(path)))
      )
      // 如果子项中有匹配项，保留该项
      return item.children.length > 0
    }

    // 默认情况下，如果没有匹配项，过滤掉该项
    return false
  })
}

// export function filterNavs1(navs: Array<NavItem>, paths: Array<string>) {
//   // 一个highlight数组是否至少有一个元素在paths中
//   function isHighlightValid(highlight: Array<string>) {
//     return highlight.some((item) => paths.includes(item))
//   }

//   function filterChildren(children: Array<NavItem>) {
//     return children.filter((child: any) => {
//       if (child.highlight && !isHighlightValid(child.highlight)) {
//         // 如果highlight数组不符合条件，过滤掉该子项
//         return false
//       }
//       // 如果有children，继续递归检查
//       if (child.children && child.children.length > 0) {
//         child.children = filterChildren(child.children)
//         // 如果子项都被过滤掉了，也过滤掉该父项
//         return child.children && child.children.length > 0
//       }
//       return true
//     })
//   }

//   return navs.filter((nav) => {
//     if (nav.highlight && !isHighlightValid(nav.highlight)) {
//       // 如果nav项的highlight数组不符合条件，过滤掉该项
//       return false
//     }

//     if (nav.children && nav.children.length > 0) {
//       nav.children = filterChildren(nav.children)
//       return nav.children && nav.children.length > 0
//     } else if (nav.children && nav.children.length === 0) {
//       return false
//     }
//     return true
//   })
// }
