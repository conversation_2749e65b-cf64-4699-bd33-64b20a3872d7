<template>
  <el-dialog
    v-model="visible"
    :min-width="'230px'"
    draggable
    :show-close="false"
    :style="{ paddingTop: '0' }"
    class="custom-measurement-dialog"
  >
    <!-- 自定义 header -->
    <template #header="">
      <div class="dialog-header">
        <div class="dialog-title">{{ title }}</div>

        <div class="dialog-operations">
          <i v-if="title === '测量对象'" class="iconfont icon-tips-delete" @click="handleDelete"></i>
          <i class="iconfont icon-tips-close" style="margin-left: 16px" @click="handleClose"></i>
        </div>

        <div class="hover-line"></div>
      </div>
    </template>

    <!-- 插槽内容 -->
    <slot />
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '对话框'
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

const visible = ref(props.modelValue)

// 同步 visible 与 modelValue
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
  }
)

// 同步 visible 到父组件
watch(
  visible,
  (newVal) => {
    emit('update:modelValue', newVal)
  }
)

// 点击关闭按钮
const handleClose = () => {
  visible.value = false
  emit("close")
}

// 点击删除按钮
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="less" scoped>
.dialog-header {
  // width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .dialog-title {
    font-size: @text-2;
    font-weight: @font-weight-2;
  }

  .dialog-operations {
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .hover-line {
    position: absolute;
    left: 45%;
    top: 2px;
    width: 40px;
    height: 6px;
    border-radius: 8px;
    opacity: 1;
    background: @border-color-1;
  }
}

// 覆盖 el-dialog 默认样式
::v-deep(.custom-measurement-dialog .el-dialog__header) {
  margin-bottom: 0 !important;
}

::v-deep(.custom-measurement-dialog .el-dialog__headerbtn) {
  top: 10px;
}
</style>