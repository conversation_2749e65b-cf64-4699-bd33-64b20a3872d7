<template>
  <el-dialog
    title="新建项目协作区"
    v-model="localVisible"
    :show-close="true"
    :style="{
      width: '520px',
      height: '200px'
    }"
    class="dialog-container"
    @close="handleClose"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :append-to-body="true"
  >
    <el-form id="dialog-space-add" ref="ruleFormRef" :model="ruleForm" :rules="rules" >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="ruleForm.name"
          placeholder="必须以字母或中文开头"
          show-word-limit
          minlength="1"
          maxlength="64"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, defineEmits, defineProps, watch } from 'vue'
import { ElMessage, FormInstance, type FormRules } from 'element-plus'
import httpApi from '@/service/http'
import { validateNameStart } from '@/utils/validate'
import cdp from '@/service/cdp';
import { CollaborationZone } from '@/common/types/space'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'handleCreated'])

const localVisible = ref(props.visible)
const ruleFormRef = ref<FormInstance>()

const ruleForm: CollaborationZone = reactive({
  name: '',
  type: 'COMMON'
})

const validateNameStr = (rule: any, value: any, callback: any) => {
  const msg = '支持1-64位字符，必须以字母或中文开头'
  console.log('value==', value)
  console.log('rule', rule)
  if (value === '') {
    console.log('value', value)
    callback(new Error(msg))
  } else if (value.length < 1 || value.length > 64) {
    callback(new Error(msg))
  } else if (!validateNameStart(value)) {
    callback(new Error(msg))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  name: [{ required: true, trigger: 'blur', validator: validateNameStr }],
})

const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

const resetForm = () => {
  ruleFormRef.value?.resetFields()
  ruleForm.name = ''
}
const handleConfirm = async () => {
  await ruleFormRef.value?.validate((valid) => {
    if (valid) {
      handleSubmit()
    } else {
      ElMessage.error('表单验证失败')
    }
  })
}

const handleSubmit = async () => {
  try {
    const res = await cdp.postPlt0ProjectCreate(ruleForm)
    if (res) {
      ElMessage.success('创建成功')
      emit('handleCreated', res)
      handleClose()
    }
  } catch (error) {
    ElMessage.error('创建失败: ')
  }
}


watch(
  () => props.visible,
  (newVal: boolean) => {
    localVisible.value = newVal
  },
  { immediate: true }
)
</script>
<style lang="less">
.dialog-container {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    flex: 1;
  }
}
.dialog-footer {
  text-align: right;
}
</style>
