import axios from 'axios'
import { useRouter } from 'vue-router'
import storage from '@/utils/storage'
import { ElMessage } from 'element-plus'
import EventBus from '@/utils/event-bus'
import baseConfig from './base-config'

// 所有请求携带凭证
axios.defaults.withCredentials = true
/**
 * 基础请求服务
 */
console.log(baseConfig)
const axiosIns = axios.create({
  baseURL: baseConfig.baseUrl,
  timeout: 10000,
  headers: {}
})
// 请求拦截器
axiosIns.interceptors.request.use(
  (config) => {
    const header = config.headers
    config.headers = {
      ...header,
      'Content-Type': 'application/json',
      Authorization: `Bearer ${storage.get('token')}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器即异常处理
axiosIns.interceptors.response.use(
  function (res) {
    let { data, response, ...otherRes } = res.data
    const { code } = response
    if (res.status === 200 && code == 0) {
      let result = {}
      /*单个json {
      data: {} ,//后端所有东西
      //预留
      code: xxx,
      msg:xxx,
      ...
      }*/
      /*list格式 {
      data: [{},{}] ,//后端所有东西
      //预留
      totle:xxx,
      pageSize:xxx,
      limit:xxx,
      code: xxx,
      msg:xxx,
      ...
      }*/

      if (Array.isArray(data)) {
        result = { data: data, ...otherRes }
      } else {
        result = { data }
      }
      return Promise.resolve(result)
    } else {
      if (code === 104 || code === 401) {
        EventBus.emit('ROUTER_CHANGE', { path: '/login' })
      } else {
        //接口不需要报错提醒axios  传config参数
        if (!res.config?.skipErrorHandler) {
          ElMessage({
            message: response.message,
            type: 'error'
          })
        }
      }
      return Promise.reject(res.data)
    }
  },
  function (error: any) {
    if (error.response.statusText) {
      let { statusText, data,config } = error.response
      if (error.response && error.response.status === 401) {
        EventBus.emit('ROUTER_CHANGE', { path: '/login' })
      } else {
        if (config?.skipErrorHandler) return Promise.reject(error.response);
        let { response, code } = data
        if (true) {
          ElMessage.error(response.message||statusText)
        } else {
          ElMessage.warning(response.message||statusText)
        }
        return Promise.reject(error.response)
      }
    }
  }
)
export default axiosIns
