<template>
    <div class="cax-icon-text">
            <i :class="['iconfont', iconConfig[props.type]]" 
                :style="{color: colorConfig[props.type], fontSize: props.iconSize }"></i>
            <el-tooltip effect="dark" :content="props.content" placement="top" :show-after="500" :show-arrow="true" :offset="6">
                <span :style="{fontSize: props.textSize}">{{ props.content }}</span>
        </el-tooltip>
    </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
const props = defineProps({
    type: {
        type: String,
        default: 'default'
    },
    content: {
        type: String,
        default: ''
    },
    iconSize: {
        type: String,
        default: '16px'
    },
    textSize: {
        type: String,
        default: '14px'
    },
})



const iconConfig: Record<string, string> = {
  'NEUE_ASM': 'icon-tree-assembly',
  'DRAWING': 'icon-tree-drawing',
  'NEUE_PRT': 'icon-tree-part',
  'Normal': 'icon-tree-jiegou',
    'BOM':'icon-bin',
    'root': 'icon-a-3D-mesure-volume1',
};

const colorConfig: Record<string, string> = {
  'NEUE_ASM': '#9747FF',
  'DRAWING': '#EDB581',
  'NEUE_PRT': '#92A6D4',
  'Normal': '#31D0DC',
  'BOM':'#FDCD4B',
    'root': '#334155',
};

</script>

<style lang="less">
.el-tooltip {
    .el-popper__arrow {
  display: block !important;
}
}

</style>


<style lang="less" scoped>
.cax-icon-text {
    display: flex;
    align-items: center;
    .iconfont {
        margin-right: 4px;
    }
    span{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        max-width: 210px;
    }
}
</style>