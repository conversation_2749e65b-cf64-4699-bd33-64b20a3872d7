<template>
  <el-dialog class="ny-dialog" v-bind="props" :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)" @close="$emit('update:modelValue', false)"
    style="--el-dialog-title-font-size: 16px; --el-text-color-primary: #0f172a;--el-dialog-padding-primary:20px">
    <template #header>
      <slot name="header"></slot>
    </template>
    <slot />
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { DialogProps } from 'element-plus'

interface NyDialogProps extends Omit<DialogProps, 'modelValue'> {
  modelValue: boolean;
}

const props = withDefaults(defineProps<NyDialogProps>(), {
  showClose: true,
  modal: true,
  modelValue: false
})

defineEmits(['update:modelValue'])
</script>
