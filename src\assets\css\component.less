/* 面包屑 */
.el-breadcrumb {
  font-size: @text-0 !important;
}

.el-breadcrumb__item {
  line-height: 30px !important;
}

.el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
  color: @text-color-2 !important;
}

.el-breadcrumb__inner a:hover,
.el-breadcrumb__inner.is-link:hover {
  color: @link-6 !important;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: @text-color-5 !important;
}
/* popconfirm */
.el-popconfirm {
  padding: 8px 12px;
}

.el-popconfirm .el-icon {
  font-size: @text-4;
  margin-right: 12px;
}
.el-popover {
  position: absolute;
  top: 0;
  left: 0;
  font-size: @text-0 !important;
  z-index: 99999;
}
/* 树 */
.el-tree {
  background-color: transparent !important;
}

.el-tree-node__content {
  height: 32px !important;
  line-height: 32px !important;
  width: auto;
}

.el-tree-node__content:hover,
.el-tree-node__content .custom-node-content:hover {
  background-color: @link-2 !important;
}

.el-tree-node__content:hover,
.el-tree-node__content .custom-node-content:hover {
  background-color: @primary-1 !important;
  .custom-tree-node .iconfont {
      color: @text-color-3 !important;
    }
}
.is-current>div:first-child,
.is-current>.el-tree-node__content .custom-node-content {
  background-color: @primary-2 !important;
}

.el-tree-node__content .custom-tree-node {
  height: 32px !important;
  line-height: 32px !important;
  color: @text-color-2 !important;
}
.el-tree-node__content .custom-tree-node .iconfont {
  margin-right: 6px;
  color: @text-color-3 !important;
}
.el-tree-node__expand-icon {
  color: @text-color-3 !important;
}

/* 侧边栏 */
.el-aside {
  border-radius: @border-radius-3 !important;
}
/* drawer 抽屉 */
.el-drawer__header {
  margin-bottom: 0 !important;
}

/* select */
.el-select-dropdown .el-select-dropdown__item {
  padding: 0 12px;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  background-color: @primary-1;
}

.el-select-dropdown .el-select-dropdown__item.is-active {
  background-color: @primary-2;
  color: @link-6;
}

.el-dropdown-menu__item:not(.is-disabled):focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: @link-2 !important;
  color: @link-6 !important;
  cursor: pointer;
}

.el-dropdown-menu__item:not(.is-disabled):active {
  background-color: @link-1 !important;
  color: @text-color-2;
}
.el-select__wrapper.is-hovering,
.el-select__wrapper.is-focused {
  border-color: @link-5 !important;
  box-shadow: 0 0 0 1px @link-5 !important;
  color: @link-6 !important;
}

.el-select-dropdown__item {
  font-size: @text-0 !important;
}

.el-select-dropdown__item.is-hovering,
.el-select-dropdown .el-select-dropdown__item.is-hovering {
  background-color: @primary-2;
  color: @link-6 !important;
}
/* 下拉树 */
/* tag */

.el-tag.text-card {
  background-color: @link-2 !important;
}
.el-tag--plain, .el-tag--plain.el-tag--primary {
  --el-tag-bg-color:#EDF2FC !important;
  --el-tag-border-color: @link-6 !important;
  --el-tag-text-color: @link-6 !important;
}
/* upload */
.el-upload-dragger {
  border-radius: @border-radius-2 !important;
}
.el-upload__tip {
  color: @text-color-5 !important;
}
/* menu */
.el-menu {
  background: 0 none !important;
  border: 0 none !important;

  .el-menu-item {
    margin-top: 4px;
    color: @text-color-2;
    font-size: @text-1;
    height: 44px !important;

    .iconfont {
      margin-right: 12px;
      font-size: 22px;
      color: @text-color-1 !important;
        opacity: 0.45 !important;
    }

    &.active,
    &:hover {
      color: @primary-6;
      font-weight: @font-weight-2;
      background-color: @other-color-2;
      border-radius: @border-radius-3;

      .iconfont {
        color: @primary-6;
          // color: transparent !important;
        font-weight: @font-weight-1;
      }
    }
  }
}

.el-menu-item.is-active {
  border-radius: @border-radius-3 !important;
}

.el-sub-menu {
  margin-top: 4px;

  .el-sub-menu__title {
    height: 44px;
      line-height: 44px;
    .iconfont {
      margin-right: 12px;
      font-size: 22px;
    }

    &.active,
    &:hover {
      color: @primary-6;
      font-weight: @font-weight-2;
      background-color: @other-color-2;
      border-radius: @border-radius-3 !important;

      .iconfont {
        color: @primary-6;
        font-weight: @font-weight-1;
      }
    }
  }

  .el-menu-item {
    padding-left: 52px !important;
  }

  &.childactive {
    color: @primary-6;
    font-weight: @font-weight-2;
    background-color: 0 none;

    .el-sub-menu__title {
      color: @primary-6;
    }

    .iconfont {
      color: @primary-6;
      font-weight: @font-weight-1;
    }
  }
}

.el-sub-menu .el-sub-menu__title:hover,
.el-menu .el-menu-item:hover {
  background-color: @other-color-1;
}

.el-menu-item .menu-icon,
.el-sub-menu .menu-icon {
  margin-right: 12px;
  width: 22px !important;
  height: 22px !important;
}
/* tabs */
.el-tabs {
  flex-direction: column-reverse !important;
}

.el-tabs__nav-wrap:after {
  height: 1px !important;
}

.el-tabs__item:active {
  color: @link-7;
}

.el-tabs__item.is-active {
  color: @link-6;
}

.el-tabs__item:hover {
  color: @link-6;
}

.el-menu-tabs.el-menu--horizontal.el-menu {
  height: 38px;
  line-height: 38px;
  padding: 0;
  margin: 0;
}

.el-menu-tabs.el-menu-tabs.el-menu--horizontal>.el-menu-item {
  height: 36px !important;
  line-height: 36px !important;
  padding: 0;
}

.el-menu-tabs.el-menu--horizontal>.el-menu-item:hover {
  color: @link-5 !important;
  font-weight: @font-weight-1;
  background: none !important;
}

.el-menu-tabs.el-menu--horizontal>.el-menu-item.is-active {
  border-radius: 0 !important;
  color: @link-6 !important;
  font-size: @text-1 !important;
  border-bottom-color: @link-6 !important;
  background: none !important;
}
/* 警示条 */
.el-message {
  top: 56px !important;
  height: 34px;
  line-height: 34px;
  border: 0 none !important;
}

.el-message .el-message__content {
  font-size: @text-0 !important;
}

.el-message .el-message__icon {
  font-size: 14px !important;
  color: @warning-6 !important;
}

.el-message .el-message__closeBtn {
  font-size: @text-0 !important;
}

.el-message.el-message--warning {
  background: @warning-1 !important;
}

.el-message--warning .el-message__content {
  color: @warning-5 !important;
}

.el-message .el-message-icon--warning {
  color: @warning-5 !important;
}

.el-message.el-message--error {
  background: @danger-1 !important;
  color: @danger-6 !important;
}

.el-message .el-message-icon--error {
  color: @danger-6 !important;
}

.el-message.el-message--success {
  background: @success-1 !important;
}

.el-message--success .el-message__content {
  color: @success-6 !important;
}

.el-message .el-message-icon--success {
  color: @success-6 !important;
}

.el-message.el-message--info {
  background: @link-1 !important;
}

.el-message--info .el-message__content {
  color: @text-color-2 !important;
}

.el-message .el-message-icon--info {
  color: @link-6 !important;
}
/*
* message-box 消息框，此处和接口异常弹的toas整体风格有差别
*/

/* 面包屑 */
.el-breadcrumb {
  .el-breadcrumb__inner {
    color: @text-color-2;
    font-weight: @font-weight-2;
    font-size: @text-0;

    &:hover {
      color: @primary-6;
    }
  }

  .el-breadcrumb__inner:last-child {
    color: @text-color-5;
  }
}
/* 气泡窗 */
.el-popover__title {
  font-size: @text-1;
}

/* 弹窗 */
div.el-dialog {
  padding-left: 28px;
  padding-right: 28px;
  .el-dialog__title {
    font-weight: @font-weight-3;
  }
}
.el-dialog__headerbtn .el-dialog__close {
  color: @text-color-2 !important;
}

.el-dialog__headerbtn .el-dialog__close:hover {
  color: @link-6 !important;
}

/* 输入框 */
.el-input__wrapper {
  .el-input__inner {
    color: @text-color-2;
  }
}
.el-input__wrapper.is-focus {
  box-shadow:0 0 0 1px @link-5 inset !important
}

.el-button:hover {
  background-color: @primary-1 !important;
  color: @primary-5 !important;
}

.el-button:active {
  background-color: @primary-2 !important;
  color: @primary-7 !important;
}

/* primary 主要按钮 */
.el-button--primary {
  color: @color-white !important;
    background-color: @primary-6 !important;
    border-color: @primary-6 !important;
  }

.el-button--primary:hover {
  color: @color-white !important;
  background-color: @primary-5 !important;
  border-color: @primary-5 !important;
}

.el-button--primary:active {
  background-color: @primary-7 !important;
  border-color: @primary-7 !important;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover {
  background-color: @fill-color-2 !important;
  border: 0 none !important;
    color: @text-color-6 !important;
}
/* plain 次要按钮 */
.el-button--primary.is-plain {
  color: @primary-6 !important;
  background-color: @color-white !important;
    border-color: @primary-4 !important;
  }

.el-button--primary.is-plain:hover {
  background-color: @primary-2 !important;
  border-color: @primary-2 !important;
}

.el-button--primary.is-plain:active {
  background-color: @primary-2 !important;
  border-color: @primary-2 !important;
  color: @primary-7 !important;
}

.el-button--primary.is-plain.is-disabled,
.el-button--primary.is-plain.is-disabled:hover {
  background-color: @fill-color-2 !important;
  border-color: @border-color-1 !important;
    color: @text-color-6 !important;
  }

/* link 链接按钮、文字按钮 */
.el-button--primary.is-link,
.el-button--primary.is-link:hover,
.el-button--primary.is-link:active,
.el-button--primary.is-link.is-disabled {
  color: @link-6 !important;
  background-color: transparent !important;
  border-color: transparent !important;
}

.el-button--primary.is-link:hover {
  color: @link-5 !important;
}

.el-button--primary.is-link:active {
  color: @link-7 !important;
}

.el-button--primary.is-link.is-disabled {
  color: @text-color-6 !important;
}
/* table */

.el-table thead th{
  color: @text-color-2 !important;
  font-weight: @font-weight-2;
}

.el-table .cell {
  line-height: 18px;
}

.el-pager li {
  min-width: 20px !important;
  height: 20px !important;
    line-height: 20px !important;
  color: @text-color-5 !important;
  margin: 0 1px;
  font-size: 12px !important;
}

.el-pager li:hover {
  background: @link-1 !important;
    min-width: 20px;
      height: 20px;
      line-height: 20px;
}

.el-pager li.is-active {
  color: @link-6 !important;
  background: @link-2 !important;
  padding: 0 4px;
  width: auto !important;
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  font-weight: @font-weight-2 !important;
}

.el-select-dropdown__item.is-selected {
  color: @link-6 !important;
  background: @link-2;
  font-weight: @font-weight-1;
}

/* tooltip 气泡 */
.el-tooltip__popper {
  color: @text-color-2;
  background-color: @color-white;
  border: 1px solid @border-color-1;
}

.el-tooltip__trigger {
  cursor: default;
}
.el-tooltip__popper.is-dark {
  background-color: @text-color-2;
  color: @color-white;
}
/* 单选按钮 */
.el-radio__inner:hover {
  border-color: @link-6 !important;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: @link-6 !important;
  background: @link-6 !important;
}
.el-radio__input.is-checked+.el-radio__label {
  color: @link-6 !important;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border-color: @link-6 !important;
  background: @link-6 !important;
}

.el-table th.el-table__cell>.cell.highlight {
  color: @text-color-2 !important;
}
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: @text-color-2 !important;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  border-color: @link-6 !important;
  background: @link-6 !important;
}
.el-checkbox__inner:hover {
  border-color: @link-5 !important;
}

.el-checkbox__input.is-disabled .el-checkbox__inner,
.el-checkbox__input.is-disabled .el-checkbox__inner:hover {
  background-color: @fill-color-2 !important;
  border-color: @fill-color-2 !important;
}
.el-table-filter__checkbox-group {
  padding: 4px 0 !important;
}

.el-table-filter__checkbox-group label.el-checkbox {
  font-weight: @font-weight-1 !important;
  line-height: 32px !important;
  height: 32px !important;
  margin: 0 !important;
  padding: 0 12px;
}

.el-table-filter__checkbox-group label.el-checkbox:hover {
  background-color: @primary-1 !important;
}

.el-table-filter__checkbox-group label.el-checkbox:active {
  background-color: @primary-2 !important;
}

.el-table-filter__content .el-checkbox__input {
  width: 16px !important;
  height: 16px !important;
}

.el-table__column-filter-trigger {
  width: 20px;
  height: 20px;
  background-image: url('@/assets/images/icon-filter.svg') !important;
  background-size: 13px 13px !important;
  background-repeat: no-repeat;
  background-position: 4px 7px;

  .el-icon {
    display: none !important;
  }
}

.cell.highlight {
  .el-table__column-filter-trigger {
    background-image: url('@/assets/images/icon-filter-active.svg') !important;
  }
}

.el-table-filter__bottom {
  height: 24px;
}

.el-table-filter__bottom button {
  color: @color-white !important;
  background-color: @primary-6 !important;
  border-color: @primary-6;
  border-radius: @border-radius-2;
  font-size: 12px;
  width: 40px;
  height: 24px;
  float: right;
  margin-left: 4px;

  &:hover {
    background-color: @primary-5 !important;
    border-color: @primary-5 !important;
  }

  &:active {
    background-color: @primary-7 !important;
    border-color: @primary-7 !important;
  }

  &.is-disabled,
  &.is-disabled:hover {
    background-color: @border-color-2 !important;
    border-color: @border-color-2 !important;
    color: @text-color-6 !important;
  }
}

.el-table-filter__bottom button:nth-child(2) {
  color: @primary-6 !important;
  background-color: @color-white !important;
  border: solid 1px @primary-4 !important;

  &:hover {
    background-color: @primary-2 !important;
    border-color: @primary-2 !important;
  }

  &:active {
    background-color: @primary-2 !important;
    border-color: @primary-2 !important;
    color: @primary-7 !important;
  }

  &.is-disabled,
  &.is-disabled:hover {
    background-color: @color-white !important;
    border-color: @border-color-1 !important;
    color: @text-color-6 !important;
  }
}

.el-table__expanded-cell {
  padding: 0 !important;
  }

  .td-content-table {
    padding: 10px 10px 10px 48px !important;
}

.td-content-table,
.td-content-table * {
  background-color: @bg-color-2 !important;
}

.el-table__expanded-cell th,
.el-table__expanded-cell tr {
  // background: @bg-color-2 !important;
    // background: red !important;
  border-color: @border-color-2 !important;
}
// ::v-deep .el-table__row:hover {
//   background-color: red !important;
// }

.el-table--border .el-table__inner-wrapper:after,
.el-table--border:after,
.el-table--border:before,
.el-table__inner-wrapper:before {
  background-color: inherit !important;
}

.el-table .sort-caret.ascending {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-bottom: 4px solid transparent;
  border-top: 6px solid transparent;
  border-bottom-color: @text-color-3 !important;
}

.el-table .sort-caret.descending {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-bottom: 6px solid transparent;
  border-top: 4px solid transparent;
  border-top-color: @text-color-3 !important;
}
.el-table th .cell {
  display: flex;
  line-height: 26px;
  align-items: center;
  }

  .el-table__column-filter-trigger {
    margin-left: 4px;
    background-position: 0;
}
/* 行悬浮-hover */
.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
  background-color: @primary-1 !important;
}
/* 文本域 */
.el-textarea {
  font-size: @text-0 !important;
}


/* 表单 */
.el-form-item__label {
  font-size: @text-0 !important;
}
/* 穿梭框 */
.el-transfer {
  height: 100% !important;
  display: flex !important;
}

.el-transfer-panel {
  height: 100% !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

.el-transfer-panel__body {
  flex: 1 !important;
}

.el-transfer__buttons {
  display: flex !important;
  align-items: center !important;
  height: 100%;
}
.el-checkbox.el-transfer-panel__item:hover {
  color: @text-color-2;
    background-color: @primary-1 !important;
}
.el-transfer-panel__header .el-checkbox__label {
  font-size: @text-0 !important;
}
.el-transfer-panel__list.is-filterable {
  overflow: auto !important;
}
.transfer-item {
  display: flex;
  justify-content: space-between;
  .transfer-item-label span,
    .transfer-item-label-user .header {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    margin-right: 8px;
    background-color: #92a6d4;
    color: #fff;
  }
}
div.el-popover.el-popper{
  min-width: auto ;
  width: auto !important;
}

//form-item
.form-item-start{
  .el-form-item__label{
    line-height: normal;
  }
  .el-form-item__content{
    align-items: flex-start;
  }
}