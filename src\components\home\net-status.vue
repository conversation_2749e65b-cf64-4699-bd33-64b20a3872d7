<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-10-24 17:13:12
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-11-29 15:27:16
 * @FilePath: /neue-cloud-vdi/src/components/home/<USER>
-->
<template>
  <div class="net-status">
    <div class="chart-bar">
      <ChartBar />
    </div>
    <div class="chart-curve chart-curve-up">
      <img src="@/assets/images/home/<USER>" width="32" height="32" alt="" />
      <div class="chart-data">
        <div class="chart-data-title">上行带宽</div>
        <div class="chart-data-detail">
          <span>124</span>mbps
        </div>
      </div>
      <div class="chart-curve-line">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
    </div>
    <div class="chart-curve chart-curve-down">
      <img src="@/assets/images/home/<USER>" width="32" height="32" alt="" />
      <div class="chart-data">
        <div class="chart-data-title">下行带宽</div>
        <div class="chart-data-detail">
          <span>124</span>mbps
        </div>
      </div>
      <div class="chart-curve-line">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import ChartBar from '@/components/home/<USER>'
</script>

<style lang="less" scoped>
.net-status {
  height: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-left: 24px;
  padding-right: 24px;
  .chart-bar {
    height: 40%;
  }
  .chart-curve {
    margin-top: 20px;
    // width: 100%;
    height: 30%;
    min-height: 70px;
    border-radius: 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 24px;
  }
  .chart-curve.chart-curve-up {
    background: #C8D3EA;
  }
  .chart-curve.chart-curve-down {
    color: #fff;
    margin-top: 12px;
    background: #262A33;
  }
  .chart-data {
    margin-left: 16px;
    text-align: left;
  }
  .chart-data-detail span{
    font-size: @text-5;
    line-height: 36px;
    margin-right: 8px;
  }
  .chart-curve-line {
    flex: 1;
    margin-left: 16px;
    img {
      min-width: 50px;
      width: 80%;
    }
  }
}
</style>
