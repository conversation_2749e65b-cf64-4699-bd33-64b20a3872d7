<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-12-11 15:04:10
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-13 10:55:29
 * @FilePath: /neue-cloud-vdi/src/component-design/ny-drawer/index.vue
-->
<template>
  <el-drawer class="ny-drawer" size="640" v-bind="$attrs" v-model="drawerValue" :destroy-on-close='true'>
    <template #header>
      <slot name="header" />
    </template>
    <template #default>
      <slot />
    </template>
    <template #footer>
      <slot name="footer" />
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { defineProps, defineEmits, computed } from 'vue'
const props = defineProps({
  modelValue: { type: Boolean, required: true }
})
const emit = defineEmits(['update:modelValue'])
const drawerValue = computed({
  get: () => props.modelValue,
  set: (val: string | number) => {
    emit('update:modelValue', val)
  }
})
</script>

<style lang="less">
.ny-drawer {
  .el-drawer__header {
    font-size: @text-2;
    color: @text-color-1;
    font-weight: @font-weight-3;
    margin-bottom: 0;
  }

  .el-drawer__footer {
    text-align: left;
  }
  &.el-dialog {
    --el-dialog-padding-primary: 20px;
    padding-left: 28px;
    padding-right: 28px;
  }
}
</style>
