<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-10-17 17:20:50
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-08 17:21:33
 * @FilePath: /neue-cloud-vdi/src/components/user/reset-pwd.vue
-->
<template>
  <el-dialog
    v-model="props.visible"
    title="重置密码"
    :style="{
      width: '520px',
      height: '184px'
    }"
    :before-close="handleClose"
    class="dialog-reset-pwd"
    :close-on-click-modal="false"
  >
    <template #default>
      <div class="dialog-content">
        <div class="dialog-title">
          <div class="dialog-title-text">
            <i class="iconfont icon-exclamation-circle-fill"></i>
            <span>确定要为以下用户重置密码？</span>
          </div>
          <div class="dialog-title-desc">用户：{{ userName }}</div>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { account as accountService } from '@/service';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';


const emit = defineEmits(['update:visible', 'handleSaveComplete'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const userName = ref('')
const userId = ref(-1)
const userEmail = ref('')

const handleClose = () => {
  emit('update:visible', false)
}

const handleCancel = () => {
  handleClose()
}

const handleSave = () => {
  accountService.postOperationsResetPassword({
    userId: userId.value,
    email: userEmail.value,
    user: userName.value
  }).then((res: any) => {
    console.log('resetPwd', res)
    if (res) {
      ElMessage.success('密码重置成功')
      emit('handleSaveComplete')
      handleClose()
    }
  })
}

const show = (resetDetail: any) => {
  userName.value = resetDetail.user
  userId.value = resetDetail.userId
  userEmail.value = resetDetail.email
  emit('update:visible', true)
}

defineExpose ({
  show
})
</script>

<style lang="less" scoped>
.dialog-reset-pwd {
  .dialog-content {
    display: flex;
    flex-direction: column;
    .dialog-title {
      text-align: left;
      font-size: @text-1;
      color: @text-color-2;
    }
    .dialog-title-text {
      color: @text-color-2;
      line-height: 24px;
      i {
        margin-right: 12px;
      }
    }
    .dialog-title-desc {
      padding-left: 32px;
      line-height: 18px;
    }
  }
}
</style>
