<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-11-06 16:43:36
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-30 13:26:00
 * @FilePath: /neue-cloud-vdi/src/views/console/users/group.vue
-->
<template>
  <el-main class="main-content main-depart">
    <!-- <div class="depart-container page-container" style="display: flex; flex-direction: column;"> -->
      <div class="container-title">
        <span>用户组</span>
      </div>
      <div class="depart-content">
        <div class="depart-left">
          <div class="depart-title">
            <span>用户组</span>
            <i class="iconfont icon-edit-add" @click="handleCreateGroup"></i>
          </div>
          <div class="depart-filter">
            <el-input
              id="filterDepart"
              v-model="filterDepartName"
              class="filter-depart"
              placeholder="请输入名称进行模糊查询"
              :clearable="true"
              :clear="handleClearFilters"
              @clear="handleClearFilters"
              @keyup.enter="handleSearchIconClick"
            >
              <template v-slot:suffix>
                <i
                  id="searchIcon"
                  class="iconfont icon-interactive-search"
                  @click="handleSearchIconClick"
                ></i>
              </template>
            </el-input>
          </div>
          <div class="depart-tree">
            <TreeList
              ref="treeListRef"
              :filterText="filterDepartName"
              @getTreeListComplete="getTreeListComplete"
              @handleGroupActive="handleGroupActive"
            ></TreeList>
          </div>
        </div>
        <div class="depart-right">
          <template v-if="showUserManage">
            <UserManage :activeGroup="activeGroup" @handleRefresh="handleRefresh"></UserManage>
          </template>
          <div v-else class="depart-nodata">
            <div class="nodata-container">
              <img src="@/assets/images/group/group-none.svg" width="64" alt="">
              <div class="nodata-title">{{  treeData && treeData.length > 0 ? treeData[0].label : ''  }}</div>
              <div class="nodata-desc">暂无用户组，<el-button id="redirectBtn1" type="primary" class="console-btn" link @click="handleCreateGroup">
                  请创建
                </el-button>
              </div>
            </div>
          </div>
        </div>
    </div>
  </el-main>
</template>

<script lang="ts" setup>
// import { UserDetail } from '@/common/types/console/user';
import { TreeItem } from '@/common/types/console/group'
import deleteUsers from '@/components/user/delete.vue'
// import Group from '@/components/group/group-tree.vue'
import TreeList from '@/components/group/tree-list.vue'
import UserManage from '@/components/group/user-manage.vue'
import { computed, ref, watch } from 'vue';

const filterDetail = ref<any>({})
// const filterDepart = ref('')
const filterDepartName = ref('')
const activeGroup = ref<TreeItem>({})
const treeListRef = ref<InstanceType<typeof TreeList> | null>(null)
const treeData = ref<Array<TreeItem>>([])
// const currentPartDetail = ref<TreeItem>({})

const showUserManage = computed(() => {
  const treeDataLength = (treeData.value[0]?.children || []).length;
  return treeData.value?.length > 0 && treeDataLength > 0
})

const handleRefresh = () => {
  treeListRef.value?.getGroupMembers()
}

// 创建用户组
const handleCreateGroup = () => {
  console.log('handleCreateDepart')
  treeListRef.value?.addGroup()
}


const handleClearFilters = () => {
  if ((filterDetail.value).hasOwnProperty[filterDepartName.value]) {
    delete filterDetail.value[filterDepartName.value]
  }
  filterDepartName.value = ''
}

const handleSearchIconClick = (val: any) => {
  filterDepartName.value = filterDepartName.value
}

const handleFilterChange = (filter: any) => {
  console.log('handleFilterChange', filter)
  let filterAll =  { ...filterDetail.value, ...filter }

  for (const key in filterAll) {
    if (!filterAll[key] || filterAll[key].length === 0) {
      delete filterAll[key]
    }
  }
  // filterDetail.value = filterAll
}

const getTreeListComplete = (list: Array<TreeItem>) => {
  // console.log('getTreeListComplete', list)
  treeData.value = list
}

const handleGroupActive = (val: any) => {
  console.log('handleGroupActive-收到-点击了节点', val)
  activeGroup.value = {
    name: val.label,
    ...val
  }
}

</script>

<style lang="less" scoped>
.main-depart {
  display: flex;
  flex-direction: column;
  overflow: hidden !important;
  .depart-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }
  .depart-left {
    display: flex;
    flex-direction: column;
    padding: 16px;
    width: 328px;
    background-color: @bg-color-3;
    border-radius: @border-radius-3;
    overflow: auto;
    .depart-title {
      font-size: @text-1;
      display: flex;
      height: 22px;
      line-height: 22px;
      flex-direction: row;
      justify-content: space-between;
      .iconfont {
        cursor: pointer;
      }
    }
    .depart-filter {
      margin: 10px 0;
      height: 32px;
    }
    .depart-tree {
      flex: 1;
      overflow: auto;
    }
  }
  .depart-right {
    flex: 1;
    padding: 0 0 20px 24px;
    .depart-nodata {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .nodata-container {
        height: 338px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .nodata-title {
        line-height: 36px;
        font-size: @text-6;
        margin-bottom: 8px;
      }
      .nodata-desc {
        color: @text-color-5;
      }
      img {
        margin-bottom: 12px;
      }
      div {
        font-size: @text-0;
      }
    }
  }
}
</style>
