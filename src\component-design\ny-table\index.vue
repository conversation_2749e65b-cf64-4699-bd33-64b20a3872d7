<template>
  <div style="max-height: calc(100% - 44px)">
    <el-table
      class="ny-table"
      v-bind="restProps"
      :data="dataSource"
      style="
        width: 100%;
        max-height: calc(100% - 44px);
        --el-table-header-bg-color: #f7f8fa;
        --el-border-color-lighter: #e2e8f0;
      "
    >
      <el-table-column
        v-for="column in columns"
        v-bind="column"
        :key="column.prop"
        :type="column.type || undefined"
      >
        <template #default="scope" v-if="!column.type">
          <slot v-if="$slots[column.prop]" :name="column.prop" v-bind="scope"> </slot>
          <template v-else>{{ scope.row[column.prop] }}</template>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt-4 flex justify-end" v-if="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page="paginationRef.currentPage"
        :page-size="paginationRef.pageSize"
        :total="totalItems"
        layout="total, prev, pager, next, sizes"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElTable, ElTableColumn, ElPagination } from 'element-plus'
import { nyTableProps } from './type'

// 使用nyTableProps定义组件属性
const props = defineProps(nyTableProps)
const restProps = computed(() => {
  const { columns, defaultpagination, pagination, ...otherProps } = props
  return otherProps
})
// 获取columns属性
const columns = computed(() => props.columns || [])

// 设置默认值
const defaultPaginationValue = {
  currentPage: 1,
  pageSize: 10
}
// 获取属性，使用默认值
const { pagination = true, defaultPagination = defaultPaginationValue } = props
const totalItems = ref(0)
const paginationRef = ref(
  Object.assign({}, defaultPagination || defaultPaginationValue)
)
watch(
  () => props.data,
  (newVal) => {
    totalItems.value = newVal.length
  }
)

const dataSource = computed(() => {
  const { currentPage, pageSize } = paginationRef.value
  const start = (currentPage - 1) * pageSize
  const end = start + pageSize
  return props.data.slice(start, end)
})
const handleCurrentChange = (page: number) => {
  paginationRef.value.currentPage = page
}
const handleSizeChange = (pageSize: number) => {
  paginationRef.value.pageSize = pageSize
}
</script>

<style lang="less">
.ny-table {
  &.td-boder0 {
    .el-table__inner-wrapper {
      &:before {
        background: transparent !important;
      }
    }

    td {
      border-bottom: none !important;
    }
  }
}

div.el-pagination .el-select {
  width: 100px;
}
</style>
