
const checkIn = () => {
    console.log('checkIn')
}
const checkOut = () => {
    console.log('checkOut')
}
const query = () => {
    console.log('query')
}


const rightMenusConfig = [
    {
        name: '检入',
        icon: 'el-icon-menu',
        action: 'checkIn',
    },
    {
        name: '检出',
        icon: 'el-icon-menu',
        action: 'checkOut',
    },
    {
        name: '取消检出',
        icon: 'el-icon-menu',
        action: 'cancelCheckOut',
    },
    {
        name: '反查',
        icon: 'el-icon-menu',
        action: 'counterCheck',
        children: [
            {
                name: '查询',
                icon: 'el-icon-menu',
                action: 'query',
            },
        ]
    },
    {
        name: '获取最新',
        icon: 'el-icon-menu',
        action: 'getLatest',
    },
    {
        name: '获取历史',
        icon: 'el-icon-menu',
        action: 'getHistory',
        children: [
            {
                name: '上个版本',
                icon: 'el-icon-menu',
                action: 'previousVersion',
            },
            {
                name: '原始版本',
                icon: 'el-icon-menu',
                action: 'originalVersion',
            },
        ]
    },
    {
        name: '从CDP中引用装配图',
        icon: 'el-icon-menu',
        action: 'referencingAssembly',
    },
    {
        name: '生成轻量化',
        icon: 'el-icon-menu',
        action: 'generateModel',   
    },
    {
        name: '从云端更新文件属性',
        icon: 'el-icon-menu',
        action: 'updateFileProperties',
    },
    {
        name: '更新状态',
        icon: 'el-icon-menu',
        action: 'updateStatus',
        children: [
            {
                name: '提交审签',
                icon: 'el-icon-menu',
                action: 'submitReview',
            },
            {
                name: '归档',
                icon: 'el-icon-menu',
                action: 'archive',
            },
            {
                name: '废弃',
                icon: 'el-icon-menu',
                action: 'discard',
                disabled: true,
            }
        ]
    },
    {
        name: '取消参照特定/参照特定',
        icon: 'el-icon-menu',
        action: 'cancelReference',
    },
    {
        name: '修订',
        icon: 'el-icon-menu',
        action: 'revision',
    },
    {
        name: '更改所有权',
        icon: 'el-icon-menu',
        action: 'changeOwnership',
    },
    {
        name: '分享',
        icon: 'el-icon-menu',
        action: 'share',
    }
]

export default rightMenusConfig