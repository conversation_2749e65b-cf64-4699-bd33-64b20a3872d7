

const version = '202512'
const businessobjectUrl = `/cdp/${version}/designCollaborationSpaces`
const accountUrl = `/cdp/${version}/users`
const plt0ProjectSpaceUrl = `/cdp/${version}/plt0ProjectSpace`
export default {
  // 权限校验
  operationsAccess: `${accountUrl}/me`,
  // 创建设计协作空间
  businessobjectCreate: businessobjectUrl,
  // 修改协作空间|删除协作空间|获取协作空间详情
  businessobjectId: (designCollaborationSpaceId: any | string) => `${businessobjectUrl}/${designCollaborationSpaceId}`,
  // 获取设计协作空间列表
  businessobjectList: `${businessobjectUrl}/actions/list`,
  // 创建项目协作区
  plt0ProjectCreate: plt0ProjectSpaceUrl,
  plt0Project: (projectSpaceId: any | number) =>
    `${plt0ProjectSpaceUrl}/${projectSpaceId}`,
  // 修改项目协作区|删除项目协作区|获取项目协作区详情
  plt0ProjectId: (projectSpaceId: any | number) =>
    `${plt0ProjectSpaceUrl}/${projectSpaceId}`,
  // 获取项目协作区列表
  plt0ProjectList: `${plt0ProjectSpaceUrl}/actions/list`,
      // 获取bom树
  getDesignArtifact: (designCollaborationSpaceId: string) => `/cdp/202512/designCollaborationSpaces/${designCollaborationSpaceId}/designArtifact`,
  getActionsList: (className: string) => `/cdp/202512/${className}/actions/list`,
  getBomTree: (model: string, ncid: string) => `/cdp/202512/${model}/${ncid}/actions/recursiveGet`,

}