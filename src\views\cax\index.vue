
<template>
  <div class="cax-container page-container">
    <el-main class="main-container">
      <div class="cax">
        <div class="cax-menu">
          <cax-menu></cax-menu>
        </div>
        <div class="cax-content">
          <router-view></router-view>
        </div>
      </div>
    </el-main>
  </div>


  </template>
  
  <script lang="ts" setup>
  
  import { onMounted, ref, computed } from 'vue'
  
  import caxMenu from './cax-menu.vue';
  
  import { socket, loginData } from './websocket/sockets';
  import { ElMessage } from 'element-plus'
  import { useRoute } from 'vue-router'
  import storage from '@/utils/storage';

  const route = useRoute()
  const isCax = computed(() => {
  return route.path.includes('/cax')
  })
  // 监听登录后
  socket.on('AC00004', (data: any) => {
    console.log('登录成功', data);
    if(data.body.AC00004[0].result === true){
      console.log('登录成功')
    }else{
      ElMessage({
        message: '登录agent失败',
        type: 'error',
      })
    }
  })
  const userId = storage.get('agentUserId')
  const socketURL = `ws://127.0.0.1:9000?consoleID=${userId}`;
  // const socketURL = `ws://**********:9000?consoleID=${userId}`;
  onMounted(() => {
    socket.connect(socketURL)
    let loginDaTa = loginData()
    socket.send(loginDaTa)
  })
  



  
  </script>
  
  <style scoped lang="less">
  .main-container{
    padding: 0 0;
  }
  .cax{
    position: relative;
    display: flex;
    height: 100vh;
    .cax-menu{
      position: relative;
      width: 80px;
      // min-width: 80px;
      padding: 22px 0;
      height: 100%;
      border-right: 1px solid rgba(0, 0, 0, 0.1);;
    }
    .cax-content{
      width: calc(100% - 80px);
    }
  }
  
  </style>
  