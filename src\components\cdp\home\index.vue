<template>
  <router-view v-if="specialLayout.includes(getRouteName())" />
  <!-- 控制台 -->
  <main v-else class="main-container common-layout">
    <el-container class="content-layout">
      <el-header class="header-container">
        <Header />
      </el-header>
      <el-container class="content-container">
        <el-aside width="240px">
          <Nav />
        </el-aside>
        <router-view />
      </el-container>
    </el-container>
  </main>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import Header from '@/components/cdp/header.vue'
import Nav from '@/components/nav.vue'

const route = useRoute();

const specialLayout = ['login', 'vdi']

const getRouteName = () => {
  return route.name?.toString().toLowerCase()??'';
}

console.log('START::', new Date())
</script>

<style scoped lang="less">

.main-container {
  width: 100%;
  min-width: 1024px;
  height: 100%;
  background-color: @bg-color-4;
  display: flex;
  justify-content: center;
  // align-items: center;
  .content-layout {
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
    border-radius: 10px;
    .content-container {
      overflow: hidden;
    }
  }
}
</style>
