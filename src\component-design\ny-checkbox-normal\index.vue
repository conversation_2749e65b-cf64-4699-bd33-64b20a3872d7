<template>
  <el-checkbox-group class="ny-checkbox-normal" @change="handleChange" v-bind="otherProps" v-model="internalValue">
    <template v-for="item in items">
      <el-checkbox v-bind="item" />
    </template>
  </el-checkbox-group>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import NyCheckboxNormalProps from '.'

const props = defineProps<NyCheckboxNormalProps>()

const { items,checkboxItemProps ,...otherProps } = props;
console.log(items)
const emit = defineEmits(['update:modelValue', 'change:modelValue'])
const internalValue = ref(props.modelValue)
const handleChange = (value: any) => {
  internalValue.value = value
  emit('update:modelValue', value)
  emit('change:modelValue', value)
}
watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue
  }
)
</script>

<style lang="less" >
.ny-checkbox-normal {
 
}
</style>
