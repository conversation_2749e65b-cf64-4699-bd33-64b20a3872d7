/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-22 09:51:04
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-11 09:49:02
 * @FilePath: /neue-cloud-vdi/src/common/types/common.ts
 */
import { UploadRawFile } from 'element-plus/es/components/upload/src/upload'

export interface SelectItem {
  key: string
  value: string
  icon?: string
  class?: string
}

export interface ExtendedUploadRawFile extends UploadRawFile {
  suffix?: string
}

export interface EnvMap {
  [key: string]: string
}


interface SortParams {
  sortBy?: string
  sortOrder?: string
}

export interface PageParams {
  limit?: number
  page?: number | string
  sorts?: Array<SortParams>
}


export interface CommonResponse<T> {
  code: number
  message: string
  data: T
}

export interface ConditionStatement {
  field: string
  value: string[]
  operator: string
}

export interface Condition {
  logic: "AND" | "OR"
  ignoreCase: boolean
  statements: ConditionStatement[]
}
