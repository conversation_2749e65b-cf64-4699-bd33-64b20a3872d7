<template>
  <el-container class="bg-[#F7F8FA] min-h-[600px]">
    <el-aside width="220px">
      <div class="text-[16px] py-[20px] ml-[28px] font-bold text-[#0F172A]">
        空间设置
      </div>
      <el-menu
        :default-active="activeMenu"
        class="mx-[20px]"
        style="--el-menu-base-level-padding: 38px; --el-menu-text-color: #06090f"
        @select="handleMenuSelect"
      >
        <el-menu-item :index="item.key" :key="item.key" v-for="item in menuList">
          <span>{{ item.title }}</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <div class="flex-1">
      <basic-info
        @onClose="handleClose"
        :ncid="ncid"
        v-if="activeMenu === 'basic-info'"
      ></basic-info>
      <member-management
        @onClose="handleClose"
        :ncid="ncid"
        v-else-if="activeMenu === 'member-management'"
      ></member-management>
    </div>
  </el-container>
</template>

<script lang="ts" setup>
import basicInfo from './basic-info.vue'
import memberManagement from './member-management.vue'

const menuList = [
  {
    key: 'basic-info',
    path: '/space-setting/basic-info',
    title: '基本信息'
  },
  {
    key: 'member-management',
    path: '/space-setting/member-management',
    title: '成员管理'
  }
]
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// 用于高亮当前菜单项和控制内容显示
const route = useRoute()
const activeMenu = ref('basic-info') // 默认选中基本信息

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  activeMenu.value = key
}
const emit = defineEmits(['onClose'])
let handleClose = () => {
  emit('onClose')
}
// 获取URL参数ncid
const ncid = ref('')
onMounted(() => {
  // 从路由查询参数中获取ncid
  ncid.value = route.query.ncid as string
  // 根据路由路径设置初始选中的菜单
  const path = route.path
  const menuItem = menuList.find((item) => path.includes(item.key))
  if (menuItem) {
    activeMenu.value = menuItem.key
  }
})
</script>

<style scoped lang="less">
/deep/ .el-menu-item.is-active {
  background: #dee8ff;
  font-weight: 500;
}
</style>
