<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-11-14 10:01:35
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-06 16:08:01
 * @FilePath: /neue-cloud-vdi/src/components/common/dialog-msg.vue
-->
<template>
  <el-dialog
    id="deployMsg"
    v-model="props.visible"
    destroy-on-close
    :close-on-click-modal="false"
    title="删除"
    :style="{
      width: '520px',
      maxHeight: '200px'
    }"
    class="dialog-msg"
    :before-close="handleClose"
  >
    <template #default>
      <div class="dialog-content">
        <div class="dialog-title">
          <div class="dialog-title-text title-danger">
            <i class="iconfont icon-exclamation-circle-fill"></i>
            <span>请删除此用户组下的成员或子用户组后，再进行删除操作</span>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button id="closeBtn3" type="primary" @click="handleClose()">我知道了</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])


const handleClose = () => {
  emit('update:visible', false)
}

</script>

<style lang="less">
.dialog-msg {
  .dialog-title-text {
    display: flex;
    align-items: center;
    .title-danger {
      color: @danger-6;
    }
  }
  .icon-exclamation-circle-fill {
    color: @warning-5;
    font-size: @text-5 !important;
  }

  .dialog-content {
    color: @text-color-2;
    .dialog-title {
      text-align: left;
    }
    .dialog-title-text {
      line-height: 24px;
      font-size: @text-1;
      i {
        margin-right: 12px;
      }
    }
    .dialog-title-desc {
      padding-left: 32px;
      line-height: 18px;
      font-size: @text-0;
      color: @text-color-4;
    }
  }
}
</style>
