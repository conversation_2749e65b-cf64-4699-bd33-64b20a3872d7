/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-09 17:23:45
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-10 16:09:34
 * @FilePath: /neue-cloud-vdi/src/utils/index.ts
 */

import { ElMessage } from 'element-plus'
import axios from 'axios'
import { envMap } from '@/common/filters/common'
import { iconMap } from '@/common/filters/file'
import { roleMap } from '@/common/filters/user'
import { isPlainObject, uniqBy } from 'lodash-es'

/**
 * 格式化文件大小
 * @param size 文件大小
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (size: number) => {
  console.log(size)
  if (!size || size < 0) return '0 B'
  const kb = 1024
  const mb = kb * 1024
  const gb = mb * 1024
  if (size < kb) return `${size} B`
  if (size < mb) return `${(size / kb).toFixed(2)} KB`
  if (size < gb) return `${(size / mb).toFixed(2)} MB`
  return `${(size / gb).toFixed(2)} GB`
}
/**
 * 文件下载
 * @param url 文件路径
 */
export const downloadFile = (file: any) => {
  console.log('downloadFile', file)
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = file.url
  link.setAttribute('download', file.fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
/**
 * 通过二进制文件流方式创建url并下载
 * @param blobDetail 文件流信息
 */
export const downloadFileByBlob = (blobDetail: any) => {
  const url = URL.createObjectURL(blobDetail)
  downloadFile({ url, fileName: blobDetail.name })
}

export const getFileType = (fileName: string) => {
  if (!fileName.length) {
    return ''
  }
  const arr = fileName.split('.')
  return arr[arr.length - 1]
}

export const getFileIconUrl = (fileTypeStr: string) => {
  const fileType = getFileType(fileTypeStr)
  console.log('fileType', fileType)
  return `@/assets/images/console/space/${iconMap[fileType]}`
}
// 复制到剪贴板
export const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    ElMessage.success('复制成功')
  }
}

// 格式化选项为表格筛选
export const tableFiltersFormat = (data: any) => {
  return data.map((item: any) => {
    return { text: item.value, value: item.key }
  })
}

export const convertToObjectArray = (jsonObject: any) => {
  const resultArray: { key: string; value: any }[] = []
  const keys = Object.keys(jsonObject)

  keys.forEach((key) => {
    resultArray.push({
      key,
      value: jsonObject[key]
    })
  })

  return resultArray
}

export const envFormat = (env: any) => {
  const row = convertToObjectArray(env)
  return row.map((item) => `${envMap[item.key] || ''}:${item.value}`).join(' | ')
}

export const formatInstanceList = (data: any): { total: Number; list: any[] } => {
  const list = JSON.parse(data || '[]')
  return { total: list.length, list }
}

// 获取指定部门下的部门+成员列表【误删，后面可能会用】
const getChildren = (groupChildren: any, accountChildren: any, children = 'all') => {
  if (children === 'group') {
    return groupChildren || []
  }
  if (children === 'account') {
    return accountChildren || []
  }
  if (!groupChildren) {
    return accountChildren || []
  }
  if (!accountChildren) {
    return groupChildren || []
  }
  return accountChildren.concat(groupChildren)
}

const getTreeNodeDisabled = (item: any, type: string, disabledIds: Array<Number>) => {
  if (type === 'user') {
    return false
  }
  if (type === 'all') {
    // 全部树，用户不可选择
    return !!item.uid
  }
  if (type === 'group') {
    // 用户组编辑，不可选择自己
    return disabledIds.includes(item.id)
  }
  return false
}

// 格式化数据：数据转树形格式
// ;(data = []), (parentId = 0), (isRoot = false), (children = 'all')
export const formatGroupData = ({
  listData = [],
  parentId = 0,
  isRoot = false,
  children = 'all',
  disabledIds = []
}: any = {}) => {
  // console.log('formatGroupData', { listData, parentId, isRoot, children })
  const list = listData.map((item: any) => ({
    label: item.name || item.user,
    disabled: getTreeNodeDisabled(item, children, disabledIds),
    value: item.groupId,
    id: item.groupId,
    isRoot,
    parentId,
    leafType: item.email && item.password ? 'user' : 'group',
    children: formatGroupData({
      listData: item.groupChildren || [], // getChildren(item.groupChildren, item.accountChildren, children),
      parentId: item.groupId,
      isRoot: false,
      children,
      disabledIds
    })
  }))
  return list
}
// TODO sprint5 之后可能用不到了
export const getRoleName = (roleKey: string) => {
  return roleMap[roleKey] || '未知'
}

// 控制并发请求数量
export const concurrentRequest = (requests: any, max: number) => {
  return new Promise((resolve) => {
    if (requests.length === 0) {
      return resolve([])
    }
    const results: any[] = []
    let index: number = 0 // 下一个请求的下标
    let count: number = 0 // 当前请求完成数量

    // 发送请求
    async function sendRequest() {
      if (index >= requests.length) {
        return
      }
      const i = index
      const { url, formData, config } = requests[index]
      index += 1
      try {
        const res = await axios.post(url, formData, config)
        results[i] = res
      } catch (err) {
        results[i] = err
      } finally {
        count += 1
        if (count === requests.length) {
          resolve(results)
        } else {
          sendRequest()
        }
      }
    }
    const times = Math.min(max, requests.length)
    for (let i = 0; i < times; i++) {
      sendRequest()
    }
  })
}

const isTrulyEmpty = (val: any): boolean => {
  if (val === '' || val === null || val === undefined) return true
  if (Array.isArray(val) && val.length === 0) return true
  if (isPlainObject(val)) return isObjectValuesEmpty(val)
  return false
}

export const isObjectValuesEmpty = (obj: Record<string, any>): boolean => {
  return Object.values(obj).every((value) => isTrulyEmpty(value))
}

/**
 * 根据 ncid 去重并根据 ncid 和 parentId 构建树形结构
 * @param {Array} data - 原始数据，包含 ncid 和 parentId 字段
 * @param {Object} options - 配置选项
 * @param {string} options.idField - ID 字段名，默认为 'ncid'
 * @param {string} options.parentIdField - 父ID 字段名，默认为 'parentId'
 * @param {string} options.childrenField - 子节点字段名，默认为 'children'
 * @param {boolean} options.addHasChildren - 是否添加 hasChildren 字段，默认为 true
 * @returns {Array} - 构建的树形结构数据
 *
 */
export function buildUniqueTree(
  data: any[],
  options: {
    idField?: string
    parentIdField?: string
    childrenField?: string
    addHasChildren?: boolean
  } = {}
): any[] {
  // 参数验证
  if (!Array.isArray(data)) {
    console.warn('buildUniqueTree: data 参数必须是数组')
    return []
  }

  if (data.length === 0) {
    return []
  }

  // 默认配置
  const {
    idField = 'ncid',
    parentIdField = 'parentId',
    childrenField = 'children',
    addHasChildren = true
  } = options

  try {
    // 1. 根据 ID 字段去重
    const uniqueData = uniqBy(data, idField)
    console.log(
      `buildUniqueTree: 去重前 ${data.length} 条，去重后 ${uniqueData.length} 条`
    )

    // 2. 根据 ID 和 parentId 构建树形结构
    function buildTree(data: any[], parentId: string | null = null): any[] {
      return data
        .filter((item) => {
          // 处理根节点的情况
          if (parentId === null) {
            return (
              !item[parentIdField] ||
              item[parentIdField] === null ||
              item[parentIdField] === '' ||
              item[parentIdField] === undefined
            )
          }
          return item[parentIdField] === parentId
        })
        .map((item) => {
          const children = buildTree(data, item[idField])
          const result = { ...item }

          if (children.length > 0) {
            result[childrenField] = children
          }

          if (addHasChildren) {
            // 如果原数据已经有 hasChildren 属性，优先使用原值
            // 这对于动态加载的树形数据很重要
            if (item.hasChildren !== undefined) {
              result.hasChildren = item.hasChildren
            } else {
              result.hasChildren = children.length > 0
            }
          }

          return result
        })
    }

    // 返回树形结构
    const result = buildTree(uniqueData)
    console.log(`buildUniqueTree: 构建完成，共 ${result.length} 个根节点`)
    return result
  } catch (error) {
    console.error('buildUniqueTree 构建树形结构时出错:', error)
    return []
  }
}

export default buildUniqueTree

interface Statement {
  logic: string
  ignoreCase: boolean
  statements: Statement[]
  field: string
  value: any[]
  operator: string
}

interface FilterData {
  logic: string
  ignoreCase: boolean
  statements: Statement[]
}

export const transformData = (
  data: Record<string, any>,
  operatorKey: Record<string, string>
): FilterData => {
  const result: FilterData = {
    logic: 'AND',
    ignoreCase: true,
    statements: []
  }
  console.log(data, operatorKey)
  for (const [field, value] of Object.entries(data)) {
    if (!!value) {
      result.statements.push({
        logic: 'AND',
        ignoreCase: true,
        statements: [],
        field,
        value: value === '' ? [] : [value], // 为空的字段用空数组
        operator: operatorKey[field] || '='
      })
    }
  }

  return result
}
