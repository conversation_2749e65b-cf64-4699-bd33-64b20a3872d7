<template>
  <div class="ny-page-title">
    <div class="title">
      <slot>{{ title }}</slot>
    </div>
    <slot name="extra"></slot>
  </div>
</template>

<script lang="ts" setup>
import NyPageTitleProps from '.';
const props = defineProps<NyPageTitleProps>()
</script>

<style lang="less" scoped>
.ny-page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    text-align: left;
    color: #0F172A;
    font-size: 18px;
    font-weight: 600;
  }

}
</style>
