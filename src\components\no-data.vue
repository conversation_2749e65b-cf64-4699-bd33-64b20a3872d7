<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-16 16:47:22
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-08-19 12:05:52
 * @FilePath: /neue-cloud-vdi/src/components/no-data.vue
-->
<template>
  <div class="nodata-container">
    <div class="nodata-main">
      <div class="nodata-bg"></div>
      <div class="nodata-content">
        <div class="nodata-title">{{ props.title }}</div>
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  title: String
})

</script>
<style scoped lang="less">
.nodata-container {
  display: flex;
  justify-content: center;
  align-items: center;
  .nodata-main {
    display: flex;
    // align-items: space-between;
    width: 280px;
    height: 98px;
  }
  .nodata-bg {
    width: 124px;
    height: 96px;
    background-image: url('@/assets/images/no-data.svg');
    background-position: 0 0;
    background-size: cover
  }
  .nodata-content {
    text-align: left;
    margin-left: 10px;
  }
  .nodata-title {
    padding-top: 22px;
    line-height: 16px;
    font-size: @text-4;
    color: @text-color-2;
  }
}
</style>
