<template>
  <el-container class="bg-[#fff] flex h-[100%]">
    <el-header :class="classHeader" class="flex justify-between items-center py-[20px]"
      style="--el-header-padding: 24px">
      <span class="font-bold text-[#0F172A] text-[16px] leading-[24px] ">{{ title }}</span>
      <el-icon v-if="closable" size="20" @click="handleClose" class="cursor-pointer">
        <Close />
      </el-icon>
    </el-header>

    <el-main v-if="hasSlotContent" class="flex-1" :class="classContainer" style="--el-main-padding: 24px">
      <slot></slot>
    </el-main>
  </el-container>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, useSlots, computed } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const slots = useSlots()

// 检查默认插槽是否有内容
const hasSlotContent = computed(() => {
  return !!slots.default && slots.default().length > 0
})
const props = withDefaults(
  defineProps<{
    title: string
    closable?: boolean
    classContainer?: string
    classHeader?: string
    onClose?: () => boolean | Promise<boolean | void>
  }>(),
  {
    closable: true
  }
)

const emit = defineEmits<{
  (e: 'close'): void
}>()

const goBack = () => {
  router.back()
}
const handleClose = async () => {
  if (props.onClose) {
    try {
      const result = await props.onClose()
      if (result === false) {
        console.log('关闭被取消')
        return
      }
    } catch (err) {
      console.error('关闭失败:', err)
      return
    }
  }
  goBack()
  // ✅ 关闭逻辑继续执行
  console.log('已关闭')
}

</script>

<style scoped lang="less"></style>
