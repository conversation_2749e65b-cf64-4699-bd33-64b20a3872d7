#!/bin/bash

# Get the directory where the script is located
SCRIPT_DIR="$(dirname "$(realpath "$0")")"
echo $SCRIPT_DIR

# Path to the file in the same directory as the script
TEMPLATE_FILE="$SCRIPT_DIR/service-template.yaml"
DEPLOYMENT_FILE="$SCRIPT_DIR/generated-service.yaml"

# Default values
DEFAULT_NAME="pig-register"
DEFAULT_IMAGE="registry.cn-hangzhou.aliyuncs.com/neue_galaxy/cicd_pig"
DEFAULT_TAG="pig-register-0.1-cicd-deploy.52"
DEFAULT_REPLICAS="1"
DEFAULT_PORT="8848"
DEFAULT_REQUESTS_MEMORY="256Mi"
DEFAULT_REQUESTS_CPU="500m"
DEFAULT_LIMITS_MEMORY="1Gi"
DEFAULT_LIMITS_CPU="1000m"
DEFAULT_IMAGE_PULL_SECRETS="repositorykey"
DEFAULT_SERVICE_TYPE="NodePort"
DEFAULT_LIVENESS_PATH="/ping"
DEFAULT_READINESS_PATH="/ping"
DEFAULT_NODE_SELECTOR="kubernetes-node1"
DEFAULT_PROFILE_OPTS="-Dspring.profiles.active=local"
DEFAULT_SERVER_DOMAIN="http://storage-internal.cloud.neuetech.cn"
DEFAULT_VOLUME_DATA="/mnt/test"

# Parse command-line arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    --profile-opts=*) PROFILE_OPTS="${1#*=}" ;;  # Extracts value after '='
    --profile-opts) PROFILE_OPTS="$2"; shift ;;  # Handles `--profile-opts "value"`
    --name) NAME="$2"; shift ;;
    --image) IMAGE="$2"; shift ;;
    --tag) TAG="$2"; shift ;;
    --replicas) REPLICAS="$2"; shift ;;
    --port) PORT="$2"; shift ;;
    --requests-memory) REQUESTS_MEMORY="$2"; shift ;;
    --requests-cpu) REQUESTS_CPU="$2"; shift ;;
    --limits-memory) LIMITS_MEMORY="$2"; shift ;;
    --limits-cpu) LIMITS_CPU="$2"; shift ;;
    --image-pull-secrets) IMAGE_PULL_SECRETS="$2"; shift ;;
    --service-type) SERVICE_TYPE="$2"; shift ;;
    --liveness-path) LIVENESS_PATH="$2"; shift ;;
    --readiness-path) READINESS_PATH="$2"; shift ;;
    --node-selector) NODE_SELECTOR="$2"; shift ;;
    --profile-opts) PROFILE_OPTS="$2"; shift ;;
    --server-domain) SERVER_DOMAIN="$2"; shift ;;
    --volume-data) VOLUME_DATA="$2"; shift ;;
    *) echo "Unknown parameter passed: $1"; exit 1 ;;
  esac
  shift
done

# Set default values if not provided
NAME="${NAME:-$DEFAULT_NAME}"
IMAGE="${IMAGE:-$DEFAULT_IMAGE}"
TAG="${TAG:-$DEFAULT_TAG}"
REPLICAS="${REPLICAS:-$DEFAULT_REPLICAS}"
PORT="${PORT:-$DEFAULT_PORT}"
REQUESTS_MEMORY="${REQUESTS_MEMORY:-$DEFAULT_REQUESTS_MEMORY}"
REQUESTS_CPU="${REQUESTS_CPU:-$DEFAULT_REQUESTS_CPU}"
LIMITS_MEMORY="${LIMITS_MEMORY:-$DEFAULT_LIMITS_MEMORY}"
LIMITS_CPU="${LIMITS_CPU:-$DEFAULT_LIMITS_CPU}"
IMAGE_PULL_SECRETS="${IMAGE_PULL_SECRETS:-$DEFAULT_IMAGE_PULL_SECRETS}"
SERVICE_TYPE="${SERVICE_TYPE:-$DEFAULT_SERVICE_TYPE}"
LIVENESS_PATH="${LIVENESS_PATH:-$DEFAULT_LIVENESS_PATH}"
READINESS_PATH="${READINESS_PATH:-$DEFAULT_READINESS_PATH}"
NODE_SELECTOR="${NODE_SELECTOR:-$DEFAULT_NODE_SELECTOR}"
PROFILE_OPTS="${PROFILE_OPTS:-$DEFAULT_PROFILE_OPTS}"
SERVER_DOMAIN="${SERVER_DOMAIN:-$DEFAULT_SERVER_DOMAIN}"
VOLUME_DATA="${VOLUME_DATA:-$DEFAULT_VOLUME_DATA}"

# Display chosen configuration
echo "Generating service definition with the following configuration:"
echo "Name: $NAME"
echo "Image: $IMAGE"
echo "Tag: $TAG"
echo "Replicas: $REPLICAS"
echo "Port: $PORT"
echo "Requests Memory: $REQUESTS_MEMORY"
echo "Requests CPU: $REQUESTS_CPU"
echo "Limits Memory: $LIMITS_MEMORY"
echo "Limits CPU: $LIMITS_CPU"
echo "Image Pull Secrets: $IMAGE_PULL_SECRETS"
echo "Service Type: $SERVICE_TYPE"
echo "Liveness Path: $LIVENESS_PATH"
echo "Readiness Path: $READINESS_PATH"
echo "Node Selector: $NODE_SELECTOR"
echo "Profile Opts: $PROFILE_OPTS"
echo "Server Domain: $SERVER_DOMAIN"
echo "Volume Data: $VOLUME_DATA"

# Substitute placeholders in template using @ as the delimiter to avoid conflicts
sed -e "s@{{name}}@$NAME@g" \
    -e "s@{{image}}@$IMAGE@g" \
    -e "s@{{tag}}@$TAG@g" \
    -e "s@{{replicas}}@$REPLICAS@g" \
    -e "s@{{port}}@$PORT@g" \
    -e "s@{{requests.memory}}@$REQUESTS_MEMORY@g" \
    -e "s@{{requests.cpu}}@$REQUESTS_CPU@g" \
    -e "s@{{limits.memory}}@$LIMITS_MEMORY@g" \
    -e "s@{{limits.cpu}}@$LIMITS_CPU@g" \
    -e "s@{{imagePullSecrets}}@$IMAGE_PULL_SECRETS@g" \
    -e "s@{{serviceType}}@$SERVICE_TYPE@g" \
    -e "s@{{livenessPath}}@$LIVENESS_PATH@g" \
    -e "s@{{readinessPath}}@$READINESS_PATH@g" \
    -e "s@{{profileOpts}}@$PROFILE_OPTS@g" \
    -e "s@{{nodeSelector}}@$NODE_SELECTOR@g" \
    -e "s@{{serverDomain}}@$SERVER_DOMAIN@g" \
    -e "s@{{volumeData}}@$VOLUME_DATA@g" \
    $TEMPLATE_FILE > $DEPLOYMENT_FILE

echo "Service definition generated in generated-service.yaml"
