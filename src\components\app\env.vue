<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-28 17:43:52
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-08-29 14:32:01
 * @FilePath: /neue-cloud-vdi/src/components/app/env.vue
-->
<template>
  <el-radio-group v-model="props.selected" @change="handleChange">
    <el-radio label="windows">Windows</el-radio>
    <el-radio label="linux">Linux</el-radio>
  </el-radio-group>
</template>

<script setup>
import { ref } from 'vue'
import { ElRadioGroup, ElRadio } from 'element-plus'

const props = defineProps({
  selected: String
})

const emit = defineEmits(['update:selected'])

function handleChange(value) {
  emit('update:selected', value)
}
</script>
