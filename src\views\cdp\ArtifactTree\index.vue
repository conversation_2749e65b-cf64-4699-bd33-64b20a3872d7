<template>

  <div :class="['tree-panel', isExpand ? 'tree-panel-expand' : 'tree-panel-collapse']">
    <el-auto-resizer>
    <template #default="{ height, width }">
      <el-tree-v2
        id="tree"
        ref="treeRef"
        :props="treeProps"
        :expand-on-click-node="false"
        node-key="id"
        @node-click="nodeClick"
        :data="treeData"
        :height="height"
      >
        <template #default="{ node, data }">
          <div :class="['leaf']" :key="node.id">
            <icon-text
              :type="data.type"
              :content="data.label"
              :iconSize="'16px'"
              :textSize="'14px'"
            ></icon-text>
            <div v-show="data.id!=='root'" v-if="data.isHide" class="hide-button" @click="(e) => visibleSendSdk(e, data)"><i :class="['iconfont', 'icon-tree-eye-invisible']"></i></div>
            <div v-show="data.id!=='root'" v-else class="hide-button" @click="(e) => hideSendSdk(e, data)"><i :class="['iconfont', 'icon-tree-eye']"></i></div>
            <el-popover
              trigger="click"
              placement="right-start"
              popper-class="tree-menu"
              :show-arrow="false"
              :offset="12"
              :disabled="data.isHide"
              v-if="popoverObject.id !== data.id"
              @before-leave="hidePopover(data)"
              >
              <template #reference>
                <div class="menu-button" v-show="data.id!=='root'"><i class="iconfont icon-tree-more"></i></div>
              </template>

                <div class="neue-cax" @click="open(data)" v-if="isCax">
                  <img :src="neueCax" alt="">
                  <span>在诺源CAX中打开
                  </span>
                </div>
                <div class="view-des" @click="viewDes(data)">
                  <i class="iconfont icon-file"></i>
                  <span>查看详情</span>
                </div>
            </el-popover>
          </div>
        </template>
      </el-tree-v2>
    </template>
    </el-auto-resizer>
    <div class="tree-panel-aside" @click="isExpand = !isExpand">
    </div>
  </div>
  <open-progress
    v-model:checkVisible="progressVisible"
  />

  </template>
  
  <script lang="ts" setup>

import { nextTick, onMounted, ref, watch, toRefs, computed } from 'vue'
import { useRoute } from 'vue-router'
import iconText from './icon-text.vue'
import openProgress from './open-progress.vue'
import neueCax from '@/assets/images/neuecax.svg'
import type { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type'
import type Node from 'element-plus/es/components/tree/src/model/node'
import {sendOrderToSDK} from '@/utils/cdp-engine'

  interface Tree {
    id: string
    label: string
    type?: string
    isHide?: boolean
    children?: Tree[]
  }

const props = defineProps({
  bomData: {
    type: Object,
    default:{}
  },
})
const { bomData } = toRefs(props);

const treeData = ref<Tree[]>()
const isExpand = ref(true)
const treeRef = ref()
const popoverVisible = ref(true)
const popoverObject = ref<any>({
  id: ''
})

const hidePopover = (data:any) => {
popoverObject.value.id = data.id
  setTimeout(() => {
    popoverObject.value.id = ''
  }, 10)
}
const route = useRoute()
const isCax = computed(() => {
  return route.path.includes('/cax')
})


  // 类型枚举
const typeEnum: Record<string, string> = {
    NEUE_PRT: '零件',
    NEUE_ASM: '装配体',
    DRAWING: '图纸',
    Normal: '标准件',
    BOM: '物料清单',
};

  // 自定义节点类
  const customNodeClass = (data: TreeNodeData) =>{
    return data.isHide ? 'hide' : 'display'
  }
  
  const treeProps = {
    value: 'id',
    label: 'label',
    children: 'children',
    parent: 'parent',
    isLeaf: 'leaf',
    class: customNodeClass,
  }

  const progressVisible = ref(false)

// 隐藏该节点，及其所有子节点，并将修改的数据存储到updateData中
const hide = (data: Tree) => {

  data.isHide = true
  if (data.children) {
    data.children.forEach((item) => {
      hide(item)
    })
  }
  updataParent(data)
}

const hideSendSdk = (e: Event,data: any) => {
    e.stopPropagation()
    hide(data)
  // 发送隐藏指令到SDK
  if(data.idi3DLightModelId){
    sendOrderToSDK(data.idi3DLightModelId, 'hide')
  }
}
const visibleSendSdk = (e: Event,data: any) => {
   e.stopPropagation()
  visible(data)
  // 发送显示指令到SDK
  if(data.idi3DLightModelId){
    sendOrderToSDK(data.idi3DLightModelId, 'visible')
  }
}

// 显示该节点，及其所有子节点，并更新树形数据
const visible = (data: Tree) => {

  data.isHide = false
  if (data.children) {
    data.children.forEach((item) => {
      visible(item)
    })
  }
  updataParent(data)
}

// 更新父节点的isHide属性
const updataParent = (data: Tree) => {
  nextTick(() => {
    let node = treeRef.value.getNode(data.id)
    while (node) {
        const parent = node.parent
        if (parent && parent.data && parent.data.children) {
        // 递归判断父节点下的所有层级的子节点是否都隐藏
        let all = Judgment(parent.data)
        if(all) {
          parent.data.isHide = true
        }else {
          parent.data.isHide = false
        }   
      }
      node = node.parent
    }



  })
}


// 判断该节点下的所有子节点是否都隐藏
const Judgment = (data: Tree) => {
const allChildrenHidden = data.children?.every((item) => item.isHide)
if (allChildrenHidden) {
    data.children?.forEach((item) => {
      if (item.children) {
        // 递归判断子节点
        Judgment(item)
      }
    })
} else {
  return false
}
  return true
}



// 打开该节点在诺源CAX中
const open = (data: Tree) => {
    console.log('open', data)
    progressVisible.value = true
}
// 查看该节点详情
const viewDes = (data: Tree) => {
    console.log('view', data)
  }
// 节点点击事件
const emit = defineEmits(['nodeClick'])
const nodeClick = (data: any) => {
  console.log('data', data);
  // 如果是根节点，直接返回
  if(data.id === 'root') return
  emit('nodeClick', data)
  // sendOrderToSDK()
  if(data.idi3DLightModelId){
    sendOrderToSDK(data.idi3DLightModelId, 'highLight')
  }
}

  const initData =(data:any)=>{      
    const newData = data?.map((item: any) => {
      let leafName = ''
      let children = []
      if(item.target){
        leafName = item.target.partNo + ' ' + item.target.partName
        children = item.target.cadboms ? initData(item.target.cadboms) : []
      }else{
        children = item.cadboms ? initData(item.cadboms) : []
        leafName = item.partNo + ' ' + item.partName
      }
      return {
        id: item.ncid,
        label: leafName,
        type: item.target ? item.target.partType : item.partType,
        isHide: false,
        children: children,
        ...item.target? item.target : item,
      }
    })
    return newData
  }



  // 懒加载数据___问题太多，不想用
//   const loadNode = (node: Node, resolve: (data: Tree[]) => void) => {
//     if (node.level === 0) {
//       const firstLevelData = treeData.value.map((item) => {
//         return {
//           ...item,
//           hasChildren: !!item.children?.length,
//         }
//       })
//       // 根节点已加载
//       return resolve(firstLevelData)
//     }
//     if(node.level >= 1) {
//       // 加载剩余的节点,如果没有则加载空数组
//       setTimeout(() => {
//         const children = node.data.children
//       if (children) {
//         const childrenData = children.map((item) => {
//           return {
//             ...item,
//             hasChildren: !!item.children?.length,
//           }
//         })
//         // 节点已加载
//         return resolve(childrenData)
//       } else {
//         // 没有子节点
//         return resolve([])
//       }
//       }, 100)

//   }
// }
watch(bomData, (newVal) => {  
    if (newVal) {
    const rootData = {
      id: 'root',
      label: '设计数据',
      type: 'root',
      isHide: false,
      children: [],
    }         
      rootData.children = initData([newVal])
      treeData.value = [rootData]
    }
  }, { deep: true })


  onMounted(() => {    
    console.log(props.bomData);
    
  })



  </script>

<style lang="less">
.tree-menu{
  padding: 4px !important;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
  &>div{
    width: 185px;
    padding: 5px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    i{
      margin-right: 4px;
    }
    &:hover{
      background: @primary-2;
    }
  }
  .neue-cax{
    img{
      display: inline-block;
      margin-right: 4px;
    }
}
}

</style>


<style lang="less" scoped>
.tree-panel {
  width: 350px;
  padding: 12px 0 12px 12px;
  position: relative;
  height: 100%;
  border-width: 0px 1px 0px 0px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.1);

}
.tree-panel-expand {
  &:hover {
    .tree-panel-aside {
      background-image: url('@/assets/images/cax/tree-shouqi.svg');
    }
  }
}
.tree-panel-collapse {
  width: 0px;
  padding: 0;
    .tree-panel-aside {
      background-image: url('@/assets/images/cax/tree-zhankai.svg');
    }
}

.leaf {
  display: flex;
  position: relative;
  height: 24px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

}
.hide-button {
  display: none;
  position: absolute;
  right: 28px;
  height: 24px;
  line-height: 24px;
  cursor: pointer;
}
.menu-button {
  display: none;
  position: absolute;
  right: 8px;
  height: 24px;
  line-height: 24px;
  cursor: pointer;
}


:deep(.el-tree){
  .el-tree-node__content{
    height: 24px !important;
    margin-bottom: 10px !important;
    margin-right: 12px !important;
    border-radius: 4px !important;
    // 箭头
    cursor:default !important;
    &:hover{
      background: @primary-2 !important;
      .hide-button{
        display: block;
      }
      .menu-button{
        display: block;
      }
    }
  }
  .el-tree-node:focus>.el-tree-node__content {
    background-color: transparent;
}
.is-current>div:first-child,
.is-current>.el-tree-node__content .custom-node-content {
  .hide-button{
    display: block;
  }
  .menu-button{
    display: block;
  }
}

.hide{
    >.el-tree-node__content{
      opacity: 0.3;
      background-color: transparent !important;
      &:hover{
      opacity: 1;
      background: @primary-2 !important;
    }
    }
    .menu-button{
      // 禁用
      cursor: not-allowed;
    }
  }
  .display{
    >.el-tree-node__content{
      opacity: 1 ;
    }
    .menu-button{
      cursor: pointer;
    }
  }


}
:deep(.el-scrollbar__bar) {
  right: 0px !important;
}
:deep(.el-virtual-scrollbar) {
  right: 0px !important;
}

.tree-panel-aside {
  width: 12px;
  height: 100px;
  position: absolute;
  top: calc(50% - 50px);
  right: -12px;
  background: transparent;
  cursor: pointer;
}






  </style>