interface CdpToSDKMessage{
    type: string;  //"tree"丨 "toolbar" | "resize"
    payload: {
    nodeId?: string; //树形结构中的
    nodeOperator?: string; // 树形结构操作内容 // highLight | hide | visible 
    operation?: string; // 工具栏操作内容 
    resizeFlag?: boolean; // 是否需要重新渲染
}
}
const sendSyncMessageToSDK = (message: CdpToSDKMessage) => {
    console.log(message, 'cdpToSDKMessage---');
    const event = new CustomEvent("cdpTosDKMessage", { detail: message });
    window.dispatchEvent(event);
};

export const sendOrderToSDK = (nodeId: string, nodeOperator: string) => {
    sendSyncMessageToSDK({
        type: "tree",
        payload: {
            nodeId: nodeId, //树形结构中的nodeID
            nodeOperator: nodeOperator //树形结构操作内容
        }
    });
}

const getCdpToSDKMessage = () => {
    window.addEventListener("sdkToCdpMessage", 
        function (ev: Event){const customEv = ev as CustomEvent;
        const message = customEv.detail;
        return message;
    });
}