<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-12-16 10:28:19
 * @LastEditors: fangshijie <EMAIL>
 * @LastEditTime: 2024-12-25 17:04:54
 * @FilePath: /neue-cloud-vdi/src/components/common/dialog-warning.vue
-->
<template>
  <el-dialog
    id="common-dialog-warning"
    v-model="props.visible"
    :title="props.title"
    :width="props.width"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="common-dialog-warning"
  >
    <template #default>
      <div class="common-dialog-content">
        <div class="common-dialog-title">
          <div class="common-dialog-title-text">
            <i class="iconfont icon-exclamation-circle-fill"></i>
            <span>{{ props.warnText1 }}</span>
          </div>
          <div class="common-dialog-title-desc">{{ props.warnText2 }}</div>
        </div>

        <slot></slot>
      </div>
    </template>
    <template #footer>
      <span class="common-dialog-footer">
        <el-button id="cancelBtn" @click="handleCancel">{{ cancelText }}</el-button>
        <el-button id="sureBtn" type="primary" @click="handleOK">{{ okText }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '520px'
  },
  warnText1: {
    type: String,
    default: '确定要删除以下角色吗？'
  },
  warnText2: {
    type: String,
    default: '删除操作无法恢复，请谨慎操作。'
  },
  okText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  }
})


const emit = defineEmits(['update:visible', 'confirm'])
const handleClose = () => {
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('confirm', { type: 'cancel'})
  handleClose()
}
const handleOK = () => {
  // emit('update:visible', false)
  emit('confirm', { type: 'OK'})
}

console.log('props===》', props)

</script>
<style lang="less" scoped>
.common-dialog-warning {
  color: @text-color-2;
  .common-dialog-title {
    text-align: left;
  }
  .common-dialog-title-text {
    display: flex;
    line-height: 24px;
    font-size: @text-1;
    color: @text-color-2;
    font-weight: @font-weight-2;
    i {
      margin-right: 12px;
    }
  }
  .common-dialog-title-desc {
    padding-left: 32px;
    line-height: 18px;
    font-size: @text-0;
    color: @text-color-4;
  }
  .common-dialog-footer button:first-child {
    margin-right: 10px;
  }
}
</style>
