/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-23 14:19:56
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-26 14:45:22
 * @FilePath: /neue-cloud-vdi/src/utils/validate.ts
 */

/**
 * 校验邮箱
 * @param str email
 * @returns boolean
 */
export const validateEmail = (str: string) => {
  if (!str) return false
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(str)
}
/**
 * 校验手机号
 * @param str phone
 * @returns boolean
 */
export const validatePhone = (str: string) => {
  if (!str) return false
  return /^1[3-9]\d{9}$/.test(str)
}

/**
 * 校验用户名（用户名、邮箱不区分）
 * @param str name
 * @returns boolean
 */
export const validateName = (str: string) => {
  if (!str) return false
  return /^[a-zA-Z0-9]+$/
}

/**
 * 校验创建用户名
 * @param str name
 * @returns boolean
 */
export const validateUserName = (str: string) => {
  if (!str) return false
  return /^[\u4E00-\u9FA5a-zA-Z][\u4E00-\u9FA5a-zA-Z0-9_]*$/.test(str)
}
// 校验名称：必须字母或中文开头
export const validateNameStart = (str: string) => {
  if (!str) return false
  return /^[\u4e00-\u9fa5a-zA-Z]/.test(str)
}

export const validatePassword = (str: string) => {
  if (!str) return false
  return /^[a-zA-Z0-9@#$%^&+=_-]*$/.test(str)
}

export const validateRoleName = (str: string) => {
  if (!str) return false
  return /^[a-zA-Z\u4e00-\u9fa5][\w\u4e00-\u9fa5]{0,31}$/.test(str)
}
