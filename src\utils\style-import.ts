import { App } from 'vue'
import {
  ElIcon,
  ElLoading,
  ElCard,
  ElButton,
  ElForm,
  ElInput,
  ElSelect,
  ElDropdown,
  ElAvatar,
  ElBreadcrumb,
  ElDrawer,
  ElTable,
  ElMessage,
  ElDialog,
  ElUpload,
  ElTag,
  ElContainer,
  ElHeader,
  ElAside,
  ElMain,
  ElPagination,
  ElConfigProvider,
  ElMenu
} from 'element-plus'

/**
 * 按需导入 Element Plus 组件
 * Vite 插件 https://github.com/element-plus/vite-plugin-element-plus
 * @param app {App}
 */
export default function styleImport(app: App) {
  ;[
    ElButton,
    ElCard,
    ElLoading,
    ElIcon,
    ElForm,
    ElInput,
    ElSelect,
    ElDropdown,
    ElAvatar,
    ElBreadcrumb,
    ElDrawer,
    ElTable,
    ElMessage,
    ElDialog,
    ElUpload,
    ElTag,
    ElContainer,
    ElHeader,
    ElAside,
    ElMain,
    ElPagination,
    ElConfigProvider,
    ElMenu
  ].forEach((v) => {
    app.use(v)
  })
  return app
}
