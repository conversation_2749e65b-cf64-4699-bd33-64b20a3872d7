version: '1.0'
name: auto-release-platform-cd-pipeline
displayName: auto-release-platform-cd-pipeline
triggers:
  trigger: manual
  push:
    branches:
      prefix:
        - develop
        - master
variables:
  global:
    - PIPELINE_DATA_CENTER
    - PIPELINE_INFRA_NODE
    - PIPELINE_INFRA_REPO
    - PIPELINE_SERVER_DOMAIN
    - PIPELINE_SVC_ARTIFACT
    - PIPELINE_SVC_LIVENESS
    - PIPELINE_SVC_NAME
    - PIPELINE_SVC_PORT
    - PIPELINE_SVC_READINESS
    - PIPELINE_VOLUME_DATA
stages:
  - name: stage-1de73a5f
    displayName: Deploy
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: execute@docker
        name: execute_by_docker
        displayName: Deploy Artifacts
        certificate: ''
        image: registry.cn-hangzhou.aliyuncs.com/neue_galaxy/hub_tools_mirrors:cd_tools_v1
        command:
          - '# generate yml file variables'
          - COMMAND_FILE="./infra/k8s/templates/generate-service.sh"
          - '# generate yml file by env vars through storage service'
          - if [ "$PIPELINE_SVC_NAME" = "neue-platform-storage" ]; then
          - '    COMMAND_FILE="./infra/k8s/storagetemplates/generate-service.sh"'
          - fi
          - ''
          - COMMAND_PARM_IMAGE="${PIPELINE_INFRA_REPO}"
          - COMMAND_PARM_TAG="${PIPELINE_SVC_ARTIFACT}"
          - chmod +x $COMMAND_FILE
          - '# spring profile active yml file by env vars'
          - COMMAND_PROFILE_OPTS=""
          - if [ "$PIPELINE_DATA_CENTER" = "cn-neue-1" ]; then
          - '    COMMAND_PROFILE_OPTS="-Dspring.profiles.active=test -Dspring.cloud.nacos.discovery.server-addr=***********:8848"'
          - fi
          - ''
          - if [ "$PIPELINE_DATA_CENTER" = "cn-neue-2" ]; then
          - '   COMMAND_PROFILE_OPTS="-Dspring.profiles.active=prod -Dspring.cloud.nacos.discovery.server-addr=*************:8848 -Dspring.cloud.nacos.config.namespace=public -Dspring.cloud.nacos.discovery.username=nacos -Dspring.cloud.nacos.discovery.password=nacos"'
          - fi
          - ''
          - ls ./infra/k8s/templates/
          - ls ./infra/k8s/storagetemplates/
          - ''
          - '$COMMAND_FILE --profile-opts "${COMMAND_PROFILE_OPTS}" --name ${PIPELINE_SVC_NAME} --port ${PIPELINE_SVC_PORT} --image ${COMMAND_PARM_IMAGE} --tag ${COMMAND_PARM_TAG} --liveness-path ${PIPELINE_SVC_LIVENESS} --readiness-path ${PIPELINE_SVC_READINESS} --node-selector ${PIPELINE_INFRA_NODE} --volume-data ${PIPELINE_VOLUME_DATA} --server-domain ${PIPELINE_SERVER_DOMAIN} --replicas 1 '
          - ''
          - DEPLOYMENT_FILE="./infra/k8s/templates/generated-service.yaml"
          - '# generate yml file by env vars through storage service'
          - if [ "$PIPELINE_SVC_NAME" = "neue-platform-storage" ]; then
          - '    DEPLOYMENT_FILE="./infra/k8s/storagetemplates/generated-service.yaml"'
          - fi
          - ''
          - if [ -f "$DEPLOYMENT_FILE" ]; then
          - '  # cat DEPLOYMENT_FILE'
          - '  echo "File ''$DEPLOYMENT_FILE'' has been created."'
          - else
          - '  echo "File ''$DEPLOYMENT_FILE'' does not exist."'
          - '  exit 1'
          - fi
          - ''
          - if [ "$PIPELINE_DATA_CENTER" = "cn-neue-1" ]; then
          - '  # export KUBECONFIG=/root/neue-cloud-paas/infra/k8s/kubeconfig-dev'
          - '  echo "************* kubernetes-master" >> /etc/hosts'
          - '  echo "=====/etc/hosts====="'
          - '  cat /etc/hosts'
          - '  echo "=====/etc/hosts====="'
          - ''
          - '  echo "=====$DEPLOYMENT_FILE====="'
          - '  cat $DEPLOYMENT_FILE'
          - '  echo "=====$DEPLOYMENT_FILE====="'
          - ''
          - '  #ping kubernetes-master -c 5'
          - ''
          - '  #kubectl get node --kubeconfig  ./infra/k8s/kubeconfig-dev'
          - ''
          - '  kubectl config get-contexts --kubeconfig  ./infra/k8s/kubeconfig-dev  # 检查是否默认指向了错误的namespace'
          - ''
          - '  kubectl config set-context --current --namespace=default --kubeconfig  ./infra/k8s/kubeconfig-dev'
          - ''
          - '  kubectl apply -f  $DEPLOYMENT_FILE --kubeconfig  ./infra/k8s/kubeconfig-dev'
          - ''
          - '  echo "Deployed Data Center: cn-neue-1"'
          - fi
          - ''
          - if [ "$PIPELINE_DATA_CENTER" = "cn-neue-2" ]; then
          - '  # export KUBECONFIG=/root/neue-cloud-paas/infra/k8s/kubeconfig-prod'
          - '  echo "************* kubernetes-master" >> /etc/hosts'
          - '  '
          - '  echo "=====/etc/hosts====="'
          - '  cat /etc/hosts'
          - '  echo "=====/etc/hosts====="'
          - ''
          - '  echo "=====$DEPLOYMENT_FILE====="'
          - '  cat $DEPLOYMENT_FILE'
          - '  echo "=====$DEPLOYMENT_FILE====="'
          - ''
          - '  kubectl config get-contexts --kubeconfig  ./infra/k8s/kubeconfig-prod  # 检查是否默认指向了错误的namespace'
          - ''
          - '  kubectl config set-context --current --namespace=default --kubeconfig  ./infra/k8s/kubeconfig-prod'
          - ''
          - '  kubectl apply -f  $DEPLOYMENT_FILE --kubeconfig ./infra/k8s/kubeconfig-prod'
          - ''
          - '  echo "Deployed Data Center: cn-neue-2"'
          - fi
          - ''
        notify: []
        strategy:
          retry: '0'
