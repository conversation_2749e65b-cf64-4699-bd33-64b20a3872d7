<template>
  <el-tabs v-model="activeTab" class="custom-tabs">
    <el-tab-pane label="基础设置" name="basic">
      <MeasurementBasic />
    </el-tab-pane>
    <el-tab-pane label="显示设置" name="display">
      <MeasurementDisplay />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { ref } from 'vue'
import MeasurementBasic from './measurementBasic.vue'
import MeasurementDisplay from './measurementDisplay.vue'

const activeTab = ref('basic')
</script>

<style lang="less" scoped>
// 自定义 tabs 样式
.custom-tabs {
  ::v-deep(.el-tabs__nav-wrap) {
    // 移除默认的下划线
    &::after {
      content: none;
    }
  }

	::v-deep(.el-tabs__active-bar) {
		background-color: @link-6 !important;
	}

  ::v-deep(.el-tabs__item) {
    // 未选中状态样式
    &.is-active {
      // 选中状态样式
      color: @link-6; // 蓝色文字
      font-weight: bold;

      // &::after {
      //   content: '';
      //   position: absolute;
      //   left: 0;
      //   bottom: -2px; // 根据实际需求调整
      //   width: 100%;
      //   height: 2px; // 下划线高度
      //   background-color: #1856EB !important; // 蓝色下划线
      // }
    }
  }
}
</style>
