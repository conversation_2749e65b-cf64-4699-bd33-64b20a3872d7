


export default {
  // // 鉴权相关
  // permissionsCheck:(userId: string | number) =>
  //   `/access/users/${userId}/authorizations/permissions/check`,
  // 获取用户的角色列表
  operationsListPermissions: (userId: string | number) =>
    `/access/users/${userId}/operations/listPermissions`,
  operationsList: (userId: string | number) =>
    `/access/users/${userId}/permissions/operations/list`,

  // // 资源相关
  // accessResources:  `/access/resources`,
  // resourcesOperationsList: `/access/resources/operations/list`,
  // resourcesResourceId: (resourceId: string | number) =>
  //   `/access/resources/${resourceId}`,
  // operationsAttachResource: (roleId: string | number) =>
  //   `/access/roles/${roleId}/resources/operations/attachResource`,
  // roleIdResourcesOperationsList: (roleId: string | number) =>
  //   `/access/roles/${roleId}/resources/operations/list`,
  // operationsUnAttach: (roleId: string | number) =>
  //   `/access/roles/${roleId}/resources/operations/unAttach`,
  // roleIdResourcesResourceId: (roleId: string | number, resourceId: string | number) =>
  //   `/access/roles/${roleId}/resources/${resourceId}`,

  //角色相关
  accessRoles: `/access/roles`,
  rolesOperationsList: `/access/roles/operations/list`,
  rolesRoleId: (roleId: string | number) => `/access/roles/${roleId}`,
  operationsAttachUsers: (roleId: string | number) =>
    `/access/roles/${roleId}/users/operations/attachUsers`,
  operationsDetachUsers: (roleId: string | number) =>
    `/access/roles/${roleId}/users/operations/detachUsers`,
  usersOperationsList: (roleId: string | number) =>
    `/access/roles/${roleId}/users/operations/list`,
  operationsListAssignable: (roleId: string | number) =>
    `/access/roles/${roleId}/users/operations/listAssignable`,
  userIdRoles: (userId: string | number) => `/access/users/${userId}/roles`,
  operationsListRoles: (userId: string | number) =>
    `/access/users/${userId}/roles/operations/listRoles`
}
