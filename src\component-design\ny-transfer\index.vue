<template>
  <el-transfer class="ny-transfer" v-bind="props" v-model="value">
    <template #default="scope" v-if="$slots['default']">
      <slot v-bind="scope"></slot>
    </template>
    <template #left-footer="scope" v-if="$slots['left-footer']">
      <slot></slot>
    </template>
    <template #left-empty="scope" v-if="$slots['left-empty']">
      <slot></slot>
    </template>
    <template #right-footer="scope" v-if="$slots['right-footer']">
      <slot></slot>
    </template>
    <template #right-empty="scope" v-if="$slots['right-empty']">
      <slot></slot>
    </template>
  </el-transfer>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import NyTransferProps from '.'

const props = defineProps<NyTransferProps>()
const emit = defineEmits(['update:modelValue'])

const value = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})
</script>

<style lang="less">
.ny-transfer {
  .el-transfer-panel__item {
    padding: 8px 0;
    display: flex !important;
    line-height: 36px;
    height: 52px;
    border-bottom: 1px solid #e2e8f0;
    margin:0px 16px;
    padding: 0 16px;

    .el-checkbox__input {
      position: relative;
      top: 0;
    }
  }

  .el-checkbox__label {
    padding-left: 4px !important;
  }

  .el-transfer__buttons {
    flex-direction: column;
    justify-content: center;
    padding: 0 16px;

    .el-transfer__button {
      border-radius: 30px;
      padding: 0;
      width: 32px;
      margin: 6px 0;

      span {
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .el-transfer-panel__empty {
    padding: 16px;
  }

  .el-transfer-panel__body {
    max-height: calc(100% - 32px);

    .el-transfer-panel__list {
      max-height: calc(100% - 32px);
    }
  }
  .el-checkbox__label{
    flex:1

  }

}
</style>
