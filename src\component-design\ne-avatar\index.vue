<template>
  <div class="ne-avatar-wrapper">
    <div class="ne-avatar-circle" :style="{ backgroundColor: avatarColor }">
      <span class="ne-avatar-text">{{ avatarText }}</span>
    </div>
    <span v-if="name" class="ne-avatar-name">{{ name }}</span>
  </div>
</template>

<script setup lang="ts">
import { ColorList } from '@/constant'
import { computed } from 'vue'
const props = defineProps({
  text: { type: String, default: '' },
  name: { type: String, default: '' },
  color: { type: String, }
})
const avatarText = computed(() => {
  if (props.text) return props.text
  if (props.name) return props.name.slice(-1)
  return ''
})
const avatarColor = computed(() => {
  if (props.color) return props.color
  return ColorList[Math.floor(Math.random() * ColorList.length)]
})
const { name } = props
</script>

<style scoped>
.ne-avatar-wrapper {
  display: flex;
  align-items: center;
}

.ne-avatar-circle {
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.ne-avatar-text {
  color: #fff;
  font-weight: 500;
}

.ne-avatar-name {
  color: #374151;
  font-weight: 400;
}
</style>