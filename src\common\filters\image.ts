/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-10-14 14:47:22
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-10-29 16:55:56
 * @FilePath: /neue-cloud-vdi/src/common/filters/image.ts
 */
import { SelectItem } from '../types/common'

export const deployStatus: Array<SelectItem> = [
  { key: '1', value: '部署中', icon: 'icon-tips-ing', class: 'status-ing' },
  {
    key: '0',
    value: '已部署',
    icon: 'icon-tips-success',
    class: 'status-success'
  },
  { key: '-1', value: '部署失败', icon: 'icon-tips-fail', class: 'status-fail' },
  { key: '-2', value: '未部署', icon: 'icon-tips-minus', class: 'status-none' }
]
