import { createRouter, createWeb<PERSON><PERSON><PERSON><PERSON><PERSON>, Router, RouteRecordRaw } from 'vue-router'
import { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useUserInfoStore } from '@/store/user/userInfo'
import { account as accountService, cdp as cdpService } from '@/service'
import storage from '@/utils/storage'
// import { RoleDetail, PermissionDetail } from '@/common/types/console/role'
import Home from '@/views/home/<USER>'
import Login from '@/views/login.vue'

import UserManage from '@/views/console/users/index.vue'
import { consoleNavsList } from '@/common/filters/nav'
import test from '@/views/test/index.vue'
import CadDetail from '@/views/cdp/cad-detail/index.vue'
// import { useUserStore } from '@/store/user'
import cax from '@/views/cax/index.vue'
import caxModelFile from '@/views/cax/NeTreeTable/index.vue'
import caxCdp from '@/views/cax/cax-cdp.vue'
import caxAgentNotStart from '@/views/cax/cax-agent-not-start.vue'
import caxCdpMain from '@/components/cdp/cax-cdp.vue'
import SpaceSetting from '@/views/space-setting/index.vue'
import DesignPart from '@/views/design-part/index.vue'
import Bom from '@/views/bom/index.vue'

import { usePagesStore } from '@/store/common/usePagesStore'
import {
  RouteDetail,
  extractUniquePermissions,
  mapPermissionsToPaths,
  checkPermissionExists,
  filterNavs
} from './auth-guard'
import { ElMessage } from 'element-plus'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: '',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/cad-detail',
    name: 'CadDetail',
    component: CadDetail
  },
  {
    path: '/cax/cax-agent-not-start',
    name: 'CaxAgentNotStart',
    component: caxAgentNotStart
  },
  {
    path: '/cax',
    name: 'Cax',
    component: cax,
    redirect: '/cax/cax-model-file',
    children: [
      {
        path: 'cax-model-file',
        name: 'caxModelFile',
        component: caxModelFile
      },
      {
        path: 'cax-cdp',
        name: 'caxCdp',
        component: caxCdpMain,
        redirect: '/cax/cax-cdp/home',
        children: [
          {
            path: 'home',
            name: 'cax-Home',
            component: Home
          },
          {
            path: 'cad-detail',
            name: 'cax-CadDetail',
            component: CadDetail
          },
          {
            path: 'design-part',
            name: 'cax-DesignPart',
            component: DesignPart
          }
        ]
      }
    ]
  },
  {
    path: '/space-setting',
    name: 'SpaceSetting',
    component: SpaceSetting
  },
  {
    path: '/design-part',
    name: 'DesignPart',
    component: DesignPart
  },
  {
    path: '/bom',
    name: 'Bom',
    component: Bom
  },
  {
    path: '/test',
    name: 'Test',
    component: test
  },
  {
    path: '/console',
    name: 'Console',
    redirect: 'user-manage',
    children: [
      {
        path: 'user-manage',
        name: 'UserManage',
        component: UserManage
      }
    ]
  }
]

const router: Router = createRouter({
  history: createWebHashHistory(),
  routes
})

async function authGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const userInfoStore = useUserInfoStore()

  // 判断路径里是否包含cax, 并存储agentId
  let isCax = to.path.includes('cax')
  if (to.query.agentId && isCax) {
    storage.set('agentId', to.query.agentId)
    //随机生成一个4位的字母数字
    const random = Math.random().toString(36).substring(2, 6)
    storage.set('agentUserId', random)
    console.log(1)
  }

  // 进入登录页前，agent会检查是否启动，若无启动，会直接打开到agent未启动页面，不进行权限校验
  if (isCax && to.name === 'CaxAgentNotStart') {
    next()
    return
  }

  if (to.name !== 'Login' && to.name !== 'CaxAgentNotStart') {
    try {
      const res: any = await cdpService.postOperationsAccess()
      console.log('pageAccess', res)
      if (!res || !res.data) {
        isCax
          ? next({ name: 'Login', query: { redirect: to.fullPath } })
          : next({ name: 'Login' })
        return
      }
      const { id } = res.data?.userMeta
      console.log(res.data)
      // 用户信息
      userInfoStore.setUserInfo(res.data?.userMeta)
      storage.set('uid', id)
      storage.set('userName', res.data?.userMeta?.name)
      // 用户角色信息
      userInfoStore.setUserRoleDetail(res.data?.roles)
      // 用户权限信息
      // userInfoStore.setPermissionList(res.data?.permissions)

      // userInfoStore.setUserNavList([])
      // // const userRoutes = userInfoStore.getPermissionList
      // const tPath = (to.path as string).toLowerCase().split('/')
      // const tName = tPath[tPath.length - 1]
      // if (userInfoStore.hasManageRole) {
      //   const resPermissions: any = await accessService.operationsListPermissions({
      //     userId
      //   })
      //   const roleRes = resPermissions.data
      //   console.log('roleRes', roleRes)
      //   const permissions = extractUniquePermissions(roleRes)
      //   const permissionList = mapPermissionsToPaths(permissions)
      //   const navList = filterNavs(consoleNavsList, permissionList)
      //   userInfoStore.setPermissionList(permissionList)
      //   userInfoStore.setUserNavList(navList)
      //   if (checkPermissionExists(tName) && !permissionList.includes(tName)) {
      //     ElMessage.error('您没有该页面的访问权限')
      //     next({ name: 'Home' })
      //     return
      //   }
      // } else {
      //   console.log('没有权限', to, tName, checkPermissionExists(tName))
      //   if (checkPermissionExists(tName)) {
      //     ElMessage.error('您没有该页面的访问权限')
      //     next({ name: 'Home' })
      //     return
      //   }
      // }
    } catch (error) {
      console.error('权限校验失败:', error)
      isCax
        ? next({ name: 'Login', query: { redirect: to.fullPath } })
        : next({ name: 'Login' })
      return
    }
  }
  const pagesStore = usePagesStore()
  pagesStore.setHeaderToobarInit()

  if (
    from.path.includes('cax-cdp') &&
    !to.path.includes('cax-model-file') &&
    !to.path.includes('login')
  ) {
    to.name?.includes('cax-')
      ? next()
      : next({ name: `cax-${String(to.name)}`, query: to.query, params: to.params })
    return
  } else {
    pagesStore.setPathFrom(from.path)
  }
  next()
}

router.beforeEach(authGuard)

export default router
