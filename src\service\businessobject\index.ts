import http from '@/service/http'
import url from './index-url'
import { CollaborationZone } from '@/common/types/space'

const { get, post, put, deleteReq } = http

export default {
  // 创建项目协作区
  postBusinessobject(params: CollaborationZone) {
    return post(url.businessobjectCreate, params)
  },
  // 修改项目协作区
  putBusinessobject(params: CollaborationZone) {
    return put(url.businessobjectId(params.ncid), params)
  },
  // 删除项目协作区
  deleteBusinessobject(params: CollaborationZone) {
    return deleteReq(url.businessobjectId(params.ncid), params)
  },
  // 获取项目协作区详情
  getBusinessobject(params: CollaborationZone) {
    return get(url.businessobjectId(params.ncid), params)
  },
  postBusinessobjectList(params: CollaborationZone) {
    return post(url.businessobjectList, params)
  }
}