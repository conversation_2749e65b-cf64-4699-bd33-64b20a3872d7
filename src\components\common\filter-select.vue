<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-09-02 09:57:58
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-09-02 10:33:21
 * @FilePath: /neue-cloud-vdi/src/components/common/filter-select.vue
-->
<template>
  <div>
    <i class="iconfont icon-edit-filter" @click="handleFilterOpen('env')"></i>
    <!-- <el-select
      v-model="value"
      :placeholder="placeholder"
      :filterable="filterable"
      :clearable="clearable"
      @change="handleChange"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
        <i v-if="item.icon" :class="`iconfont ${ item.icon }`"></i>
      </el-option>
    </el-select> -->
  </div>
</template>
<script setup lang="ts">

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  filterable: {
    type: Boolean,
    default: true
  },
  clearable: {
    type: Boolean,
    default: true
  },
  value: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['change'])

const handleChange = (value: string) => {
  // emit('change', value)
}
const handleFilterOpen = (name: string) => {
}
</script>
