<!--
 * <AUTHOR>
 * @description: 首页
 * @date: 2022-08-03 15:08
 * @lastEditors: wangyan-judy
 * @lastEditTime: 2022-08-03 15:08
 * @FilePath: /neue-cloud-vdi/src/views/home/<USER>
-->
<template>
  <div class="home-container page-container">
    <el-container>
      <el-main>
        <div class="content">
          <el-header>{{ title }}</el-header>
          <slot></slot>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: '主页'
  }
})
</script>

<style scoped lang="less">
.el-container {
  height: 100%;
  padding-right: 20px;
}

.el-main {
  padding: 0;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: @border-radius-3;
    background: @bg-color-white;
  }
  .user-data {
    margin-top: 20px;
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: row;
    .user-data-item {
      flex: 1;
      border-radius: @border-radius-3;
      background: @bg-color-white;
      display: flex;
      flex-direction: column;
      .item-title {
        text-align: left;
        font-size: @text-1;
        font-weight: @font-weight-2;
        line-height: 22px;
        padding: 12px 24px;
      }
    }
  }
  .el-tabs {
    flex: 1;
    padding: 10px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    .el-tabs__content {
      flex: 1 !important;
      height: calc(100% - 54px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  .el-tabs__content {
    // min-height: 380px;
    overflow: scroll;
  }

  .el-header {
    padding-left: 8px;
    text-align: left;
    font-size: @text-4;
    font-weight: bold;
    height: 40px;
  }
  .el-tabs__nav-wrap {
    &::after {
      height: 1px;
    }
  }
  .el-tabs__content {
    flex: 1;
    width: 100%;
  }
}
.demo-tabs .el-tabs__content {

  .el-main {
    position: relative;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 0;
    .nomal-show {
      position: absolute;
      width: 100%;
      height: 0;
      padding-bottom: calc(37% + 36px);
      background-image: url('@/assets/images/preview.jpg');
      background-position: top center;
      background-repeat: no-repeat;
      background-size: 100% auto;
      // background-size: cover;
      display: flex;
      justify-content: center;
      align-items: flex-end;
    }
    .nomal-text {
      width: 100%;
      height: 30px;
      line-height: 30px;
      position: absolute;
      bottom: 0;
      text-align: center;
      .nomal-title {
        font-size: @text-6;
        font-weight: bold;
        color: @text-color-3;
      }
      .nomal-description {
        font-size: @text-0;
        color: @text-color-5;
      }
    }
  }
}
.apps-content-none {
  flex-wrap: wrap;
  flex-direction: row;
}
.apps-content-home {
  justify-items: left;
  flex-direction: column;
}
.apps-content {
  flex: 1;
  display: flex;
  .app-view {
    height: 50%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }
  .app-info {
    height: 50%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .app-title {
      font-size: @text-8;
      font-weight: @font-weight-3;
      color: @primary-4;
    }
    .app-desc {
      font-size: @text-0;
      color: @text-color-2;
      margin-top: 4px;
    }
    .app-enter {
      margin-top: 12px;
    }
    .el-button--primary {
      border-radius: @border-radius-5 !important;
    }
  }
  .tab-name {
    display: flex;
    padding-left: 24px;
    height: 22px;
    font-size: @text-1;
    font-weight: @font-weight-2;
    color: @text-color-2;
  }
  .home-app-list {
    padding-left: 24px;
    padding-right: 24px;
  }
  .app-nocontent {
    height: 50%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
  .app-card {
    min-width: 160px;
    margin-top: 16px;
    background-color: @primary-1;
    border-radius: @border-radius-5;
    padding: 20px;
    cursor: pointer;
  }
  .app-detail {
    display: flex;
    flex-direction: row;
    align-items: center;
    .app-icon {
      margin-right: 12px;
      width: 36px;
      height: 36px;
      border-radius: @border-radius-2;
      background: @bg-color-0;
    }
    .app-name {
      text-align: left;
      font-size: @text-1;
      font-weight: @font-weight-2;
      color: @text-color-3;
    }
    .app-version {
      text-align: left;
    }
  }
  .app-description {
    text-align: left;
    margin-top: 12px;
    font-size: @text-0;
    color: @text-color-5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
  .app-tags {
    text-align: left;
    margin-top: 8px;
  }
}
</style>
<style lang="less">
.page-container {
  .demo-tabs {
    .el-tabs__content {
      flex: 1 !important;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
/* 当宽度大于等于 1600px 时，调整为四列布局 */
@media (min-width: 1680px) {
  .home-app-list ul {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 16px;
  }
}
@media (max-width: 1679px) {
  .home-app-list ul {
    display: grid;
    column-gap: 16px;
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
