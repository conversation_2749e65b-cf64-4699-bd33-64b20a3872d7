server {
    listen 9112;  # 监听 9082 端口

    # 转发 /account/* 到 127.0.0.1:8081
    location ~ ^/account/ {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 转发 /file/*/folder/* 到 127.0.0.1:8082
    location ~ ^/file/.*/folder/ {
        proxy_pass http://127.0.0.1:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 转发 /vdi/* 到 127.0.0.1:8083
    location ~ ^/vdi/ {
        proxy_pass http://127.0.0.1:8083;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}