{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "allowJs": true,
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "types": [ "element-plus/global.d.ts","vite/client", "jest", "vue-router", "node"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"] ,
      "@neue/resolver": ["node_modules/@neue/resolver"],
    }
  },
  "vueCompilerOptions": {
    "target": 3.3
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]
}
