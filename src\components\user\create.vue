<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-17 16:44:28
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-21 10:20:23
 * @FilePath: /neue-cloud-vdi/src/components/user/create.vue
-->
<template>
  <el-dialog
    id="dialog-user-dialog"
    v-model="props.visible"
    :title="dialogTitle"
    :style="{
      width: '520px'
    }"
    class="dialog-user"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" status-icon>
      <el-form-item label="用户名称" prop="user">
        <el-input
          v-model="ruleForm.user"
          class="w-[320px]"
          minlength="2"
          maxlength="32"
          placeholder="支持2-32位字符，必须以字母或中文开头"
        />
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input v-model="ruleForm.phone" class="w-[320px]" minlength="11" maxlength="11" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="ruleForm.email"
          :disabled="props.openType === 1"
          class="w-[320px]"
          minlength="3"
          maxlength="126"
        />
      </el-form-item>
      <el-form-item label="初始密码" prop="password">
        <el-input v-model="ruleForm.password" disabled class="w-[320px]" />
      </el-form-item>
      <!-- <el-form-item label="角色" prop="role">
        <el-select v-model="ruleForm.role" placeholder="请输入">
          <el-option label="普通用户" value="user" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="角色" prop="tag">
        <el-select v-model="ruleForm.tag" placeholder="请输入">
          <el-option label="设计师" value="设计师" />
          <el-option label="管理者" value="管理者" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="备注">
        <!-- <el-input
          v-model="ruleForm.extra"
          type="textarea"
          class="w-[320px]"
          :rows="3"
          show-word-limit
          minlength="0"
          maxlength="255"
        /> -->
        <ny-textarea
          v-model="ruleForm.extra"
          class="w-[320px]"></ny-textarea>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="console-btn-cancel" @click="handleClose">取消</el-button>
        <el-button
          class="console-btn-confirm"
          type="primary"
          @click="handleSave(ruleFormRef)"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, watchEffect, computed } from 'vue'
import {
  ElMessage,
  type ComponentSize,
  type FormInstance,
  type FormRules
} from 'element-plus'
import { UserDetail } from '@/common/types/console/user'
// import user from '@/service/console/user'
import { account as accountService } from '@/service'
import { validateUserName, validatePhone, validateEmail } from '@/utils/validate'
import storage from '@/utils/storage'

const emit = defineEmits(['update:visible', 'handleAddComplete', 'handleEditComplete'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '创建'
  },
  openType: {
    type: Number,
    // 0: 创建，1: 编辑
    default: 1
  },
  userDetail: {
    type: Object as () => UserDetail,
    default: () => {
      return {}
    }
  }
})

const ruleForm: UserDetail = reactive({
  user: '',
  email: '',
  phone: '',
  password: 'nuoyuan2024',
  // 角色
  tag: '',
  extra: ''
})

const validatePhoneNum = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入手机号码'))
  } else if (!validatePhone(value)) {
    callback(new Error('请输入正确的手机号码'))
  } else {
    callback()
  }
}

const validateEmailStr = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入邮箱地址'))
  } else if (!validateEmail(value)) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}

const validateNameStr = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入用户名称'))
  } else if (value.length < 2 || value.length > 32) {
    callback(new Error('请输入2-32位字符'))
  } else if (!validateUserName(value)) {
    callback(new Error('必须以字母或中文开头，支持数字、下划线'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  user: [{ required: true, trigger: 'blur', validator: validateNameStr }],
  phone: [{ required: true, trigger: 'blur', validator: validatePhoneNum }],
  email: [{ required: true, trigger: 'blur', validator: validateEmailStr }],
  role: [{ required: false, message: '请选择', trigger: 'change' }],
  extra: [{ required: false, message: '请输入', trigger: 'change' }]
})

const ruleFormRef = ref<FormInstance>()
const dialogTitle = computed(() => {
  return props.openType === 1 ? '编辑' : '创建'
})

const submitForm = () => {
  if (props.openType === 1) {
    // 编辑
    const editOptions = {
      userId: props.userDetail.userId,
      user: ruleForm.user,
      phone: ruleForm.phone,
      // role: ruleForm.role,
      tag: ruleForm.tag,
      extra: ruleForm.extra
    }
    accountService.putAccountUser(editOptions).then((res: any) => {
      console.log('编辑用户res', res)
      ElMessage.success('更新成功')
      handleClose()
      emit('handleEditComplete')
    })
  } else {
    accountService
      .postAccountUser({
        ...ruleForm
      })
      .then((res: any) => {
        console.log('添加用户res', res)
        ElMessage.success('创建成功')
        handleClose()
        emit('handleAddComplete')
      })
  }
}

const resetForm = () => {
  ruleFormRef.value?.resetFields()
  ruleForm.user = ''
  ruleForm.email = ''
  ruleForm.phone = ''
  ruleForm.extra = ''
  ruleForm.tag = ''
}

const handleSave = async (formEl: FormInstance | undefined) => {
  console.log('handleSave==>', formEl, ruleFormRef.value)
  if (!formEl) {
    return
  }
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      console.log('submitForm==>提交', ruleForm)
      submitForm()
    } else {
      console.log('error submit!', fields)
    }
  })
}

const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

watch(
  () => props.visible,
  (newVal: boolean) => {
    if (newVal) {
      console.log('ruleForm', ruleForm, props.userDetail)
      if (props.openType === 1) {
        ruleForm.user = props.userDetail.user
        ruleForm.email = props.userDetail.email
        ruleForm.phone = props.userDetail.phone
        ruleForm.extra = props.userDetail.extra
        ruleForm.tag = props.userDetail.tag
        ruleForm.role = props.userDetail.role
      }
    } else {
      resetForm()
    }
  }
)
</script>
<style lang="less">

</style>
