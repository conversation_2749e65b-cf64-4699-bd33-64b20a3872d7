<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-12-04 16:52:29
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-12-04 17:45:35
 * @FilePath: /neue-cloud-vdi/src/views/console/users/role-create.vue
-->
<template>
  <div class="third-container w-0 flex-1">
    <div class="third-menu">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/console/role-manage' }">角色</el-breadcrumb-item>
        <el-breadcrumb-item>创建角色</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <el-container class="bg-white rounded-lg mr-5 flex-1">
      <el-main class="main-content flex items-center flex-col relative ">
        <ny-page-title :title="'创建角色'" class="mb-5 w-full"></ny-page-title>
        <role-edit ref="childRef" class="mr-[20%]"></role-edit>
      </el-main>
      <el-footer>
        <div class="py-5 border-t-[1px] flex items-center flex-col">
          <div class="w-[700px] mr-[20%] pl-[200px]">
            <el-button type="primary" @click="submitForm()"> 确定 </el-button>
            <el-button @click="resetForm()">取消</el-button>
          </div>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import RoleEdit from '@/components/role/role-edit.vue'
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
const childRef = ref();
const router = useRouter();
const submitForm = async () => {
  childRef.value?.submitForm().then((res:any) => {
    if (res.code == 200) {
      ElMessage({
        message: '创建成功',
        type: 'success'
      })
      router.go(-1);
    }
  })
}
const resetForm = () => {
  childRef.value?.resetForm();
  router.go(-1);
}
</script>
<style lang="less" scoped></style>
