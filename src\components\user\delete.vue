<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-23 17:17:47
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-14 14:39:15
 * @FilePath: /neue-cloud-vdi/src/components/user/delete.vue
-->

<template>
  <el-dialog
    v-model="props.visible"
    destroy-on-close
    :close-on-click-modal="false"
    :title="dialogInfo.title"
    :style="{
      width: '640px',
      maxHeight: '710px'
    }"
    class="dialog-user-del"
    :before-close="handleClose"
  >
    <template #default>
      <div class="dialog-content">
        <div class="dialog-title">
          <div class="dialog-title-text">
            <i class="iconfont icon-exclamation-circle-fill"></i>
            <span>{{ dialogInfo.warn1 }}</span>
          </div>
          <div class="dialog-title-desc">{{ dialogInfo.warn2 }}</div>
        </div>
        <div class="dialog-delete-users" style="max-height: 460px; overflow: auto;">
          <el-table :data="pageUsers" style="width: 100%;" :highlight-current-row="true">
            <el-table-column property="user" label="用户名称" ></el-table-column>
            <el-table-column property="email" label="邮箱"></el-table-column>
            <!-- <el-table-column property="phone" label="电话" ></el-table-column> -->
            <!-- <el-table-column property="role" label="角色">
              <template #default="scope">
                <div style="display: flex; align-items: center">
                  <span>{{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}</span>
                </div>
              </template>
            </el-table-column> -->
            <!-- <el-table-column property="extra" label="备注"></el-table-column> -->
          </el-table>
        </div>
        <div class="pagination-container">
          <el-config-provider>
            <el-pagination
              class="pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :hide-on-single-page="false"
              :current-page="currentPage"
              :page-sizes="[5, 10, 20, 50]"
              :page-size="pageSize"
              layout="total, prev, pager, next"
              :total="userTotal">
            </el-pagination>
          </el-config-provider>
        </div>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, watchEffect } from 'vue'
import { UserDetail, OpenDialog } from '@/common/types/console/user'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  users: {
    type: Array<UserDetail>,
    default: () => {
      return []
    }
  }
})

const emit = defineEmits(['update:visible', 'handleDelete', 'show'])

const pageSize = ref(10)
const currentPage = ref(1)
const userList = ref<Array<UserDetail>>([])
const pageUsers = ref<Array<UserDetail>>([])
const userTotal = ref(0)
const dialogInfo = ref({
  title: '删除',
  warn1: '确定要删除以下用户吗?',
  warn2: '删除操作无法恢复，请谨慎操作。'
})

const setUserList = (users: Array<UserDetail>) => {
  userList.value = users
  userTotal.value = users.length
  setPageUsers()
}
const setPageUsers = () => {
  pageUsers.value = userList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
}


const handleSizeChange = (val: number) => {
  pageSize.value = val
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleClose = () => {
  emit('update:visible', false)
}
const handleSave = () => {
  const userId = userList.value.map(user => user.userId)
  emit('handleDelete', userId)
}
const show = (opts: OpenDialog) => {
  setUserList(opts.users)
  opts.dialog && (dialogInfo.value = opts.dialog)
  emit('update:visible', true)
}

watch(
  [currentPage, pageSize],
  () => {
    setPageUsers()
  }
)

watch(
  () => props.visible,
  (val) => {
    if (val) {
      currentPage.value = 1
    }
  }
)
// watchEffect(() => {
//   props.users.length > 0 && setUserList(props.users)
// })

defineExpose ({
  show
})
</script>

<style lang="less" scoped>

.icon-exclamation-circle-fill {
  color: @warning-5;
  font-size: @text-4;
}
.dialog-content {
  color: @text-color-2;
  .dialog-title {
    text-align: left;
  }
  .dialog-title-text {
    // 垂直居中
    display: flex;
    align-items: center;
    height: 24px;
    font-size: @text-1;
    i {
      margin-right: 12px;
    }
  }
  .dialog-title-desc {
    padding-left: 32px;
    line-height: 18px;
    font-size: @text-0;
    color: @text-color-4;
  }
  .dialog-delete-users {
    padding-left: 32px;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}
</style>
