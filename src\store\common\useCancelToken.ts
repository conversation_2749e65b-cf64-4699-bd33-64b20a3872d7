import { defineStore } from 'pinia'

const useCancelToken = defineStore('cancelTokenStore', {
  state: () => ({
    cancelTokensMap: new Map()
  }),
  actions: {
    addCancelToken(requestUniqueId: number, source: any) {
      this.cancelTokensMap.set(requestUniqueId, source)
    },
    cancelRequest(requestUniqueId: number) {
      if (this.cancelTokensMap.has(requestUniqueId)) {
        this.cancelTokensMap.get(requestUniqueId).cancel('请求已取消')
        this.cancelTokensMap.delete(requestUniqueId)
      }
    }
  }
})

export default useCancelToken
