/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2025-01-23 10:23:08
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-25 09:51:28
 * @FilePath: /neue-cloud-vdi/src/service/account/index.ts
 */
import http from '@/service/http'
import url from './index-url'
import {
  UserAccess,
  UserList,
  UserGroupEdit,
  UserListAPP
} from '@/common/types/console/user'
import { GroupDetail, GroupMembers, TreeItem } from '@/common/types/console/group'
import type { RquestParams } from '@/common/types/login'

const { get, post, put, deleteReq } = http
export default {
  postOperationsAccess() {
    return get(url.operationsAccess)
  },
  postOperationsLogin(params: RquestParams) {
    return post(url.operationsLogin, params)
  },
  postOperationsResetPassword(params: UserAccess) {
    return post(url.operationsResetPassword(params.userId), params)
  },
  postOperationsListApp(params: UserListAPP) {
    return post(url.operationsListApp(params.userId), params)
  },
  postAccountUser(params: UserAccess) {
    return post(url.accountUser, { account: params })
  },
  putAccountUser(params: UserAccess) {
    return put(url.accountUserUid(params.userId), { account: params })
  },
  postOperationsList(params: UserList) {
    return post(url.operationsList, params)
  },
  postOperationsBatchDelete(params: Array<number> | any) {
    return post(url.operationsBatchDelete, params)
  },
  postOperationsGetLatency(params: UserAccess) {
    return post(url.operationsGetLatency, params)
  },
  postOperationsAllocate(params: UserGroupEdit) {
    return post(url.operationsAllocate(params.userId), params)
  },
  operationsFindGroups(params: UserAccess) {
    return url.operationsFindGroups(params.userId), params
  },
  operationsListGroups(params: UserAccess) {
    return get(url.operationsListGroups(params.userId), params)
  },
  postAccountGroups(params: GroupDetail) {
    return post(url.accountGroups, params)
  },
  putAccountGroupsId(params: GroupDetail) {
    return put(url.accountGroupsId(params.id), params)
  },
  postOperationsRemoveGroup(params: GroupDetail) {
    return post(url.operationsRemoveGroup(params.groupId), params)
  },
  postOperationsListUsers(params: TreeItem) {
    return post(url.operationsListUsers(params.id), params)
  },
  postOperationsAddAccounts(params: GroupMembers) {
    return post(url.operationsAddAccounts(params.id), params)
  },
  postOperationsRemoveAccounts(params: GroupMembers) {
    return post(url.operationsRemoveAccounts(params.id), params)
  },
  postOperationsListAccounts(params: GroupMembers) {
    return post(url.operationsListAccounts(params.id), params)
  },
  postOperationsListAssignableAccounts(params: GroupMembers) {
    return post(url.operationsListAssignableAccounts(params.groupId), params)
  }
}
