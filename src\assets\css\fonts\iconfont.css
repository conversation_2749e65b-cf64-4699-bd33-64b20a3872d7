/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: "iconfont"; /* Project id 4913835 */
  src: url('iconfont.woff2?t=1747985670827') format('woff2'),
       url('iconfont.woff?t=1747985670827') format('woff'),
       url('iconfont.ttf?t=1747985670827') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-download:before {
  content: "\e765";
}

.icon-a-3Dwenjian-idi:before {
  content: "\e763";
}

.icon-a-3Dwenjian-zhuangpei:before {
  content: "\e764";
}

.icon-a-3Dwenjian-zhumoxing:before {
  content: "\e766";
}

.icon-zhengxu:before {
  content: "\e762";
}

.icon-general-calendar:before {
  content: "\e761";
}

.icon-Launch:before {
  content: "\e760";
}

.icon-general-search:before {
  content: "\e75e";
}

.icon-general-user-add:before {
  content: "\e75f";
}

.icon-a-3D-measure-obj-close:before {
  content: "\e75b";
}

.icon-a-3D-measure-obj-far:before {
  content: "\e75d";
}

.icon-a-3D-mesure-point:before {
  content: "\e758";
}

.icon-a-3D-mesure-arc:before {
  content: "\e756";
}

.icon-a-3D-mesure-volume1:before {
  content: "\e75c";
}

.icon-a-3D-mesure-all:before {
  content: "\e757";
}

.icon-a-3D-mesure-curvedsurface:before {
  content: "\e759";
}

.icon-a-3D-mesure-face:before {
  content: "\e75a";
}

.icon-shaixuan-congyuandaojin:before {
  content: "\e755";
}

.icon-CAX:before {
  content: "\e754";
}

.icon-a-Group19120550271:before {
  content: "\e62e";
}

.icon-a-Group1912055027:before {
  content: "\e62c";
}

.icon-a-3D-hotkey:before {
  content: "\e753";
}

.icon-a-3D-boom-enlarge:before {
  content: "\e750";
}

.icon-a-3D-boom-gather:before {
  content: "\e751";
}

.icon-a-3D-boom:before {
  content: "\e752";
}

.icon-a-3D-color:before {
  content: "\e74e";
}

.icon-a-3D-rotate:before {
  content: "\e74f";
}

.icon-a-3D-selectface:before {
  content: "\e74a";
}

.icon-a-3D-cut-z:before {
  content: "\e74b";
}

.icon-a-3D-cut-y:before {
  content: "\e74c";
}

.icon-a-3D-cut-x:before {
  content: "\e74d";
}

.icon-a-3D-Reverse:before {
  content: "\e749";
}

.icon-a-3D-cut:before {
  content: "\e746";
}

.icon-a-3D-measure-dis:before {
  content: "\e747";
}

.icon-a-3D-measure-obj:before {
  content: "\e748";
}

.icon-a-3D-measure-dis-select:before {
  content: "\e743";
}

.icon-a-3D-cut-select:before {
  content: "\e744";
}

.icon-a-3D-measure-obj-select:before {
  content: "\e745";
}

.icon-a-3D-show-surface:before {
  content: "\e742";
}

.icon-a-3D-zhengjiao:before {
  content: "\e741";
}

.icon-a-3D-show-Threading:before {
  content: "\e738";
}

.icon-a-3D-show-line:before {
  content: "\e73a";
}

.icon-a-3d-toushi:before {
  content: "\e73f";
}

.icon-a-3D-fit:before {
  content: "\e737";
}

.icon-a-3D-zoom:before {
  content: "\e735";
}

.icon-a-3D-resert:before {
  content: "\e740";
}

.icon-a-3D-view-right:before {
  content: "\e736";
}

.icon-a-3D-view-down:before {
  content: "\e739";
}

.icon-a-3D-view-left:before {
  content: "\e73b";
}

.icon-a-3D-view-up:before {
  content: "\e73c";
}

.icon-a-3D-view-front:before {
  content: "\e73d";
}

.icon-a-3D-view-back:before {
  content: "\e73e";
}

.icon-tips-close:before {
  content: "\e732";
}

.icon-tips-delete:before {
  content: "\e733";
}

.icon-tips-check:before {
  content: "\e734";
}

.icon-neuecax1:before {
  content: "\e731";
}

.icon-file:before {
  content: "\e62d";
}

.icon-tree-jiegou:before {
  content: "\e62b";
}

.icon-interactive-button-refresh:before {
  content: "\e62a";
}

.icon-fail-circle-fill:before {
  content: "\e629";
}

.icon-a-fangzhen1:before {
  content: "\e624";
}

.icon-a-tuzhi1:before {
  content: "\e625";
}

.icon-a-zhuangpei1:before {
  content: "\e626";
}

.icon-a-lingjian1:before {
  content: "\e627";
}

.icon-exit:before {
  content: "\e628";
}

.icon-daoxu:before {
  content: "\e71e";
}

.icon-paixu:before {
  content: "\e730";
}

.icon-back:before {
  content: "\e72e";
}

.icon-setting:before {
  content: "\e72d";
}

.icon-a-tree-designdata:before {
  content: "\e72f";
}

.icon-tree-eye:before {
  content: "\e72a";
}

.icon-tree-eye-invisible:before {
  content: "\e72b";
}

.icon-tree-more:before {
  content: "\e72c";
}

.icon-tips-question:before {
  content: "\e725";
}

.icon-tips-fail:before {
  content: "\e726";
}

.icon-tips-success:before {
  content: "\e727";
}

.icon-tips-fill-success:before {
  content: "\e728";
}

.icon-exclamation-circle-fill:before {
  content: "\e729";
}

.icon-caret-down:before {
  content: "\e723";
}

.icon-caret-right:before {
  content: "\e724";
}

.icon-eye-invisible:before {
  content: "\e71f";
}

.icon-eye1:before {
  content: "\e722";
}

.icon-quchu1:before {
  content: "\e720";
}

.icon-fanghui1:before {
  content: "\e721";
}

.icon-bin:before {
  content: "\e71c";
}

.icon-export:before {
  content: "\e718";
}

.icon-fanghui:before {
  content: "\e719";
}

.icon-Vector:before {
  content: "\e71a";
}

.icon-zhankai:before {
  content: "\e71b";
}

.icon-import:before {
  content: "\e71d";
}

.icon-shouqi:before {
  content: "\e716";
}

.icon-quchu:before {
  content: "\e717";
}

.icon-close:before {
  content: "\e715";
}

.icon-tree-part:before {
  content: "\e714";
}

.icon-index-project1:before {
  content: "\e712";
}

.icon-tree-assembly:before {
  content: "\e70f";
}

.icon-tree-drawing:before {
  content: "\e710";
}

.icon-index-part:before {
  content: "\e711";
}

.icon-general-empty:before {
  content: "\e708";
}

.icon-index-home:before {
  content: "\e709";
}

.icon-general-user:before {
  content: "\e70a";
}

.icon-direction-arrow-right:before {
  content: "\e70b";
}

.icon-general-plus:before {
  content: "\e70c";
}

.icon-general-application:before {
  content: "\e70d";
}

.icon-index-project:before {
  content: "\e707";
}

.icon-3D-resert:before {
  content: "\e740";
}

.icon-3D-camera:before {
  content: "\e741";
}

.icon-3D-view-front:before {
  content: "\e73d";
}

.icon-3D-zoom:before {
  content: "\e735";
}

.icon-3D-Threading:before {
  content: "\e742";
}

.icon-3D-measure-obj:before {
  content: "\e748";
}

.icon-3D-cut:before {
  content: "\e746";
}

.icon-3D-boom:before {
  content: "\e752";
}

.icon-3D-hotkey:before {
  content: "\e753";
}

.icon-3D-perspective:before {
  content: "\e73f";
}

.icon-3D-orthogonality:before {
  content: "\e741";
}

.icon-3D-view-up:before {
  content: "\e73c";
}

.icon-3D-view-down:before {
  content: "\e739";
}

.icon-3D-view-left:before {
  content: "\e73b";
}

.icon-3D-view-right:before {
  content: "\e736";
}

.icon-3D-view-front:before {
  content: "\e73d";
}

.icon-3D-view-back:before {
  content: "\e73e";
}

.icon-3D-zoom:before {
  content: "\e735";
}

.icon-3D-fit:before {
  content: "\e737";
}

.icon-3D-show-Threading:before {
  content: "\e738";
}

.icon-3D-show-line:before {
  content: "\e73a";
}

.icon-3D-show-surface:before {
  content: "\e742";
}

.icon-3D-measure-obj-select:before {
  content: "\e745";
}

.icon-3D-measure-dis-select:before {
  content: "\e743";
}

.icon-3D-cut-select:before {
  content: "\e744";
}

.icon-tips-close:before {
  content: "\e732";
}

.icon-tips-delete:before {
  content: "\e733";
}

.icon-3D-measure-face:before {
  content: "\e75a";
}

.icon-3D-measure-point:before {
  content: "\e758";
}

.icon-3D-measure-surface:before {
  content: "\e759";
}

.icon-3D-measure-volume:before {
  content: "\e75c";
}

.icon-3D-measure-arc:before {
  content: "\e756";
}

.icon-3D-measure-all:before {
  content: "\e757";
}
