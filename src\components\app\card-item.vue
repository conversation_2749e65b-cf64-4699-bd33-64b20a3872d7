<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-11-22 11:44:43
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-11-29 15:54:53
 * @FilePath: /neue-cloud-vdi/src/components/app/card-item.vue
-->
<template>
  <div class="card-item">
    <div class="app-detail">
      <div class="app-icon">
        <img :src="itemDetail.icon" width="44" alt="">
      </div>
      <div class="app-title">
        <div class="app-name">{{  itemDetail.name }}</div>
        <div class="app-version">{{  itemDetail.version  }}</div>
      </div>
    </div>
    <div class="app-tags">
      <div
        v-if="itemDetail.tag && (itemDetail.tag).trim().length > 0 && itemDetail.tag.split(',').length > 0"
        class="app-tag"
        v-for="tag in itemDetail.tag.split(',')"
        :key="tag"
      >
        <el-tooltip class="custom-tooltip">
          <template #default>
            <TextTag :text="tag" type="card" />
          </template>
          <template #content>
            <div style="max-width: 300px">
              <span>{{ tag }}</span>
            </div>
          </template>
        </el-tooltip>
      </div>
      <div v-else class="item-content item-tag-none">
        <i class="iconfont icon-general-tag"></i>
        暂无标签
      </div>
    </div>
    <el-tooltip v-if="itemDetail.description &&itemDetail.description.length > 0" class="custom-tooltip">
      <template #default>
        <div class="app-description">{{ itemDetail.description }}</div>
      </template>
      <template #content>
        <div style="max-width: 340px;">
          {{ itemDetail.description }}
        </div>
      </template>
    </el-tooltip>

    <div v-else class="app-description">--</div>
    <div class="app-info">
      <img :src="itemDetail.env &&  envUrls[itemDetail.env.toLowerCase()]" alt="" />
      <span>{{ itemDetail.env }}</span>
      <span class="line-hr"></span>
      <TextTag :text="itemDetail.category" :type="itemDetail.category === 'CAX' ? 'apptag1' : 'apptag2'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { AppDetail, AppList } from '@/common/types/console/app'
import { PropType } from 'vue';
import windows from '@/assets/images/console/app/icon-windows.svg'
import linux from '@/assets/images/console/app/icon-linux.svg'
import TextTag from '@/components/common/text-tag.vue'

const props = defineProps({
  itemDetail: {
    type: Object as PropType<AppDetail>,
    required: true
  }
})
const envUrls: any = {
  windows: windows,
  linux: linux
}

</script>

<style lang="less" scoped>
.card-item {
  min-width: 277px;
  height: 176px;
  border-radius: @border-radius-3;
  padding: 16px 20px 24px 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;

  .app-detail {
    display: flex;
    flex-direction: row;
    margin-bottom: 24px;
    .app-icon {
      width: 44px;
      height: 44px;
      margin-right: 20px;
    }
    .app-title {
      .app-name {
        font-size: @text-2;
        font-weight: @font-weight-3;
      }
      .app-version {
        font-size: @text-0;
        margin-top: 4px;
      }
    }
  }
  .app-tags {
    display: flex;
    .app-tag {
      margin-right: 8px;
    }
  }
  .app-description {
    flex: 1;
    font-size: @text-0;
    color: @text-color-5;
    margin-top: 4px;
    display: -webkit-box;
    max-height: 36px;
    line-height: 18px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .app-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 28px;
    .line-hr {
      width: 0;
      height: 12px;
      border-right: solid 1px #D8D8D8;
      margin: 0 8px;
    }
  }

}
</style>
