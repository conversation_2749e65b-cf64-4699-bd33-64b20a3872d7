<template>
  <el-dialog
    v-model="checkInVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="updateVisible"
    title="检入至协同空间"
    width="520"
    class="check-in-modal"
    :destroy-on-close="true"
  >
    <el-tree
      style="max-width: 600px"
      :data="collaborationData"
      :props="defaultProps"
      @node-click="handleNodeClick"
      :expand-on-click-node="false"
      :load="loadNode"
      lazy
    >
      <template #default="{ node, data }">
        <i class="iconfont icon-bin" style="color: #fdcd4b; margin-right: 8px"></i>
        <span>{{ data.name }}</span>
      </template>
    </el-tree>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="updateVisible">取消</el-button>
        <el-button type="primary" @click="confirm" :disabled="confirmDisable"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="progressVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    title="检入"
    width="520"
  >
    <div class="progress-container">
      <div
        class="progress-background"
        :style="{
          width: `${percentage}%`,
          borderRadius: '4px'
        }"
      >
      </div>
      <div class="progress-content">
        <img :src="bomIcon" alt="" />
        <div>文件名字</div>
        <div class="right">{{ percentage }}%</div>
      </div>
    </div>
    <div class="progress-text">{{ progressText }}</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" disabled>检入中...</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="failVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="failVisible = false"
    title="检入"
    width="520"
  >
    <div class="text-content">
      <i class="iconfont icon-tips-fill-fail"></i>
      <span>检入失败</span>
    </div>
    <div class="text-bottom">检入失败，请重新检入</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="failVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="successVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="successVisible = false"
    title="检入"
    width="520"
  >
    <div class="text-content success">
      <i class="iconfont icon-tips-fill-success"></i>
      <span>检入成功</span>
    </div>
    <div class="text-bottom">检入结果，可以到“<span>发动机项目组</span>”中查看</div>
    <div class="text-bottom">10s后自动关闭</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="goCdp">立即跳转</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'
import bomIcon from '@/assets/images/cax/bom-icon.svg'
import EventBus from '@/utils/event-bus'
import { useRouter } from 'vue-router'
import type Node from 'element-plus/es/components/tree/src/model/node'
import { cdp } from '@/service'
const router = useRouter()
interface Tree {
  ncid: string
  name: string
  isSelect?: boolean
  children?: Tree[]
}
const defaultProps = {
  children: 'children',
  label: 'name'
}

const props = defineProps({
  checkVisible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:checkVisible'])
// 检入的协同空间数据
const collaborationData = ref<Tree[]>([])
// 进度条的百分比
const percentage = ref(20)

// 弹窗的显示状态
const checkInVisible = ref(false)
const progressVisible = ref(false)
const failVisible = ref(false)
const successVisible = ref(false)
const confirmDisable = ref(true)

// 选中的节点数据
const selectedNode = ref<Tree | null>(null)

// 进度条的文本内容
const progressText = ref('反写模型属性...')
const textArr = ref([
  '反写模型属性...',
  '生成idi格式文件...',
  '上传文件...',
  '创建/更新数据对象...',
  '更新CADBOM结构'
])

const updateVisible = () => {
  emit('update:checkVisible', false)
  checkInVisible.value = false
  confirmDisable.value = true
}

const handleNodeClick = (data: Tree) => {
  console.log(data)

  /// 第一层不可以点击，第二层可以点击
  if (data.isSelect) {
    confirmDisable.value = false
    selectedNode.value = data
  } else {
    confirmDisable.value = true
  }
}

const confirm = () => {
  // 关闭检入弹窗
  updateVisible()
  // 打开进度条弹窗
  progressVisible.value = true
  const interval = setInterval(() => {
    if (percentage.value < 100) {
      percentage.value += 10
    } else {
      clearInterval(interval)
      progressVisible.value = false
      // 打开成功弹窗
      successVisible.value = true
      closeSuccess()
    }
  }, 1000)
}

// 10s后自动关闭成功弹窗
const closeSuccess = () => {
  setTimeout(() => {
    successVisible.value = false
  }, 10000)
}

// 跳转到cdp
const goCdp = () => {
  successVisible.value = false
  EventBus.emit('goCdp', { cap: 'cax' })
  router.push({
    name: 'cax-CadDetail',
    query: {
      ncid: selectedNode.value?.ncid,
      name: selectedNode.value?.name
      // type: 'space',
    }
  })
}

// 获取项目协作区列表
const getProjectCollaborationList = () => {
  let params: any = {
    fields: ['ncid', 'name'],
    pageParams: {
      limit: 999,
      page: '0'
    }
  }
  cdp
    .postPlt0ProjectList(params)
    .then((res: any) => {
      if (res.data) {
        // 处理数据
        console.log('项目协作区列表:', res.data)
        collaborationData.value = res.data
      } else {
        console.error('没有获取到项目协作区列表')
      }
    })
    .catch((error: any) => {
      console.error('获取项目协作区列表失败:', error)
    })
}

// 懒加载数据
const loadNode = (node: Node, resolve: (data: Tree[]) => void) => {
  if (node.level === 0) {
    const firstLevelData = collaborationData.value.map((item) => {
      return {
        ...item
      }
    })
    // 根节点已加载
    return resolve(firstLevelData)
  }
  if (node.level === 1) {
    // 加载剩余的节点,如果没有则加载空数组
    let pageParams = {
      fields: null,
      expands: null,
      pageParams: {
        limit: 9999,
        page: '1',
        sorts: null
      },
      condition: {
        logic: 'AND',
        ignoreCase: true,
        statements: [
          {
            field: 'projectSpace.ncid',
            value: [node.data.ncid],
            operator: '='
          }
        ]
      }
    }
    cdp
      .postBusinessobjectList(pageParams)
      .then((res: any) => {
        if (res.data) {
          const childrenData = res.data.map((item: any) => {
            return {
              ncid: item.ncid,
              name: item.name,
              isSelect: true // 默认折叠
            }
          })
          resolve(childrenData)
        } else {
          resolve([])
        }
      })
      .catch((error: any) => {
        console.error('加载子节点数据失败:', error)
        resolve([])
      })
  }
  if (node.level > 1) {
    // 如果是第三层及以下，直接返回空数组
    resolve([])
  }
}

watch(
  () => props.checkVisible,
  (newVal) => {
    if (newVal) {
      checkInVisible.value = true
    } else {
      checkInVisible.value = false
    }
  }
)

onMounted(() => {
  getProjectCollaborationList()
})
</script>

<style lang="less">
.check-in-modal {
  padding-left: 0 !important;
  padding-right: 0 !important;
  header {
    padding: 0 28px !important;
    margin-bottom: 20px !important;
  }
  footer {
    padding: 0 28px !important;
    margin-top: 20px !important;
  }
  .el-dialog__body {
    border-top: 1px solid rgba(226, 232, 240, 0.5) !important;
    height: 252px;
    overflow-y: auto;
    .el-tree-node__content {
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);
      height: 42px !important;
      .el-icon {
        margin-left: 20px;
      }
    }
    .el-tree-node:focus > .el-tree-node__content {
      background-color: transparent;
    }
  }
}
</style>

<style lang="less" scoped>
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover {
  background-color: @primary-4 !important;
  border: 0 none !important;
  color: #ffffff !important;
}

.progress-container {
  margin-top: 20px;
  // margin-bottom: 29px;
  width: 100%;
  height: 50px;
  border-radius: 4px;
  background-color: @bg-color-3;
  display: flex;
  align-items: center;
  position: relative;
  .progress-background {
    position: absolute;
    height: 50px;
    top: 0;
    left: 0;
    border-radius: 4px;
    background-color: @link-1;
  }
  .progress-content {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    height: 50px;
    display: flex;
    align-items: center;
    padding-left: 14px;
    font-size: 14px;
    color: #0f172a;
    img {
      margin-right: 14px;
    }
    .right {
      position: absolute;
      right: 14px;
    }
  }
}
.progress-text {
  margin-top: 4px;
  font-size: @text-0;
  color: @text-color-4;
  text-align: left;
}

.text-content {
  margin-top: 20px;
  text-align: left;
  font-size: 14px;
  color: @text-color-2;
  font-weight: 500;
  height: 24px;
  display: flex;
  align-items: center;
  i {
    font-size: 24px;
    color: @danger-6;
    margin-right: 12px;
  }
  span {
    display: inline-block;
  }
}
.success {
  i {
    color: @success-6;
  }
}
.text-bottom {
  text-align: left;
  font-size: 12px;
  color: @text-color-4;
  font-weight: 400;
  height: 18px;
  margin-left: 36px;
  span {
    color: #1856eb;
    font-weight: 600;
  }
}
</style>
