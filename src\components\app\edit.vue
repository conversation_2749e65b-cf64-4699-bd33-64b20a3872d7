<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-23 10:52:07
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-09-20 17:47:16
 * @FilePath: /neue-cloud-vdi/src/components/app/edit.vue
-->

<template>
  <el-dialog
    v-model="props.visible"
    title="编辑"
    :style="{
      width: '640px',
      minHeight: '680px'
    }"
    class="dialog-edit-app"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleFormRef"
        status-icon
        >
        <el-form-item label="应用名称" prop="name" minlength="1" maxlength="62">
          <el-input v-model="ruleForm.name" width="320" />
          <div class="dialog-title-desc">支持 1-64 位字符，必须以字母或中文开头<br />可以包含字母、数字、下划线（_）、中划线（-）、点(.)</div>
        </el-form-item>
        <el-form-item label="应用标签" prop="tag">
          <el-input v-model="ruleForm.tag1" width="320" minlength="0" maxlength="12"/>
          <el-input v-model="ruleForm.tag2" width="320" minlength="0" maxlength="12" class="item-tag"/>
          <div class="dialog-title-desc">
            最多可添加2个标签，每个标签不得超过12个字符
          </div>
        </el-form-item>
        <el-form-item label="应用类型" prop="category">
          <el-select v-model="ruleForm.category" placeholder="请输入">
            <el-option
              v-for="item in appType"
              :key="item.value"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="ruleForm.version" width="320" />
          <div class="dialog-title-desc">不可超过32个字符</div>
        </el-form-item>
        <el-form-item label="应用图标" prop="icon">
          <UploudImage v-model:imgUrl="ruleForm.icon" v-if="uploadImgVisible" @img-changed="appIconChanged"></UploudImage>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="ruleForm.description" type="textarea" width="320" :rows="3" show-word-limit minlength="0" maxlength="255" />
        </el-form-item>
      </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave(ruleFormRef)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import UploudImage from '@/components/upload/upload-img.vue'
import { AppDetail } from '@/common/types/console/app'
import { appType } from '@/common/filters/app'
import apps from '@/service/apps'

const router = useRouter()

const emit = defineEmits(['update:visible', 'handleEditComplete'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  appDetail: {
    type: Object,
    default: () => ({})
  }
})


const iconObj = ref<any>(null)
const uploadImgVisible = ref(false)

const ruleForm: AppDetail = reactive({
  name: '',
  tag1: '',
  tag2: '',
  tag: '',
  category: '',
  env: '',
  version: '',
  installParams: '',
  filePath: '',
  icon: '',
  description: '',
})
const validateNameStr = (rule: any, value: any, callback: any) => {
  const regex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9_\-.\u4e00-\u9fa5]{0,62}$/;
  if (value === '') {
    callback(new Error('请输入应用名称'))
  } else if (!regex.test(value)) {
    callback(new Error('请输入正确格式的名称'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  name: [
    { required: true,  trigger: 'blur', validator: validateNameStr },
  ],
  category: [
    { required: true, trigger: 'blur', message: '请选择应用类型', }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'change' }
  ],
  installParams: [
    { required: true, message: '请输入可执行文件相对路径', trigger: 'blur' }
  ],
})

const ruleFormRef = ref<FormInstance>()

const appIconChanged = (icon: any) => {
  console.log('icon==>', icon)
  iconObj.value = icon
}

const handleClose  = () => {
  emit('update:visible', false)
}

const handleSave = async (formEl: FormInstance | undefined) => {
  if (!formEl) {
    return
  }
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      console.log('submitForm==>提交', ruleForm)
      submitForm()
    } else {
      console.log('error submit!', fields)
    }
  })
}

const getTag = () => {
  const tag1 = (ruleForm.tag1)?.trim()
  const tag2 = (ruleForm.tag2)?.trim()
  return (tag1 ? tag1 : '') + (tag1 && tag2 ? ',' + tag2 : '')
}

const submitForm = () => {
  if (!iconObj.value) {
     const params = {
      ...ruleForm,
      tag: getTag(),
      icon: ''
    }
    delete params.tag1
    delete params.tag2
    apps.putAppsId(params).then((res: any) => {
      emit('handleEditComplete')
      handleClose()
    })
  } else {
    if (typeof iconObj.value === 'string') {
      const params = {
        ...ruleForm,
        tag: getTag(),
        icon: iconObj.value
      }
      submitUpdateApp(params)
      return
    }
    const reader = new FileReader()
    reader.readAsDataURL(iconObj.value)
    reader.onload = (e: any) => {
      const params = {
        ...ruleForm,
        tag: getTag(),
        icon: reader.result
      }

      submitUpdateApp(params)
    }
  }
}

const submitUpdateApp = (params: any) => {
  delete params.tag1
  delete params.tag2
  apps.putAppsId(params).then((res: any) => {
    emit('handleEditComplete')
    handleClose()
  })
}

const formatTag = (tag: any) => {
  const arr = tag.split(',')
  return arr
}

watch (
  () => props.visible,
  (val: boolean) => {
    if (val) {
      iconObj.value = null
      Object.assign(ruleForm, props.appDetail)
      const arr = formatTag(ruleForm.tag)
      ruleForm.tag1 = arr.length ? arr[0] : ''
      ruleForm.tag2 = arr.length > 1 ? arr[1] : ''
      iconObj.value = ruleForm.icon
      console.log('ruleForm==>', ruleForm, iconObj.value)
    } else {
      Object.assign(ruleForm, {
        name: '',
        tag1: '',
        tag2: '',
        tag: '',
        category: '',
        env: '',
        version: '',
        installParams: '',
        filePath: '',
        icon: '',
        description: '',
      })
      iconObj.value = null
    }
    uploadImgVisible.value = props.visible
  }
)

</script>

<style scoped lang="less">
.dialog-edit-app {
  height: calc(100%);
  display: flex;
  flex-direction: column;
  .container-title {
    height: 32px;
    text-align: left;
    color: @text-color-1;
    font-size: @text-3;
    font-weight: @font-weight-3;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
  }
  .container-content {
    width: 784px;
    margin: 0 auto;

    .el-scrollbar__view {
      height: 100% !important;
    }
  }
  .container-footer {
    display: flex;
    height: 72px;
    justify-content: space-between;
    align-items: center;
    border-top: @border-color-2 solid 1px;
    .control-footer-left {
      width: 220px;
    }
    .control-btns {
      flex: 1;
    }
  }
}
</style>
<style lang="less">
.dialog-edit-app {
  .el-form-item {
    flex: 1;
    flex-direction: row;
  }
  .el-form-item__label {
    display: block !important;
    width: 116px;
    text-align: right;
  }
  .el-form-item__content {
    flex: 1;
  }
  .el-input,
  .el-select {
    width: 320px;
  }
  .el-textarea {
    width: 320px;
  }
  .item-tag {
    margin-top: 8px;
  }
  .dialog-title-desc {
    margin-top: 4px;
    width: 320px;
    color: @text-color-5;
    font-size: @text-0;
    line-height: 18px;
  }
}
</style>
