<!--
 * <AUTHOR>
 * @description: 首页
 * @date: 2022-08-03 15:08
 * @lastEditors: wangyan-judy
 * @lastEditTime: 2022-08-03 15:08
 * @FilePath: /neue-cloud-vdi/src/views/home/<USER>
-->
<template>
  <div class="home-container page-container">
    <el-container>
      <el-main>
        <div class="content">
          <el-header class="page-content-header">
            <span class="content-title">{{ pageTitle }}</span>
            <slot name="header-slot"></slot>
          </el-header>
          <slot name="content"></slot>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router'
const props = defineProps({
  title: {
    type: String,
    default: '主页1'
  }
})

const pageTitle = computed(() => {
  return props.title || '主页'
});
</script>

<style scoped lang="less">
.el-container {
  height: 100%;
  padding-right: 20px;
}

.el-main {
  padding: 0;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .content {
    padding: 24px;
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: @border-radius-3;
    background: @bg-color-white;
  }
  .page-content-header {
    display: flex;
    justify-content: space-between;
  }
  .user-data {
    margin-top: 20px;
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: row;
    .user-data-item {
      flex: 1;
      border-radius: @border-radius-3;
      background: @bg-color-white;
      display: flex;
      flex-direction: column;
      .item-title {
        text-align: left;
        font-size: @text-1;
        font-weight: @font-weight-2;
        line-height: 22px;
        padding: 12px 24px;
      }
    }
  }

  .el-header {
    padding-left: 8px;
    // margin-top: 24px;
    text-align: left;
    font-size: @text-4;
    font-weight: bold;
    height: 40px;
  }
  .el-tabs__nav-wrap {
    &::after {
      height: 1px;
    }
  }
  .el-tabs__content {
    flex: 1;
    width: 100%;
  }
}
</style>
<style lang="less">
.page-container {
  .demo-tabs {
    .el-tabs__content {
      flex: 1 !important;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
/* 当宽度大于等于 1600px 时，调整为四列布局 */
@media (min-width: 1680px) {
  .home-app-list ul {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 16px;
  }
}
@media (max-width: 1679px) {
  .home-app-list ul {
    display: grid;
    column-gap: 16px;
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
