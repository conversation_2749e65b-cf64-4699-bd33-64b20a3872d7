import { defineStore } from 'pinia'
import { CollaborationZone } from '@/common/types/space'

type ProjectInfoState = {
  activeProject: CollaborationZone
  projectList: Array<CollaborationZone>
  projectSpaceList: Array<CollaborationZone>
}

const state: ProjectInfoState = {
  activeProject: {},
  projectList: [],
  projectSpaceList: []
}

export const useProjectInfo = defineStore('projectInfo', {
  state: () => state,
  getters: {
    getActiveProject: (state) => state.activeProject,
    getProjectList: (state) => state.projectList,
    getProjectSpaceList: (state) => state.projectSpaceList
  },
  actions: {
    setActiveProject(activeProject: CollaborationZone) {
      this.activeProject = activeProject
    },
    setProjectList(projectList: Array<CollaborationZone>) {
      console.log('setProjectList', projectList)
      this.projectList = projectList
    },
    setProjectSpaceList(projectSpaceList: Array<CollaborationZone>) {
      this.projectSpaceList = projectSpaceList
    }
  }
})