/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-08-22 09:43:44
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-09-18 11:37:20
 * @FilePath: /neue-cloud-vdi/src/common/filters/app.ts
 */
import { SelectItem } from '@/common/types/common'

// 应用类型
export const appType: Array<SelectItem> = [
  { key: '设计', value: '设计' },
  { key: '办公', value: '办公' },
  { key: 'CAX', value: 'CAX' }
  // { key: '设计', value: '设计' },
  // { key: '仿真', value: '仿真' },
  // { key: '制造', value: '制造' },
  // { key: '产品管理', value: '产品管理' },
  // { key: '其他', value: '其他' }
]
// 应用验证状态
export const appStatuList: Array<SelectItem> = [
  { key: '3', value: '待验证', icon: 'icon-tips-clock' },
  { key: '4', value: '验证中', icon: 'icon-tips-ing' },
  { key: '5', value: '验证失败', icon: 'icon-tips-fail' },
  { key: '6', value: '验证成功', icon: 'icon-tips-success' }
]

// 应用运行环境
export const appEnv: Array<SelectItem> = [
  { key: 'windows', value: 'Windows', icon: 'icon-windows' },
  { key: 'linux', value: 'Linux', icon: 'icon-linux' }
]

// 应用使用状态
export const appBindStatus: Array<SelectItem> = [
  { key: '0', value: '未定义' }, // 试用应用名称查询【版本信息】时，获取不到此状态
  { key: '1', value: '未使用' },
  { key: '2', value: '使用中' }
]
