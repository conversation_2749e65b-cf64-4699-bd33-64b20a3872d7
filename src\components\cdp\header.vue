<template>
  <div class="header-container">
    <div class="header-left">
      <panel />
      <img src="@/assets/images/logo-banner.png" width="62" class="header-logo" @click="handleHome" height="24"
        alt="" />
      <div class="header-toobar" v-if="pageInfo.title.length > 0">
        <i class="iconfont icon-back" @click="handleBack"></i>
        <div class="header-page-view">
          <template v-for="(title, index) in pageInfo.title" :key="index">
            <span>{{ title }}</span>
            <span v-if="index < pageInfo.title.length - 1" class="page-view-split">|</span>
          </template>
          <el-tag v-if="pageInfo.tag && (pageInfo.tag).trim().length > 0">{{ pageInfo.tag }}</el-tag>
        </div>
        <i class="iconfont icon-setting" @click="handleSetting"></i>
      </div>
    </div>
    <div class="header-right">
      <el-dropdown @command="handleCommand" popper-class="head-dropdown" class="avatar-container">
        <el-avatar class="avatar-dropdown">
          <i class="iconfont icon-general-user"></i>
        </el-avatar>
        <template #dropdown>
          <div class="user-info">
            <div class="user-tag">用户名</div>
            <div class="user-name">{{ userName || '--' }}</div>
          </div>
          <el-dropdown-menu>
            <el-dropdown-item disabled>
              <i class="iconfont icon-settings"></i>
              设置
            </el-dropdown-item>
            <el-dropdown-item @click="doLogout" v-if="!isCax"> <i class="iconfont icon-exit"></i>退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <ny-dialog v-model="settingVisible" size="80%" class="!p-0" header-class="hidden" footer-class="hidden">
      <space-setting @onClose="settingVisible = false"></space-setting>
    </ny-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import panel from '@/components/cdp/panel.vue'
import cookie from 'cookiejs'
import { useUserInfoStore } from '@/store/user/userInfo'
import { usePagesStore } from '@/store/common/usePagesStore'
import storage from '@/utils/storage'
import spaceSetting from '@/views/space-setting/index.vue'

const router = useRouter()
const route = useRoute()
const userInfoStore = useUserInfoStore()
const pagesStore = usePagesStore()
let settingVisible = ref(false)
const isCax = computed(() => {
  return route.path.includes('/cax')
})

const userName = computed(() => {
  console.log('userInfoStore.access.user', userInfoStore.userInfo)
  return userInfoStore.userInfo?.name || '--'
})
const pageInfo = computed(() => {
  return pagesStore.headerToobar
})

const pageFrom = computed(() => {
  return pagesStore.pathFrom
})


const handleHome = () => {
  router.push('/home')
}

const handleBack = () => {
  if(route.name === 'cax-CadDetail'){
    router.push('/home')
  }else{
  router.back()
  }

}

const handleCommand = () => {

}
let handleSetting = () => {
  settingVisible.value = true
}

const doLogout = () => {
  cookie.set('token', '', 24 * 60 * 60)
  storage.set('token', '')
  storage.set('storeid', '')
  storage.set('role', '')
  storage.set('userVid', '')
  router.push('/login')
}

</script>

<style scoped lang="less">
.header-container {
  width: 100%;
  height: 68px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  box-sizing: border-box;

  .header-left {
    display: flex;
    align-items: left;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-interactive-button-wait {
      width: 28px;
      height: 28px;
      font-size: 24px;
      color: @text-color-4;
    }

    .header-logo {
      cursor: pointer;
    }

    .header-hr {
      width: 1px;
      height: 18px;
      background: @border-color-1;
      margin: 0 10px;
    }

    .header-tag {
      font-size: @text-1;
      font-weight: @font-weight-1;
      color: @text-color-2;
    }

    .header-toobar {
      display: flex;
      align-items: center;
      margin-left: 8px;
      font-size: @text-1;
      color: @text-color-2;
      cursor: pointer;

      .header-page-view {
        font-size: @text-3;
        font-weight: @font-weight-1;
        color: @text-color-2;
        margin-left: 8px;
        margin-right: 8px;

        span {
          font-weight: @font-weight-2;
        }

        .page-view-split {
          margin: 0 4px;
          color: @text-color-2;
          font-size: @text-2;
        }
        .el-tag { 
          margin-left: 8px;
          font-size: @text-1;
          color: @text-color-2;
        }
      }

      .icon-back {
        width: 24px;
        height: 24px;
        background: @primary-2;
        border-radius: @border-radius-2;
        text-align: center;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: right;

    .swap-logo {
      cursor: pointer;
      width: 32px;
      height: 32px;
      line-height: 32px;
      padding-left: 8px;
      color: @text-color-3;
      border-radius: 50%;
      margin-right: 20px;
      border: 1px solid @border-color-1;
    }

    .console-btn {
      margin-right: 24px;
      font-weight: @font-weight-1;
      color: @text-color-2 !important;

      &:hover {
        color: @link-6 !important;
      }
    }

    .avatar-container {
      border: none !important;

      &:hover,
      &:active {
        border: none !important;
      }
    }

    .avatar-dropdown {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background: #383b46;
      color: @link-3;
      border: none !important;

      &:hover,
      &:active {
        border: none !important;
      }
    }

    .el-button--primary.is-link {
      color: @text-color-2;

      &:hover {
        color: @link-6;
      }
    }

    .swap-logo {
      &:hover {
        background: #CCD5EA;
        border-color: #CCD5EA;
        color: #334155;
      }
    }
  }

  .line {
    margin: 0 10px;
    display: inline-block;
  }
}
</style>
<style lang="less">
.el-tooltip__trigger,
.el-tooltip__trigger:hover,
.el-tooltip__trigger:focus {
  outline: none !important;
}

.head-dropdown {
  width: 160px !important;
  padding-bottom: 4px !important;

  .el-scrollbar {
    .user-info {
      font-size: @text-1;
      color: @text-color-2;
      padding: 14px 16px 8px 16px;
      line-height: 20px;

      .user-tag,
      .user-name {
        height: 18px;
        line-height: 18px;
      }

      .user-name {
        font-weight: @font-weight-3;
        border-bottom: solid 1px @border-color-1;
        padding-bottom: 12px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .iconfont {
      width: 14px;
      font-size: 14px;
      margin-right: 10px;
    }

    .el-scrollbar__wrap {
      width: 160px;
    }
  }
}
</style>
