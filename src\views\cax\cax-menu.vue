<template>
  <!-- <div class="logo">
    <img :src="CDP" alt="logo" />
  </div> -->
    <el-menu
      class="el-menu-vertical"
      :default-active="String(activeMenu?.index)" 
    >
      <el-menu-item
        v-for="(menu, index) in menus"
        :key="index"
        :index="String(index)"
        @click="handleOpen(menu)"
      >
        <template #default>
          <div>
            <div class="cax-menu-icon">
              <img v-if="activeMenu.name===menu.name" :src="menu.iconActive" alt="">
              <img v-else :src="menu.icon" alt="">
            </div>
            <div class="cax-menu-name">{{ menu.name }}</div>
          </div>
        </template>
      </el-menu-item>
    </el-menu>
    <div class='logout' @click="logoutVisible=true"> <i class="iconfont icon-exit"></i></div>
    <el-dialog
    v-model="logoutVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="注销"
    width="520"
  >
    <div class="logout-dialog"><i class="iconfont icon-exclamation-circle-fill"></i>确定要注销窗口</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="logoutVisible = false">取消</el-button>
        <el-button type="primary" @click="doLogout">确定</el-button>
      </div>
    </template>
  </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { ref, onMounted, h} from 'vue'
  import { ElMessage } from 'element-plus'
  import { useRoute, useRouter } from 'vue-router'
  import CDP from '@/assets/images/cdp.png'
  import modelFile from '@/assets/images/cax/model-file.svg'
  import modelFileActive from '@/assets/images/cax/model-file-active.svg'
  import cdpLogo from '@/assets/images/cax/cdp-logo.svg'
  import cdpLogoActive from '@/assets/images/cax/cdp-logo-active.svg'
  import cookie from 'cookiejs'
  import storage from '@/utils/storage'
  import EventBus from '@/utils/event-bus'
   import { socket} from './websocket/sockets';
  const router = useRouter()
  const route = useRoute()
  // 激活菜单
  const activeMenu:any = ref({
    index: 0,
    name: 'Model File',
  })
  console.log('route', route);
  console.log('router', router);
  
  const logoutVisible = ref(false)

  interface Menus {
    index?: number
    path: string
    highlight?: Array<string>
    name: string
    icon?: string
    iconActive?: string
    isActive: boolean
    childActive?: boolean
    childActiveId?: string
    parent?: string
    
  }
  const menus:Menus[] = [
    {
      index: 0,
      name: 'Model File',
      path: '/cax/cax-model-file',
      icon: modelFile,
      iconActive: modelFileActive,
      isActive: true,
    },
    {
      index: 1,
      name: '协同设计',
      path: '/cax/cax-cdp',
      icon: cdpLogo,
      iconActive: cdpLogoActive,
      isActive: false,
    }
  ]

  const doLogout = () => {
  // deleteCookie('token')
  // setCookie('token', '', 24 * 60 * 60)
  // cookie('token', '', 24 * 60 * 60)
  cookie.set('token', '', 24 * 60 * 60)
  storage.set('token', '')
  storage.set('uid', '')
  socket.close()
  ElMessage({
    message: '注销成功',
    type: 'success',
    plain: false,
  })
  router.push(
    {
      name: 'Login',
      query: {
        redirect: route.path,
      },
    }
  )
}

  const handleOpen = (item: any) => {
    if(activeMenu.value.name === item.name) return
    router.push(item.path)
    activeMenu.value = item
  }

  EventBus.on('goCdp', (params: any) => {
    console.log('goCdp', params);
    // let menu = {
    //   index: 1,
    //   name: '协同设计',
    //   path: 'cax-cdp',
    //   isActive: false,
    // }
    // handleOpen(menu)
    activeMenu.value = menus[1]
})



  // 初始化菜单
  onMounted(() => {
    let path = route.path
    activeMenu.value = menus.find((item) => item.path === path) || menus[1]

  })
  </script>
  
  <style  scoped lang="less">

  .logo {
    width: 100%;
    margin-bottom: 50px;
    display: flex;
    justify-content: center;
    img {
      width: 48px;
      height: 22px;
      object-fit: contain;
    }
  }

  .el-menu-vertical {
    //水平居中
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .el-menu-item {
      font-size: 12px;
      padding-left: 0 !important;
      height: 60px !important;
      width: 60px !important;
      margin-bottom: 12PX;
      border-radius: 8px;
      .cax-menu-icon {
        width: 60px;
        height: 38px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .cax-menu-name {
        font-size: 11px;
        color: #1e1d1d;
        height: 22px;
        line-height: 22px;
        text-align: center;
      }
    }
    .el-menu-item.is-active {
    color: #262A33;
    font-weight: 500;
    background-color: @link-2;
  }
    .el-menu-item:hover {
      background-color: @link-1;
    }
  }
.logout {
  width: 100%;
  position: absolute;
  bottom: 26px;
  display: flex;
  i{
    font-size: 20px;
    color: #262A33;
    cursor: pointer;
    position: relative;
    left: 40%;
  }
}
.logout-dialog {
  margin-top: 20px;
  margin-bottom: 29px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: @text-2;
  i{
    font-size: 24px;
    color: @link-6;
    margin-right: 14px;
  }
}
  </style>