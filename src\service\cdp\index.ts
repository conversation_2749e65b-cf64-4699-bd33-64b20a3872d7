import http from '@/service/http'
import url from './index-url'
import { CollaborationZone } from '@/common/types/space'

const { get, post, put, deleteReq } = http

export default {
  postOperationsAccess() {
    return get(url.operationsAccess)
  },
  postBusinessobjectCreate(params: CollaborationZone) {
    return post(url.businessobjectCreate, params)
  },
  putBusinessobjectId(params: CollaborationZone) {
    return put(url.businessobjectId(params.ncid), params)
  },
  deleteBusinessobjectId(params: CollaborationZone) {
    return deleteReq(url.businessobjectId(params.ncid), params)
  },
  getBusinessobjectId(params: any) {
    return get(url.businessobjectId(params.projectSpaceId), params)
  },
  postBusinessobjectList(params: any) {
    return post(url.businessobjectList, params)
  },
  // 创建项目协作区
   postPlt0ProjectCreate(params: CollaborationZone) {
      return post(url.plt0ProjectCreate, params)
    },
    // 修改项目协作区
    putPlt0Project(params: CollaborationZone) {
      return put(url.plt0Project(params.ncid), params)
    },
    // 删除项目协作区
    deletePlt0Project(params: CollaborationZone) {
      return deleteReq(url.plt0ProjectId(params.ncid), params)
    },
    // 获取项目协作区详情
    getPlt0Project(params: CollaborationZone) {
      return get(url.plt0ProjectId(params.ncid), params)
    },
    postPlt0ProjectList(params: CollaborationZone) {
      return post(url.plt0ProjectList, params)
    },
      // 获取bom树
  getDesignArtifact(id: string) {
    return get(url.getDesignArtifact(id))
  },
  postActionsList(className: string, data: any) {
    return post(url.getActionsList(className), data)
  },
  postGetBomTree(params: any) {
    return post(url.getBomTree(params.model, params.ncid), {})
  },
}