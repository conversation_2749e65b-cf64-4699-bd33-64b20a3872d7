<template>
    <div :class="['cax-tag', `cax-tag-${type}`]">
        {{ content }}
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
    type: {
        type: String,
        default: 'default'
    },
    content: {
        type: String,
        default: ''
    },
})

</script>

<style lang="less" scoped>
.cax-tag {
    display: inline-block;
    padding: 1px 8px;
    border-radius: 4px;
    font-size: 12px;
    height: 22px;
    line-height: 20px;

}
.cax-tag-WORKING {
        background-color: @link-1;
        border: 1px solid;
        border-color: @link-4;
        color: @link-6;
    }
.cax-tag-PUBLISHED {
        background-color: @success-1;
        border: 1px solid;
        border-color: @success-4;
        color: @success-6;
    }
.cax-tag-PUBLISHING {
        background: rgba(0, 0, 0, 0.04);
        border: 1px solid;
        border-color:rgba(0, 0, 0, 0.25);
        color: rgba(0, 0, 0, 0.65);
    }
.cax-tag-4 {
        background-color: @danger-1;
        border: 1px solid;
        border-color: @danger-4;
        color: @danger-6;
    }
</style>