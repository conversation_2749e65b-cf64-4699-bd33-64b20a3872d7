<!--
 * @Author: fangshijie <EMAIL>
 * @Date: 2024-12-11 14:53:08
 * @LastEditors: fangshijie <EMAIL>
 * @LastEditTime: 2025-02-13 16:26:16
 * @FilePath: /neue-cloud-vdi/src/component-design/ny-input/index.vue
-->
<template>
  <el-input class="ny-input" v-bind="props" v-model="inputValue">
    <template v-for="(_, name) in $slots" v-slot:[name]>
       <slot :name="name"></slot>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
// import type { InputProps } from 'element-plus';
import  NyInputProps  from '.';

const emit = defineEmits(['update:modelValue'])

const props = defineProps<NyInputProps>()

const inputValue = computed({
  get: () => props.modelValue,
  set: (val: string | number) => {
    emit('update:modelValue', val)
  }
})
</script>

<style lang="less" scoped>
.ny-input {

}

</style>
