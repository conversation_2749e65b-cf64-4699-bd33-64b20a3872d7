<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-17 13:44:19
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-21 17:06:04
 * @FilePath: /neue-cloud-vdi/src/views/console/users/index.vue
-->
<template>
  <el-main class="main-content">
    <div
      class="users-container page-container"
      style="display: flex; flex-direction: column"
    >
      <div class="container-title">
        <span>用户</span>
        <el-button
          id="createUser"
          type="primary"
          style="margin-left: 10px"
          @click="handleCreate"
        >
          创建用户
        </el-button>
      </div>
      <div class="" style="max-height: calc(100% - 140px); overflow: hidden">
        <el-table
          id="userList"
          ref="userListRef"
          :data="userList"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
          :row-key="getRowKeys"
          class="user-list"
          style="overflow: auto; width: 100%; max-height: 100%; overflow: auto"
        >
          <el-table-column
            type="selection"
            width="55"
            :reserve-selection="true"
            :selectable="(row: any) => row.role !== 'super' && row.role !== 'admin'"
          />
          <el-table-column property="user" label="用户名称"></el-table-column>
          <el-table-column property="email" label="邮箱"></el-table-column>
          <el-table-column property="phone" label="电话"></el-table-column>
          <!-- <el-table-column property="role" label="角色">
            <template #default="scope">
              <div style="display: flex; align-items: center">
                <span>{{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}</span>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column property="status" label="已分配资源">
            <template #default="scope">
              <div>
                <span v-if="formatInstanceList(scope.row.existVdis).total === 0">{{
                  formatInstanceList(scope.row.existVdis).total
                }}</span>
                <el-popover
                  v-else
                  ref="popover"
                  placement="right"
                  title=""
                  :width="200"
                  trigger="hover"
                >
                  <div class="userinstance-popover-content">
                    <ul>
                      <li
                        v-for="(item, index) in formatInstanceList(scope.row.existVdis)
                          .list"
                        :key="index"
                      >
                        <span>{{ item.vdiName }}</span>
                        <span>{{ item.vdiId }}</span>
                      </li>
                    </ul>
                  </div>
                  <template #reference>
                    <el-button type="primary" class="console-btn" link>
                      {{ formatInstanceList(scope.row.existVdis).total }}
                    </el-button>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column property="extras" label="备注">
            <template #default="scope">
              <template v-if="!scope.row.extra">
                <div style="display: flex; align-items: center">
                  <span>--</span>
                </div>
              </template>
              <template v-else>
                <el-tooltip class="custom-tooltip">
                  <template #default>
                    <div style="display: flex; align-items: center">
                      <span>{{ scope.row.extra }}</span>
                    </div>
                  </template>
                  <template #content>
                    <div style="max-width: 300px">
                      <span>{{ scope.row.extra }}</span>
                    </div>
                  </template>
                </el-tooltip>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240">
            <template #default="scope">
              <el-button
                type="primary"
                class="console-btn console-btn-manage"
                link
                @click.native.stop="handleBindResource(scope.row)"
              >
                管理资源
              </el-button>
              <el-button
                type="primary"
                class="console-btn console-btn-reset"
                link
                @click.native.stop="handleResetPwd(scope.row)"
              >
                重置密码
              </el-button>
              <el-button
                type="primary"
                class="console-btn console-btn-edit"
                link
                @click.native.stop="handleEditUser(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                class="console-btn console-btn-delete"
                link
                @click.native.stop="handleDelUser(scope.row)"
                :disabled="scope.row.role === 'super' || scope.row.role === 'admin'"
              >
                删除
              </el-button>
              <!-- <el-button v-if="scope.row.vid && scope.row.vid.length > 0" type="primary" class="console-btn" link @click.native.stop="handleUnBindResource(scope.row)">
                解绑
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="container-footer">
        <el-button
          id="deleteUserBatch"
          type="primary"
          plain
          @click="handleBatchDelete"
          :disabled="batchDeleteDisabled"
          >删除</el-button
        >
        <!-- <div class="pagination-container">
          <el-config-provider>
            <el-pagination
              id="pagination"
              class="pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :hide-on-single-page="false"
              :current-page="currentPage"
              :page-sizes="[5, 10, 20, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="userTotal">
            </el-pagination>
          </el-config-provider>
        </div> -->
      </div>
      <createUser
        v-model:visible="userDialogVisible"
        :openType="openType"
        :userDetail="editUserDetail"
        @handleAddComplete="handleAddComplete"
        @handleEditComplete="handleAddComplete"
      ></createUser>
      <deleteUsers
        ref="deleteDialogRef"
        v-model:visible="delUserDialogVisible"
        :users="userSelectionList"
        @handleDelete="handleBatchDelUser"
      ></deleteUsers>
      <resetPwd
        ref="resetPwdRef"
        v-model:visible="resetPwdDialogVisible"
        @handleSaveComplete="handleComplete"
      ></resetPwd>
      <instanceManage
        ref="instanceManageRef"
        v-model:visible="instanceManageDialogVisible"
        @handleComplete="handleComplete"
      ></instanceManage>
    </div>
  </el-main>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
// 更换为npm源重新安装，升级为最新的ElementPlus版本
// import zhCn from 'element-plus/lib/locale/lang/zh-cn'
import createUser from '@/components/user/create.vue'
import deleteUsers from '@/components/user/delete.vue'
import { UserDetail, UserSelectionsAll } from '@/common/types/console/user'
import { account as accountService } from '@/service'
import { ElMessage, ElTable } from 'element-plus';
import resetPwd from '@/components/user/reset-pwd.vue'
import instanceManage from '@/components/user/instance.vue'
import { formatInstanceList } from '@/utils/index'


const userList = ref<Array<UserDetail>>([])
const userTotal = ref(0)

const userSelection = ref<Array<UserDetail>>([])
const selectedRows = ref<Array<UserSelectionsAll>>([])
const userSelectionList = ref<Array<UserDetail>>([])
const refresh = ref(false)
const openType = ref(0)
const editUserDetail = ref<UserDetail>({} as UserDetail)
const userDialogVisible = ref(false)
const delUserDialogVisible = ref(false)
const userItemDelete = ref(false)
const pageSize = ref(9999)
const currentPage = ref(0)
const deleteDialogRef = ref(null)
const userListRef = ref<InstanceType<typeof ElTable> | null>(null)
const resetPwdDialogVisible = ref(false)
const resetPwdRef = ref<InstanceType<typeof resetPwd> | null>(null)
const instanceManageDialogVisible = ref(false)
const instanceManageRef = ref<InstanceType<typeof instanceManage> | null>(null)

const batchDeleteDisabled = computed(() => {
  return userSelectionList.value.length === 0 && userSelection.value.length === 0
})

const handleRowClick = (row: any, column: any, event: any) => {
  console.log('handleRowClick', row, row.role === 'super')
  if (row.role === 'super' || row.role === 'admin') {
    return
  }
  if (userListRef && userListRef.value) {
    userListRef.value.toggleRowSelection(row, undefined as any)
  }
}

const handleSelectionChange = (selection: UserDetail[]) => {
  userSelection.value = selection
  userSelectionList.value = selection
}

const getRowKeys = (row: any) => {
  return row.userId
}

const handleCreate = () => {
  // refresh.value = true
  openType.value = 0
  userDialogVisible.value = true
}

const handleResetPwd = (row: UserDetail) => {
  resetPwdRef.value?.show(row)
}

const handleEditUser = (row: UserDetail) => {
  refresh.value = true
  editUserDetail.value = {...row}
  openType.value = 1
  userDialogVisible.value = true
}

const handleAddComplete = () => {
  refresh.value = true
  userDialogVisible.value = false
  getUserList()
}

// 点击批量删除按钮
const handleBatchDelete = () => {
  console.log('handleBatchDelete', userSelection.value, userSelectionList.value)
  deleteDialogRef && deleteDialogRef.value?.show({ users: userSelection.value })
}

const batchDeleteUsers = (userIds: Array<number | any>) => {
  accountService.postOperationsBatchDelete({ userIds }).then((res) => {
    ElMessage.success('删除成功')
    delUserDialogVisible.value = false
    const list = userSelection.value.filter((user) => {
      return !userIds.includes(Number(user.userId))
    })
    userSelectionList.value = list
    userSelection.value = list
    console.log('batchDeleteUsers===', list)
    if (!userItemDelete.value && userListRef && userListRef.value) {
      userListRef.value.clearSelection()
    }
    if (userSelection.value.length === 0) {
      userListRef.value?.clearSelection()
    }
    userItemDelete.value = false
    console.log('batchDeleteUsers===', list)
    getUserList()
  })

}
// 删除
const handleDelUser = (row: UserDetail) => {
  userItemDelete.value = true
  deleteDialogRef && deleteDialogRef.value?.show({ users: [row] })
  console.log('handleDelUser', row)
}
// 批量删除
const handleBatchDelUser = (userIds: Array<UserDetail['userId']>) => {
  batchDeleteUsers(userIds)
}

const handleComplete = () => {
  getUserList()
}


const handleBindResource = (row: UserDetail) => {
  console.log('handleBindResource', row)
  instanceManageRef.value?.show(row)
}

const handleSizeChange = (val: number) => {
  console.log(`每页 ${val} 条`);
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  console.log(`当前页: ${val}`);
}

const getUserList = () => {
  accountService.postOperationsList({
    pageParams: {
      limit: pageSize.value
    }
  }).then((res: any) => {
    console.log('getUserList', res)
    if (res && res.data) {
      console.log('getUserList-res', res)
      userTotal.value = res.data.length
      userList.value = res.data
    }
  })
}

watch([
  currentPage,
  pageSize
], (val) => {
  refresh.value = false
  getUserList()
})


getUserList()
onMounted(() => {
  // console.log('zhCn', zhCn)
})
</script>

<style scoped lang="less">
.container-title {
  height: 32px;
  text-align: left;
  color: @text-color-1;
  font-size: @text-3;
  font-weight: @font-weight-3;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}
.container-footer {
  display: flex;
  height: 52px;
  justify-content: space-between;
  align-items: center;
}
.user-list {
  .el-tooltip__trigger {
    max-height: 40px;
    line-height: 20px;
    display: -webkit-box !important;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: normal;
  }
  .el-tooltip__popper {
    max-width: 200px;
  }
}
.popover-content {
  li {
    display: flex;
    flex-direction: column;
    line-height: 18px;
    &:nth-child(n + 1) {
      // margin-top: 4px;
    }
  }
  li:nth-child(n + 1) {
    margin-top: 4px;
  }
}
</style>
