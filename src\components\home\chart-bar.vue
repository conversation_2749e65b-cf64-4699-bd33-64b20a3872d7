<!--
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-10-25 11:43:51
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-10-25 14:49:18
 * @FilePath: /neue-cloud-vdi/src/components/home/<USER>
-->
<template>
  <div ref="chartRef" class="chart-container"></div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import chartBar from './chart-bar'
const chartRef = ref<HTMLElement | null>(null)

const initChart = () => {
  if (chartRef.value) {
    const chart = echarts.init(chartRef.value)
    chart.setOption(chartBar.barOption)
    console.log('initChart', chart, chartBar.barOption)
  }
}

onMounted(() => {
  initChart()
})

</script>
<style lang="less" scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 130px;
}
</style>
