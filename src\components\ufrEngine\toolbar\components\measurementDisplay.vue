<template>
  <div class="display-setting">
    <div v-for="group in groups" :key="group.name" class="group">
      <div class="label">{{ group.label }}</div>
      <el-checkbox-group v-model="group.checkedItems" class="checkbox-group">
        <el-checkbox
          v-for="item in group.items"
          :key="item.value"
          :label="item.value"
          class="checkbox-item"
					@change="handleCheckboxChange(item.value, group.name)"
        >
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from 'vue'

// 使用 KV 结构定义 items
const groups = reactive([
  {
    name: 'point',
    label: '点',
    items: [
      { value: 'desc', label: '描述' },
      { value: 'coord', label: '坐标' }
    ],
    checkedItems: ['desc', 'coord']
  },
  {
    name: 'line',
    label: '边线',
    items: [
      { value: 'desc', label: '描述' },
      { value: 'length', label: '长度' },
      { value: 'obj1', label: '对象 1' },
      { value: 'obj2', label: '对象 2' },
      { value: 'direction', label: '方向向量' }
    ],
    checkedItems: ['desc', 'length']
  },
  {
    name: 'arc',
    label: '弧',
    items: [
      { value: 'desc', label: '描述' },
      { value: 'length', label: '长度' },
      { value: 'angle', label: '角度' },
      { value: 'radius', label: '半径' },
      { value: 'diameter', label: '直径' },
      { value: 'obj1', label: '对象 1' },
      { value: 'obj2', label: '对象 2' },
      { value: 'center', label: '中心点' }
    ],
    checkedItems: ['desc', 'length']
  },
  {
    name: 'surface',
    label: '曲面',
    items: [
      { value: 'desc', label: '描述' },
      { value: 'area', label: '区域' },
      { value: 'centroid', label: '重心' },
      { value: 'plane', label: '平面' },
      { value: 'perimeter', label: '周长' }
    ],
    checkedItems: ['desc', 'area']
  },
  {
    name: 'volume',
    label: '体积',
    items: [
      { value: 'desc', label: '描述' },
      { value: 'volume', label: '体积' },
      { value: 'area', label: '区域' },
      { value: 'centroid', label: '重心' }
    ],
    checkedItems: ['desc', 'volume']
  }
])

const handleCheckboxChange = () => {
  console.log('已选中的内容：', checkedData.value)
}

// 实时获取已选中的内容（带 label）
const checkedData = computed(() => {
  return groups.map(group => ({
    name: group.name,
    label: group.label,
    selected: group.items
      .filter(item => group.checkedItems.includes(item.value))
      .map(item => ({
        value: item.value,
        label: item.label
      }))
  }))
})
</script>

<style lang="less" scoped>
.display-setting {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;

  .group {
    border-right: 1px solid #ebeef5;
    padding-right: 20px;

    &:last-child {
      border-right: none;
    }

    .label {
      font-weight: 500;
      margin-bottom: 6px;
      text-align: left;
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;

      .checkbox-item {
        margin-bottom: 8px;
      }
    }
  }
}
</style>