/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-09 12:04:38
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-07-26 11:28:07
 * @FilePath: /neue-cloud-vdi/src/common/types/login.ts
 */
// 接口请求列表通用参数配置
export type RquestParams = {
  [key: string]: string | number | Array<string | number>
}

export type LoginParams = {
  username: string
  password: string
  code?: string
  uid?: string
  grant_type?: string
  scope?: string
}

export type LogoutParams = {
  token: string
}
