## 组件封装规范

### 文件夹规范
1. 文件夹命名小写短横线拼接
2. 文件夹以 __ny-*__ 开始
3. 文件夹下面必须有一个入口文件 __index.(vue | js | ts)__
4. 文件夹里面可以新增components文件夹作为局部使用的组件

### 组件开发规范

组件开发以element-plus基础框架开发

1. 所有 element 属性需要透传
2. 使用ts开发，所有额外新增暴露外部调用的属性需要严格定义类型并注明使用参数
3. 组件内部非必要不新增组件独立样式，所有要修改的样式优先查阅组件css变量修改组件样式 (新增样式过多影响后期功能拓展和修改)
4. 组件内部如需要添加组件样式需要scope 或者 添加class ny-*
5. 组件内完善index.d.ts和global.d.ts的属性声明（vscode 这类编辑器能做到代码提示输入type 自动补全 value）

### 封装实例button

统一导出 index.ts

``` javascript
// el属性支持透传使用 动态绑定的属性支持编辑器补全
<NyButton type="primary"  @click="handleClick">点击我</NyButton>
     

