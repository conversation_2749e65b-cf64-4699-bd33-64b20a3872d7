import { createApp } from 'vue'
import ElementPlus, { ElMessage } from 'element-plus'
import { createPinia } from 'pinia'
import router from '@/router/index'
import { key, store } from '@/store'
import App from './App.vue'
import styleImport from '@/utils/style-import'
// import 'normalize.css';
import '@/style/basic.styl'
import '@/assets/css/base.less'
import '@/assets/css/common.less'
// 组件化的的样式变量文件
import '@/assets/css/component.less'
import 'element-plus/dist/index.css'
import '@/component-design/theme.less'
import '@/index.css'
import '@/assets/css/fonts/iconfont.css';
import * as service from '@/service'
import storage from '@/utils/storage.js'
import EventBus from '@/utils/event-bus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import components from '@/component-design'

const app = createApp(App)

// 事件监听
EventBus.on('ROUTER_CHANGE', (routeParems: any) => {
  const currentRoute = router.currentRoute.value
  // 防止重复跳转
  if (currentRoute.path === routeParems.path) return

  const routeOption = { path: routeParems.path, query: currentRoute.query }
  if (routeParems.path === '/login') {
    routeOption.query = { redirect: currentRoute.fullPath }
  }
  router.push(routeOption)
})

Object.entries(components).forEach(([name, component]) => {
  app.component(name, component);
});
app.config.globalProperties.$service = service
app.config.globalProperties.$storage = storage
app.config.globalProperties.$message = ElMessage

app.use(createPinia())
styleImport(app)
  .use(ElementPlus, { locale: zhCn })
  .use(router)
  .use(store, key)
  .mount('#app')
