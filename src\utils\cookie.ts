/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-23 15:08:30
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-08-13 18:03:10
 * @FilePath: /neue-cloud-vdi/src/utils/cookie.ts
 */

const DOMAIN = '.neuetech.cn'
/**
 * 获取所有cookie
 * @returns {Object} 所有cookie的键值对对象
 */
export function getCookies(): { [key: string]: string } {
  const cookies = document.cookie
  const cookieArray = cookies.split(';')
  const cookieObj: { [key: string]: string } = {}

  cookieArray.forEach((cookie) => {
    const parts = cookie.split('=')
    cookieObj[parts[0].trim()] = decodeURIComponent(parts[1])
  })

  return cookieObj
}

/**
 * 获取指定名称的cookie
 * @param {string} name cookie的名称
 * @returns {string|null} cookie的值或null（如果未找到）
 */
export function getCookie(name: string): string | null {
  const cookies = document.cookie.split(';')
  const len = cookies.length
  if (!len || cookies[0] === '') {
    return null
  }
  for (let i = len - 1; i >= 0; i--) {
    const cookie = cookies[i].trim().split('=')
    if (decodeURIComponent(cookie[0]) === name) {
      return decodeURIComponent(cookie[1])
    }
  }
  return null
}

/**
 * 设置cookie
 * @param {string} name cookie的名称
 * @param {string} value cookie的值
 * @param {number} days 过期时间（天数）
 */
export function setCookie(name: string, value: string, seconds: number): void {
  let expires = ''
  if (seconds) {
    const date = new Date()
    date.setTime(date.getTime() + seconds * 1000)
    expires = '; expires=' + date.toUTCString()
  }
  document.cookie = `${name}=${encodeURIComponent(
    value
  )}${expires}; domain=${DOMAIN}; path=/`
}

/**
 * 清除cookie
 * @param {string} name cookie的名称
 */
export function deleteCookie(name: string): void {
  document.cookie = `${name}=; domain=${DOMAIN}; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/`
}
