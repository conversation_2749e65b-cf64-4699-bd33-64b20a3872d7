<template>
  <div class="max-h-[590px] flex justify-between">
    <div class="flex-1 mr-3 rounded-[8px]" style="border: 1px solid #e2e8f0">
      <div class="header-text bg-[#F7F8FA] leading-[38px] pl-4 !text-[14px]">
        可选择用户
      </div>
      <div class="px-6 pb-3">
        <el-input
          class="w-full my-[10px]"
          v-model="inputLeft"
          placeholder="请输入用户名称"
          :suffix-icon="Search"
        />
        <div>
          <el-scrollbar height="400px">
            <el-checkbox-group v-model="checkList">
              <div
                class="py-1 px-3 flex items-center h-[32px]"
                v-for="item in userSelect"
                :key="item.ncid"
              >
                <el-checkbox :value="item.ncid" :disabled="getEnable(item.ncid)">
                  <div class="flex items-center">
                    <span
                      class="w-[22px] h-[22px] rounded-[22px] flex-c-c bg-[#92A6D4] ml-1 mr-2"
                    >
                      <NeIcon
                        type="icon-general-user"
                        class="text-[#fff] text-[12px]"
                      />
                    </span>
                    <span class="text-[#1E293B]">{{ item.name }}</span>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <div class="flex-1 ml-3 rounded-[8px]" style="border: 1px solid #e2e8f0">
      <div class="header-text bg-[#F7F8FA] leading-[38px] pl-4 !text-[14px]">
        已选择
      </div>
      <div class="px-6 pb-3 pt-[10px]">
        <el-scrollbar height="442px">
          <div
            class="py-1 px-1 flex items-center h-[32px] justify-between"
            v-for="item in resData"
            :key="item.ncid"
          >
            <div class="flex items-center">
              <span class="w-[22px] h-[22px] rounded-[22px] flex-c-c bg-[#92A6D4] mr-2">
                <NeIcon type="icon-general-user" class="text-[#fff] text-[12px]" />
              </span>
              <span class="text-[#1E293B]">{{ item.name }}</span>
            </div>
            <NeIcon
              type="icon-tips-close"
              class="cursor-pointer"
              @click="removeUser(item.ncid)"
            />
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import actions from '@/service/actions'
import { cloneDeep, trim } from 'lodash-es'
import storage from '@/utils/storage'
interface UserProps {
  name: string
  ncid: string
}
// 定义组件 props
const props = defineProps<{
  ncid: string
}>()
let inputLeft = ref()
let checkList = ref<UserProps['ncid'][]>([])
let userList = ref<UserProps[]>()
let userSelect = ref<UserProps[]>()
let hasUser = ref()
let getEnable = (ncid: string): boolean => {
  return hasUser.value?.includes(ncid)
}
watch(inputLeft, (val) => {
  userSelect.value = cloneDeep(
    userList.value?.filter((item) => !trim(val) || item.name.indexOf(val) != -1)
  )
})
watch(checkList, () => {})
let resData = computed((): UserProps[] | undefined => {
  return userList.value?.filter((item) => checkList.value.includes(item.ncid))
})
let removeUser = (ncid: string) => {
  checkList.value = checkList.value.filter((item) => item !== ncid)
}
let getActionsList = async (spaceId: string) => {
  let res = await actions.postPlt0UserActionsList({
    pageParams: {
      limit: 10,
      page: 0
    },
    condition: {
      logic: 'AND',
      ignoreCase: true,
      statements: [
        {
          field: 'account.ncid',
          value: [storage.get('accountNcid')],
          operator: '='
        }
      ]
    }
  })
  userList.value = res.data
  userSelect.value = res.data
  actions
    .postUserDCSRelations({
      pageParams: {
        limit: 99999,
        page: 0
      },
      condition: {
        logic: 'AND',
        ignoreCase: true,
        statements: [
          {
            field: 'target.ncid',
            value: [spaceId],
            operator: '='
          }
        ]
      }
    })
    .then((res) => {
      let ids = res.data.map((item: { source: { ncid: any } }) => item.source.ncid)
      hasUser.value = ids
    })
}
// 监听 ncid 变化
watch(
  () => props.ncid,
  (newNcid, oldNcid) => {
    if (newNcid && newNcid !== oldNcid) {
      getActionsList(newNcid)
    }
  },
  { immediate: true }
) // immediate: true 确保组件创建时立即执行一次

onMounted(() => {
  if (props.ncid) {
    // getActionsList(props.ncid)
  }
})
defineExpose({
  getData: () => {
    return resData.value
  }
})
</script>
