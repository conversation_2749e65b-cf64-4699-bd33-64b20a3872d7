/*
 * @Author: wangyan-judy
 * @Description:
 * @Date: 2024-07-12 10:20:23
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2024-08-20 17:40:49
 * @FilePath: /neue-cloud-vdi/src/common/filters/file.ts
 */
import { IconMap } from '@/common/types/console/file'
import Doc from '@/assets/images/fileIcon/doc.svg'
import Excel from '@/assets/images/fileIcon/excel.svg'
import PPT from '@/assets/images/fileIcon/ppt.svg'
import TXT from '@/assets/images/fileIcon/txt.svg'
import PICTURE from '@/assets/images/fileIcon/picture.svg'
import PDF from '@/assets/images/fileIcon/pdf.svg'
import VIDEO from '@/assets/images/fileIcon/video.svg'
import MUSIC from '@/assets/images/fileIcon/music.svg'
import ZIP from '@/assets/images/fileIcon/zip.svg'
import RAR from '@/assets/images/fileIcon/rar.svg'
import MF from '@/assets/images/fileIcon/mf1.svg'
import DWG from '@/assets/images/fileIcon/dwg.svg'
import DXF from '@/assets/images/fileIcon/dxf.svg'
import PS from '@/assets/images/fileIcon/ps.svg'
import AI from '@/assets/images/fileIcon/ai.svg'
import SKETCH from '@/assets/images/fileIcon/sketch.svg'
import TD from '@/assets/images/fileIcon/3D.svg'
import RP from '@/assets/images/fileIcon/rp.svg'
import FOLDER from '@/assets/images/fileIcon/folder.svg'
import OTHER from '@/assets/images/fileIcon/other.svg'

export const test: IconMap = {
  doc: 'doc',
  docx: 'doc'
}
export const iconMap: IconMap = {
  doc: Doc,
  docx: Doc,
  xls: Excel,
  xlsx: Excel,
  csv: Excel,
  ppt: PPT,
  pptx: PPT,
  txt: TXT,
  pdf: PDF,
  svg: PICTURE,
  png: PICTURE,
  jpg: PICTURE,
  jpeg: PICTURE,
  gif: PICTURE,
  tif: PICTURE,
  bmp: PICTURE,
  mp4: VIDEO,
  avi: VIDEO,
  mov: VIDEO,
  mp3: MUSIC,
  zip: ZIP,
  rar: RAR,
  // 7z: 'pdf',
  mf1: MF,
  dwg: DWG,
  dxf: DXF,
  psd: PS,
  psb: PS,
  ai: AI,
  esp: AI,
  sketch: SKETCH,
  obj: TD,
  fbx: TD,
  max: TD,
  rp: RP,
  folder: FOLDER,
  other: OTHER
}

export const getIconFun = (ext: string) => {
  if (iconMap[`${ext}`]) {
    return iconMap[`${ext}`]
    // return new URL(`../../assets/images/fileIcon/${iconMap[`${ext}`]}.svg`, import.meta.url).href
  }
  return iconMap.other
  // return new URL(`../../assets/images/fileIcon/${iconMap.other}.svg`, import.meta.url).href
}
