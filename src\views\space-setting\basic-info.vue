<template>
  <Page title="基本信息" :onClose="handleClose">
    <div class="flex items-center justify-center">
      <div class="w-[386px]">
        <NeDynamicForm :schema="formSchema" :disabled="isMember" :rules="rules" ref="ruleFormRef"
          @onValuesChange="handleChange">
          <el-button type="primary" @click="submitForm" :disabled="btnDisable" v-if="isMember">
            保存
          </el-button>
        </NeDynamicForm>
      </div>
    </div>
  </Page>
</template>

<script setup lang="ts">
import Page from '@/components/layout/page.vue'
import NeDynamicForm, {
  DynamicFormExposeProps,
  SchemaItemProps
} from '@/component-design/ne-dynamic-form/index.vue' // 你实际保存的路径
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage, FormRules } from 'element-plus'
import actions from '@/service/actions'
import storage from '@/utils/storage'
import { isObjectValuesEmpty } from '@/utils'

// 定义组件 props
const props = defineProps<{
  ncid: string
}>()
let dataInfo = ref()

const emit = defineEmits(['onClose'])
let handleClose = () => {
  emit('onClose')
  return false
}
let formSchema = ref<SchemaItemProps[]>([
  {
    type: 'input',
    label: '空间名称',
    prop: 'name',
    placeholder: '请输入'
  },
  {
    type: 'select',
    label: '关联项目',
    prop: 'projectSpace',
    placeholder: '请选择',
    options: []
  },
  {
    type: 'textarea',
    label: '描述',
    placeholder: '请输入',
    prop: 'description',
    maxlength: 30,
    autosize: { minRows: 4 },
    showWordLimit: true
  }
])
let postProjectSpaceListFn = async () => {
  let res = await actions.postProjectSpaceList({
    pageParams: {
      limit: 9999999,
      page: 0
    }
  })
  formSchema.value[1].options = res.data.map((item: { name: any; ncid: any }) => ({ label: item.name, value: item.ncid }))
}
// 获取空间详情
const getSpaceDetail = async (id: string) => {
  if (!id) return

  try {
    // 使用正确的 API 方法获取空间详情
    const res = await actions.getDesignCollaborationSpaces({ id })
    // 填充表单数据
    if (res && res.data) {
      dataInfo.value = res.data
      // 直接修改表单模型中的值
      formSchema.value.forEach(item => {
        if (item.prop === 'name') {
          item.modelValue = res.data.name || ''
        } else if (item.prop === 'description') {
          item.modelValue = res.data.description || ''
        } else if (item.prop === 'projectSpace') {
          item.modelValue = res.data.projectSpace?.ncid || ''
        }
      })

      // 手动更新表单值
      const formData = {
        name: res.data.name || '',
        description: res.data.description || '',
        projectSpace: id
      }
      // 触发表单值变化，使按钮可用
      handleChange(formData)
    }
  } catch (error) {
    ElMessage.error('获取空间详情失败')
  }
}

// 监听 ncid 变化
watch(() => props.ncid, (newNcid, oldNcid) => {
  if (newNcid && newNcid !== oldNcid) {
    getSpaceDetail(newNcid)
  }
}, { immediate: true }) // immediate: true 确保组件创建时立即执行一次

onMounted(() => {
  // 获取项目空间列表
  postProjectSpaceListFn()
})
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入空间名称', trigger: 'change' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5a-zA-Z0-9_]*$/, message: '名称只能以中文或字母开头，后续可包含中文、字母、数字、下划线', trigger: 'blur' }
  ]
})
let isMember = computed(() => {
  return storage.get('uid') === dataInfo.value?.owner.ncid
})
const ruleFormRef = ref<DynamicFormExposeProps>()
const btnDisable = ref(true)
let handleChange = (val: any) => {
  btnDisable.value = isObjectValuesEmpty(val)
}
const submitForm = async () => {
  if (!ruleFormRef.value) return
  let { formIns, getFormData } = ruleFormRef.value
  await formIns?.validate((valid, fields) => {
    if (valid) {
      let value = getFormData()
      let { projectSpace, ...otherData } = value
      actions
        .putDesignCollaborationSpaces({
          id: props.ncid,
          ...otherData,
          projectSpace: { ncid: projectSpace },
        })
        .then((res) => {
          ElMessage.success('修改成功')
        })
    } else {
    }
  })
}
</script>
