<template>
  <el-dialog
    id="dialog-3dview"
    v-model="props.visible"
    :style="{
      width: '1024px',
      height: '678px'
    }"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
  <template #header="{ close, titleId, titleClass }">
    <div :id="titleId" :class="`deploy-title ${titleClass}`">
      3D视图
    </div>
    <div class="deploy-close" @click="close">
      <svg-icon name="icon-close" />
    </div>
  </template>
  <template #default>
    <div class="dialog-content">
      <Cad3d :ncid="props.ncid" />
    </div>
  </template>
  </el-dialog>
</template>
<script setup lang="ts">
import Cad3d from '@/components/cdp/cad-3d.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  ncid: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible'])

const handleClose = () => {
  // 关闭操作
  emit('update:visible', false)
}

</script>