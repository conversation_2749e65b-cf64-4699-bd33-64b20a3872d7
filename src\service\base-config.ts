/*
 * @Author: wangyan-judy
 * @Description: API base url
 * @Date: 2024-07-09 10:27:50
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-01-22 11:09:55
 * @FilePath: /neue-cloud-vdi/src/service/base-config.ts
 */

const envUrl =
  import.meta.env.VITE_GALAXY_ENV ||
  import.meta.env.VITE_BASE_URL ||
  '//dev.cloud.neuetech.cn:9080'
const webUrl = window.location.hostname
const baseConfig: any = {
  'internal.dev.cdp.neuetech.cn': '//internal.dev.cdp.neuetech.cn',
  'dev.cloud.neuetech.cn': '//dev.cloud.neuetech.cn',
  'qa.cloud.neuetech.cn': '//api.qa.cloud.neuetech.cn',
  'preprod.cloud.neuetech.cn': '//api.preprod.cloud.neuetech.cn',
  'cloud.neuetech.cn': '//api.cloud.neuetech.cn',
  'galaxy.neuetech.cn': '//api.cloud.neuetech.cn',
  'cloudpre.neuetech.cn': '//api.dev.cloud.neuetech.cn',
  'internal.galaxy.neuetech.cn': '//api-internal.cloud.neuetech.cn',
  '***********': '//***********:8088',
  '123.neuetech.cn': '',
  localhost: ''
}
// TODO: GO 转JAVA临时层级 /202505
// const baseUrl = typeof baseConfig[webUrl] !== 'undefined'? baseConfig[webUrl] +'/202505': envUrl + '/202505'
const baseUrl = '//api-internal.cloud.neuetech.cn'
const imgBaseUrl = !baseConfig[webUrl] ? '/src/assets/images' : '/assets'
export default { baseUrl, imgBaseUrl }
