/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-22 16:02:40
 * @LastEditors: wangyan-judy
 * @LastEditTime: 2025-02-08 14:33:28
 * @FilePath: /neue-cloud-vdi/src/service/access/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/service/http'
import url from './index-url'
import { CreateRole, ListRoles, UpdateRole, RoleAttachUsers, AttachUserRole, RoleGetAccounts } from './definitions'

const { post, get, put, deleteReq } = http

export default {
  //角色相关操作-创建角色
  postAccessRoles(params: CreateRole) {
    return post(url.accessRoles, params, {})
  },
  operationsListPermissions(params: any) {
    return post(url.operationsListPermissions(params.userId), params, {})
  },
  operationsList(params: ListRoles) {
    return post(url.operationsList, params, {})
  },
  //角色相关操作-角色列表
  postRolesOperationsList(params: ListRoles) {
    return post(url.rolesOperationsList, params, {})
  },
  //角色相关操作-角色详情
  getRolesRoleId(roleId: string | number) {
    return get(url.rolesRoleId(roleId), {})
  },
  //角色相关操作-更新角色
  putRolesRoleId(params: UpdateRole, roleId: string | number) {
    return put(url.rolesRoleId(roleId), params, {})
  },
  //角色相关操作-删除角色
  delRolesRoleId(roleId: string | number) {
    return deleteReq(url.rolesRoleId(roleId), {})
  },
  // 角色相关操作-角色绑定用户
  postOperationsAttachUsers(roleId: string | number, params: RoleAttachUsers) {
    return post(url.operationsAttachUsers(roleId), params, {})
  },
  // 角色相关操作-角色解绑用户
  postOperationsDetachUsers(roleId: string | number, params: RoleAttachUsers) {
    return post(url.operationsDetachUsers(roleId), params, {})
  },
  //角色相关操作-角色关联用户列表
  postUsersOperationsList(roleId: string | number, params: RoleGetAccounts) {
    return post(url.usersOperationsList(roleId), params, {})
  },
  //角色相关操作-获取角色可分配用户列表
  postOperationsListAssignable(roleId: string | number, params: RoleGetAccounts) {
    return post(url.operationsListAssignable(roleId), params, {})
  },
  // //角色相关操作-用户绑定角色
  // postUserIdRoles (userId: string | number, params: AttachUserRole) {
  //   return post(url.userIdRoles(userId), params, {})
  // },
  // //角色相关操作-用户解绑角色
  // delUserIdRoles (userId: string | number, params: AttachUserRole) {
  //   return deleteReq(url.userIdRoles(userId), params)
  // },
  // //用户-角色相关操作-用户角色列表
  // postOperationsListRoles (userId: string | number) {
  //   return post(url.operationsListRoles(userId), {})
  // },
}
