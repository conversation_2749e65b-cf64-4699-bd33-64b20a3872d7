<template>
  <div class="exploded-panel">

    <div style="width: 16px; height: 16px" class="icon-left">
      <img :src="explodedLeft" />
    </div>

    <!-- 滑块 -->
    <el-slider v-model="sliderValue" :min="0" :max="100" @change="onSliderChange" class="custom-slider"/>

    <div style="width: 16px; height: 16px" class="icon-right">
      <img :src="explodedRight" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import explodedRight from '@/assets/images/ufrEngine/explodedRight.svg'
import explodedLeft from '@/assets/images/ufrEngine/explodedLeft.svg'

// 滑块的值
const sliderValue = ref(45);

// 滑块值改变时的处理函数
const onSliderChange = (value) => {
  console.log('Slider value changed:', value);
};

</script>

<style lang="less" scoped>
.exploded-panel {
  display: flex;
  align-items: center;
}


.icon-left {
  margin-right: 16px;
}

.icon-right {
  margin-left: 16px;
}

:deep(.custom-slider .el-slider__runway) {
  height: 12px;
  background-color: #ccc;
  border-radius: 6px; /* 圆角设置为高度的一半 */
}

:deep(.custom-slider .el-slider__bar) {
  height: 12px; /* 与轨道高度一致 */
  // background-color: #409eff; /* 默认蓝色 */
  border-radius: 6px; /* 圆角设置为高度的一半 */
}

:deep(.custom-slider .el-slider__button) {
  width: 16px;
  height: 16px;
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 50%; /* 圆形按钮 */
  top: -6px; /* 调整 top 值，使其垂直居中 */
}
</style>