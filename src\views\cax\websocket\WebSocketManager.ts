import { WebSocketOptions, WebSocketEventCallback } from './type';
import { ElMessage } from 'element-plus';

export class WebSocketManager {
  private socket!: WebSocket;
  private options: WebSocketOptions;// WebSocket配置
  private reconnectAttempts = 0;// 重连次数
  private heartbeatTimer: any = null;// 心跳定时器
  private reconnectTimer: any = null;// 重连定时器
  private messageQueue: string[] = [];// 消息队列
  private isClose = false;// 是否关闭
  private listeners: Record<string, WebSocketEventCallback[]> = {};// 事件监听器

  constructor(options: WebSocketOptions) {
    this.options = {
      heartbeatInterval: 10000,// 心跳间隔
      reconnectInterval: 1000,// 重连间隔
      maxReconnectAttempts: 5,// 最大重连次数
      ...options,
    };
    // this.connect();
  }

  public connect(url?: string) {
    if (url) {
      this.options.url = url;
    }
    this.socket = new WebSocket(this.options.url);
    // 开始连接
    this.socket.onopen = () => {
      this.options.onOpen?.();
      this.reconnectAttempts = 0;
      this.flushMessageQueue();
      this.startHeartbeat();
    };
    // 处理消息
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data?.trackId === 'ping') return;
      this.options.onMessage?.(data);
      this.emit(Object.keys(data.body)[0], data);
    };
    // 处理错误
    this.socket.onerror = (err) => {
        this.options.onError?.(err);
    };
    // 处理关闭事件
    // 关闭连接时，停止心跳并尝试重连
    this.socket.onclose = () => {
      this.options.onClose?.();
      this.stopHeartbeat();
      this.reconnect();
    };
  }
  // 重连逻辑
  private reconnect() {
    const max = this.options.maxReconnectAttempts ?? 5;
    if (this.reconnectAttempts >= max) {
      this.options.onReconnectFailed?.(); 
      return;
    }
    this.reconnectAttempts++;
    // 清除之前的重连定时器
    clearTimeout(this.reconnectTimer);
    if(this.isClose){
      this.isClose = false;
      return;
    }
    this.reconnectTimer=setTimeout(() => {
      this.connect();
    }, this.options.reconnectInterval);
  }
  // 心跳逻辑
  private startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(JSON.stringify({ trackId: 'ping' }));
      }
    }, this.options.heartbeatInterval);
  }
  // 停止心跳
  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
  // 清空消息队列，发送未发送的消息
  private flushMessageQueue() {
    while (this.messageQueue.length) {
      const msg = this.messageQueue.shift();
      if (msg && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(msg);
      }
    }
  }
  // 发送消息
  public send(data: any) {
    const msg = JSON.stringify(data);
    if (this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(msg);
    } else {
      this.messageQueue.push(msg);
    }
  }
  // 监听事件，event为事件名称，callback为回调函数
  public on(event: string, callback: WebSocketEventCallback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }
  // 分发事件，event为事件名称，data为事件数据
  private emit(event: string, data: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach((cb) => cb(data));
    }
  }

  // 关闭连接
  public close() {
    this.stopHeartbeat();
    this.socket.close();
    clearTimeout(this.reconnectTimer);
    this.isClose = true;
  }
}
