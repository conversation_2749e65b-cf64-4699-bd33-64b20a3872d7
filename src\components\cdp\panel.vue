<template>
  <el-popover
    popper-class="popover-pannel"
    placement="bottom"
    trigger="click"
    :visible.sync="showPopover"
    :popper-append-to-body="true"
  >
    <template #reference>
      <i class="iconfont icon-general-application" @click="handleShowPanel"></i>
    </template>
    <div class="popover-content">
      <div class="popover-header">
        <div class="popover-header-left" @click="handleHome">
          <i class="iconfont icon-interactive-button-wait"></i>
          <span>返回首页</span>
        </div>
        <i class="iconfont icon-close" @click="handleClose"></i>
      </div>
      <div class="popover-body">
        <el-collapse class="custom-collapse" v-model="activeNames">
          <el-collapse-item name="1">
            <template #title="{ isActive }">
              <div :class="['title-wrapper', { 'is-active': isActive }]">
                设计工具
              </div>
            </template>
            <div class="popover-body-content">
              <div class="popover-card popover-card-disabled">
                <div class="popover-card-icon">
                  <img src="@/assets/images/nav/icon-neuecax.png" width="64" alt="">
                </div>
                <div class="popover-cart-title">Neue CAX</div>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template #title="{ isActive }">
              <div :class="['title-wrapper', { 'is-active': isActive }]">
                PDM管理
              </div>
            </template>
            <div class="popover-body-content">
              <div class="popover-card" @click="goDesignPart">
                <div class="popover-card-icon">
                  <img src="@/assets/images/nav/icon-cadbom.png" width="64" alt="">
                </div>
                <div class="popover-cart-title">设计零部件</div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const activeNames = ref(['1', '2'])
const showPopover = ref(false)

const handleShowPanel = () => {
  showPopover.value = true
}
const handleClose = () => {
  showPopover.value = false
}
const handleHome = () => {
  router.push({ path: '/home' })
  setTimeout(() => {
    handleClose()
  }, 100)
}

const goDesignPart = () => {
  router.push({ path: '/design-part' })
  setTimeout(() => {
    handleClose()
  }, 100)
}

</script>
<style lang="less">
@import url("//at.alicdn.com/t/c/font_4913835_aexsq5x2ok6.css");
.popover-pannel {
  min-width:440px!important;
  height: 50%;
  min-height: 480px;
  .popover-header {
    display: flex;
    justify-content: space-between;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 14px;
    .popover-header-left {
      display: flex;
      align-items: center;
      font-size: @text-2;
      color: @link-6;
      cursor: pointer;
    }
    .icon-close {
      cursor: pointer;
    }
  }
  .popover-content {
    width: 416px;
    padding: 8px;
    height: 50%;
    background-color: #fff;
    border-radius: 8px;
    .el-popper__arrow {
      display: none !important;
    }
  }
  .popover-body-content {
    display: flex;
    .popover-card {
      width: 64px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-right: 8px;
      &:nth-child(n+1) {
        margin-left: 12px;
      }
      &.popover-card-disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
    .popover-card-icon {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      img {
        width: 64px;
        height: 64px;
      }
    }
    .popover-cart-title {
      cursor: pointer;
      margin-top: 10px;
      font-size: @text-0;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 8px;
    }
  }
  .custom-collapse {
    .el-collapse-item {
      border: 0 none;

    }
    .el-collapse-item__header {
      display: flex;
      justify-content: flex-start; /* 确保箭头在左侧 */
      align-items: center;
      border: 0 none;
    }
    .el-collapse-item__wrap {
      border-bottom: 0 none;
    }
    .title-wrapper {
      order: 1;
    }
    .el-collapse-item__arrow {
      order: -1;
      margin-left: 0px;
      font-family: "iconfont" !important;
      font-size: 16px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: rgba(0, 0, 0, 0.85);
      &:before {
        content: "\e724";
      }
    }
  }
}
.iconfont.icon-general-application {
  width: 28px;
  height: 28px;
  font-size: 24px;
  color: @text-color-4;
  margin-right: 12px;
  cursor: pointer;
}
// .pannel {
.el-popper__arrow {
  display: none !important;
}

</style>
