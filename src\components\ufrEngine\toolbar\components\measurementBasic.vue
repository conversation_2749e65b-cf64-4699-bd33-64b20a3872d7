<template>
  <div class="basic-setting">
    <!-- 对象标签和按钮组 -->
    <div class="setting-item">
      <div class="label">对象</div>
      <div class="mt-2 icon-group">
        <el-tooltip
          v-for="item in icons"
          :key="item.value"
          :content="item.tooltip"
          placement="top"
          effect="dark"
        >
          <i
            :class="['iconfont', item.icon, { active: selected === item.value }]"
            @click="selected = item.value"
          ></i>
        </el-tooltip>
      </div>
    </div>

    <!-- 保持显示最后一个 -->
    <div class="setting-item">
      <div class="label">保持显示最后一个</div>
      <el-switch v-model="keepLast" class="mt-2" style="--el-switch-on-color: #1856EB; --el-switch-off-color: #C9CDD6" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const keepLast = ref(true)
const selected = ref('all') // 默认选中 all

// 图标配置（KV格式）
const icons = [
  { value: 'all', icon: 'icon-3D-measure-all', tooltip: '任意几何' },
  { value: 'point', icon: 'icon-3D-measure-point', tooltip: '点' },
  { value: 'face', icon: 'icon-3D-measure-face', tooltip: '线' },
  { value: 'surface', icon: 'icon-3D-measure-surface', tooltip: '面' },
  { value: 'volume', icon: 'icon-3D-measure-volume', tooltip: '体' },
  { value: 'arc', icon: 'icon-3D-measure-arc', tooltip: '弧' }
]
</script>

<style lang="less" scoped>
.basic-setting {
  .setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .label {
      margin-right: 16px;
      font-weight: @font-weight-2;
      color: @text-color-2;
    }

    .icon-group {
      display: flex;
      gap: 16px; // 设置图标之间的间距

      i.iconfont {
        font-size: 24px;
        cursor: pointer;
        color: @text-color-3;

        &.active {
          color: @link-6; // 蓝色
        }
      }
    }

    .mt-2 {
      margin-left: auto;
      font-size: 24px;
    }
  }
}
</style>